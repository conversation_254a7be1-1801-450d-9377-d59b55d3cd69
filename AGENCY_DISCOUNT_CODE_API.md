# 企业折扣码查询接口文档

## 接口概述

企业折扣码查询接口用于检查企业渠道下单码生成状态，如果已生成则直接返回图片URL，如果未生成则先自动生成后返回图片URL。

## 接口定义

### 基本信息
- **接口名称**: 查询企业折扣码
- **请求方式**: GET 或 POST
- **接口路径**: `/api/queryAgencyDiscountCode`
- **Content-Type**: `application/json` (POST) 或 `application/x-www-form-urlencoded` (GET)

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agencyId | String | 是 | 企业ID |

### 响应格式

```json
{
  "code": "0",
  "message": "查询企业折扣码成功",
  "data": "https://example.com/discount-code-image.png"
}
```

### 响应参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | String | 响应码，"0"表示成功，其他表示失败 |
| message | String | 响应消息 |
| data | String | 图片URL（成功时返回） |

## 业务逻辑

### 1. 检查企业渠道下单码生成状态
- 调用会员系统接口查询指定企业（agencyId）是否已生成过渠道下单码

### 2. 已生成场景
- 如果企业已生成过渠道下单码，直接从返回数据中提取折扣码图片URL
- 支持多种可能的图片URL字段名：`imageUrl`、`qrCodeUrl`、`codeImageUrl`、`discountCodeUrl`、`pictureUrl`

### 3. 未生成场景
- 如果企业未生成过渠道下单码，自动执行生成流程：
  1. **获取用车规则ID**：
     - 优先获取企业的默认用车规则ID
     - 如果没有默认规则，查询用车规则列表获取最新的一个规则ID
  2. **生成渠道下单码**：
     - `secondAppKey`: 固定值 "aaa"
     - `secondAppKeyName`: "企业折扣码"
     - `agencyRoleId`: 获取到的用车规则ID
     - `agencyRoleName`: "默认用车规则"
     - `operator`: 系统自动生成操作人
  3. **获取图片URL**：
     - 生成成功后重新查询关联关系获取图片URL
  4. **记录操作日志**：
     - 记录自动生成企业折扣码的操作日志

## 接口调用示例

### GET 请求示例

```bash
curl -X GET "http://localhost:8080/api/queryAgencyDiscountCode?agencyId=AGENCY001"
```

### POST 请求示例

```bash
curl -X POST "http://localhost:8080/api/queryAgencyDiscountCode" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "agencyId=AGENCY001"
```

### JavaScript 调用示例

```javascript
// GET 请求
fetch('/api/queryAgencyDiscountCode?agencyId=AGENCY001')
  .then(response => response.json())
  .then(data => {
    if (data.code === '0') {
      console.log('折扣码图片URL:', data.data);
    } else {
      console.error('查询失败:', data.message);
    }
  });

// POST 请求
fetch('/api/queryAgencyDiscountCode', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: 'agencyId=AGENCY001'
})
  .then(response => response.json())
  .then(data => {
    if (data.code === '0') {
      console.log('折扣码图片URL:', data.data);
    } else {
      console.error('查询失败:', data.message);
    }
  });
```

## 响应示例

### 成功响应（已生成）

```json
{
  "code": "0",
  "message": "查询企业折扣码成功",
  "data": "https://csms-st.evcard.vip/images/discount-codes/AGENCY001_discount_code.png"
}
```

### 成功响应（自动生成）

```json
{
  "code": "0",
  "message": "生成企业折扣码成功",
  "data": "https://csms-st.evcard.vip/images/discount-codes/AGENCY001_discount_code.png"
}
```

### 失败响应示例

```json
{
  "code": "-1",
  "message": "企业ID不能为空"
}
```

```json
{
  "code": "-1",
  "message": "未找到企业的用车规则，无法生成折扣码"
}
```

```json
{
  "code": "-1",
  "message": "生成企业折扣码失败：企业不存在"
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 成功 | 操作成功 |
| -1 | 企业ID不能为空 | 请求参数校验失败 |
| -1 | 未找到企业的用车规则，无法生成折扣码 | 企业没有配置用车规则 |
| -1 | 企业折扣码图片URL不存在 | 渠道下单码已生成但图片URL为空 |
| -1 | 生成企业折扣码失败：[具体错误信息] | 调用会员系统接口失败 |
| -1 | 查询企业折扣码异常：[异常信息] | 系统异常 |

## 技术实现细节

### 1. 依赖服务
- **会员系统接口**：`IMembershipAgencyService`
  - `queryAgencySecondAppKeyRelation`: 查询企业渠道下单码关联关系
  - `addAgencySecondAppKeyRelation`: 新增企业渠道下单码关联关系
- **用车规则服务**：`IVehicleRuleService`
  - `getDefaultVehicleRuleId`: 获取企业默认用车规则ID
- **BVM 系统接口**：
  - `url.getAgencyRoleList`: 查询企业用车规则列表

### 2. 关键配置
```properties
# BVM 系统用车规则查询接口
url.getAgencyRoleList = http://csms-st.evcard.vip/evcard-bvm/inner/getAgencyRoleList
url.getDefaultAgencyRole = http://csms-st.evcard.vip/evcard-bvm/inner/getDefaultAgencyRole
```

### 3. 图片URL提取逻辑
系统会尝试从 `AgencySecondAppKeyRelationDTO` 对象中提取图片URL，支持以下字段名：
- `imageUrl`
- `qrCodeUrl` 
- `codeImageUrl`
- `discountCodeUrl`
- `pictureUrl`

如果直接字段访问失败，会通过JSON解析的方式尝试获取。

### 4. 异常处理
- 参数校验异常
- 会员系统接口调用异常
- BVM 系统接口调用异常
- 数据解析异常
- 系统运行时异常

### 5. 日志记录
- 接口调用日志
- 业务处理日志
- 异常错误日志
- 操作记录日志

## 注意事项

1. **幂等性**：接口具有幂等性，多次调用相同参数会返回相同结果
2. **自动生成**：如果企业没有渠道下单码，系统会自动生成，无需人工干预
3. **用车规则依赖**：企业必须有用车规则才能生成折扣码
4. **图片URL有效性**：返回的图片URL应该是可访问的完整URL地址
5. **操作日志**：自动生成的操作会记录在企业操作日志中
6. **性能考虑**：接口会调用多个外部系统，响应时间可能较长

## 维护建议

1. **监控接口调用**：监控接口的成功率和响应时间
2. **检查依赖服务**：定期检查会员系统和BVM系统的可用性
3. **图片URL验证**：定期验证返回的图片URL是否可访问
4. **日志清理**：定期清理过期的操作日志
5. **异常告警**：设置异常情况的告警机制
