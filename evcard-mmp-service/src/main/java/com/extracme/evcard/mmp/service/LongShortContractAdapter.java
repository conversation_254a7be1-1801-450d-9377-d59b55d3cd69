package com.extracme.evcard.mmp.service;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.AgencyIdUtils;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.dao.AgencyInfoMapper;
import com.extracme.evcard.mmp.dao.MmpAgencyDiscountLogMapper;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.mmp.model.MmpAgencyDiscountLog;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


/**
 *
 * 长短租合同 处理类
 *
 *
 */
@Service
public class LongShortContractAdapter {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    AgencyInfoMapper agencyInfoMapper;

    @Autowired
    IAgencyService agencyService;

    @Autowired
    IMmpDiscountRuleService mmpDiscountRuleService;

    @Autowired
    MmpAgencyDiscountLogMapper mmpAgencyDiscountLogMapper;

    @Autowired
    IMmpOperationLogService operationLogService;

    @Autowired
    IMmpAgencyAccountService mmpAgencyAccountService;

    @Autowired
    LongShortContractAdapter longShortContractAdapter;

    @Resource
    IMmpFreeDepositNumberService mmpFreeDepositNumberService;

    public BaseResponse handleMsgFromLongShortContract(AgencyInfoSyncInput input){
        log.info("长租合同同步，req:{}", JSON.toJSON(input));
        BaseResponse checkBaseResponse = commonParamCheck(input);
        if (checkBaseResponse.getCode() != 0) {
            return checkBaseResponse;
        }

        int contractStatus = input.getContractStatus();
        switch (contractStatus){
            case 6:
                //处理 待生效的合同
                return handleToBeEffectiveLongShortContract(input);
            case 7:
                //处理 生效中的合同
                return handleEffectiveLongShortContract(input);
            case 8:
                //处理 失效的合同
                return handleInvalidationLongShortContract(input);
            default:
                // 暂不支持
                return new BaseResponse(-1,"暂不支持该状态的合同");
        }
    }

    /**
     * 公共参数校验
     * @param input
     * @return
     */
    private BaseResponse commonParamCheck(AgencyInfoSyncInput input) {
        if (input.getContractStatus() < 1 || input.getContractStatus() > 8) {
            return new BaseResponse(-1, "合同状态不支持");
        }
        if (StringUtils.isEmpty(input.getAgencyName())) {
            return new BaseResponse(-1, "企业名称不能为空");
        }
        if (input.getExpireTime() == null) {
            return new BaseResponse(-1, "合同失效时间不能为空");
        }
        if (input.getCreateWay() == null || input.getCreateWay() != 2) {
            return new BaseResponse(-1, "创建方式只能为政企框架合同");
        }
        if (StringUtils.isEmpty(input.getLongRentContractId())) {
            return new BaseResponse(-1, "合同编号不能为空");
        }
        if (input.getDepositGuarantor() == null || (input.getDepositGuarantor() != 1 && input.getDepositGuarantor() != 2)) {
            return new BaseResponse(-1, "押金担保方只能为企业支付或个人支付");
        }
        if (input.getOrderPayer() == null || (input.getOrderPayer() != 1 && input.getOrderPayer() != 2)) {
            return new BaseResponse(-1, "订单支付方只能为企业支付或个人支付");
        }
        if (input.getDefaultingParty() == null || (input.getDefaultingParty() != 1 && input.getDefaultingParty() != 2)) {
            return new BaseResponse(-1, "违约承担方只能为企业承担或个人承担");
        }
        if (input.getBusinessSource() == null || (input.getBusinessSource() != 1 && input.getBusinessSource() != 2 && input.getBusinessSource() != 3)) {
            return new BaseResponse(-1, "业务来源只能为政企客户、异业合作或短租门店");
        }

        String contractsName = input.getContractsName();
        if (StringUtils.isEmpty(contractsName)) {
            return new BaseResponse(-1, "联系人姓名不能为空");
        }

        // 姓名长度过长 截取20个
        if (contractsName.length() > 20) {
            input.setContractsName(contractsName.substring(0,20));
        }

        if (StringUtils.isEmpty(input.getContractsMobile())) {
            return new BaseResponse(-1, "联系人手机号不能为空");
        }
        if (StringUtils.isEmpty(input.getLicenseNo())) {
            return new BaseResponse(-1, "客户营业证件号不能为空");
        }
        if (input.getContractDiscount() == null || input.getContractDiscount() < 0 || input.getContractDiscount() > 100) {
            return new BaseResponse(-1, "合同折扣只能在0-100之间");
        }
        if (input.getBeneficiaryNumber() < 1 || input.getBeneficiaryNumber() > 50000) {
            return new BaseResponse(-1, "受益人数只能在1-50000之间");
        }
        if (input.getExemptDepositAmount() == null || input.getExemptDepositAmount().compareTo(BigDecimal.ZERO) < 0) {
            return new BaseResponse(-1, "订单免押额度不能小于0");
        }
        return new BaseResponse(0, "");
    }

    /**
     * 处理 失效的合同
     *
     * @param input
     * @return
     */
    private BaseResponse handleInvalidationLongShortContract(AgencyInfoSyncInput input) {
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(-1L);
        operatorDTO.setOperatorName("失效合同自动更新");

        List<AgencyInfo> agencyInfos = agencyInfoMapper.getAgencyInfoByLongRentContractId(input.getLongRentContractId());
        if (CollectionUtils.isEmpty(agencyInfos)) {
            log.warn("失效的合同通过时，通过长租合同号未找到企业，消息不处理，input={}",JSON.toJSONString(input));
            return new BaseResponse(0, "同步成功");
        }

        List<AgencyInfo> agencyInfoList = agencyInfoMapper.getAgencyInfoByAgencyName(input.getAgencyName());
        if (CollectionUtils.isEmpty(agencyInfoList)) {
            return new BaseResponse(0, "该企业名称不存在");
        }
        AgencyInfo agencyInfo = agencyInfoList.get(0);
        DefaultServiceRespDTO defaultServiceRespDTO = agencyService.updatePauseCooperate(agencyInfo.getAgencyId(), null, operatorDTO);
        if (defaultServiceRespDTO.getCode() < 0) {
            return new BaseResponse(defaultServiceRespDTO.getCode(), defaultServiceRespDTO.getMessage());
        }
        return new BaseResponse(0, "同步成功");
    }

    /**
     * 处理 生效中的合同
     *
     */
    private BaseResponse handleEffectiveLongShortContract(AgencyInfoSyncInput input) {
        List<AgencyInfo> agencyInfoList = agencyInfoMapper.getAgencyInfoByAgencyName(input.getAgencyName());
        if (CollectionUtils.isEmpty(agencyInfoList)) {
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("生效合同自动生成");
            try {
                String agencyId = longShortContractAdapter.createAgencyInfo(input, operatorDTO);
                if (StringUtils.isEmpty(agencyId)) {
                    return new BaseResponse(-1, "同步新增企业会员失败");
                }
                DefaultServiceRespDTO defaultServiceRespDTO = agencyService.updateStartCooperate(agencyId, null, operatorDTO);
                if (defaultServiceRespDTO.getCode() != 0) {
                    return new BaseResponse(defaultServiceRespDTO.getCode(), defaultServiceRespDTO.getMessage());
                }
                // 超级管理创建失败不影响前置动作
                BaseResponse baseResponse = longShortContractAdapter.addSuperAdminAccountInfo(agencyId, input.getContractsMobile(), input.getContractsName(), operatorDTO);
                if (baseResponse.getCode() != 0) {
                    log.error("创建超级管理员失败, agencyId:{}, mobilePhone:{}, res:{}", agencyId, input.getContractsMobile(), JSON.toJSON(baseResponse));
                }
                return new BaseResponse(0, "同步成功");
            } catch (Exception e) {
                return new BaseResponse(-1, e.getMessage());
            }
        }
        else {
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("生效合同自动更新");
            AgencyInfo agencyInfo = agencyInfoList.get(0);
            try {
                BaseResponse baseResponse = updateAgencyInfo(agencyInfo, input, operatorDTO);
                if (baseResponse.getCode() < 0) {
                    return baseResponse;
                }
                if (agencyInfo.getStatus() != 1) {
                    DefaultServiceRespDTO defaultServiceRespDTO = agencyService.updateStartCooperate(agencyInfo.getAgencyId(), null, operatorDTO);
                    if (defaultServiceRespDTO.getCode() != 0) {
                        return new BaseResponse(defaultServiceRespDTO.getCode(), defaultServiceRespDTO.getMessage());
                    }
                }
                return new BaseResponse(0, "同步成功");
            } catch (Exception e) {
                return new BaseResponse(-1, e.getMessage());
            }
        }
    }

    /**
     * 处理 待生效的合同
     *
     * @param input
     * @return
     */
    public BaseResponse handleToBeEffectiveLongShortContract(AgencyInfoSyncInput input) {
        /**
         * 1、企业用户数据存在(企业用户企业名称与框架合同客户名称一致)，
         * 1）企业状态为合作中，则不修改
         * 2）企业状态为非合作中，基于合同内容更新
         * 2、企业用户数据不存在，基于合同内容创建，合同状态为未开始
         */
        List<AgencyInfo> agencyInfoList = agencyInfoMapper.getAgencyInfoByAgencyName(input.getAgencyName());
        if (CollectionUtils.isEmpty(agencyInfoList)) {
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("待生效合同自动生成");
            try {
                String agencyId = longShortContractAdapter.createAgencyInfo(input, operatorDTO);
                if (StringUtils.isEmpty(agencyId)) {
                    return new BaseResponse(-1, "同步新增企业会员失败");
                }
                BaseResponse baseResponse = longShortContractAdapter.addSuperAdminAccountInfo(agencyId, input.getContractsMobile(), input.getContractsName(), operatorDTO);
                if (baseResponse.getCode() != 0) {
                    log.error("创建超级管理员失败, agencyId:{}, mobilePhone:{}, res:{}", agencyId, input.getContractsMobile(), JSON.toJSON(baseResponse));
                }
                return new BaseResponse(0, "同步成功");
            } catch (Exception e) {
                return new BaseResponse(-1, e.getMessage());
            }
        }
        else {
            AgencyInfo agencyInfo = agencyInfoList.get(0);
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("待生效合同自动更新");
            try {
                BaseResponse baseResponse = updateAgencyInfo(agencyInfo, input, operatorDTO);
                if (baseResponse.getCode() == 0) {
                    return new BaseResponse(0, "同步成功");
                }
                return baseResponse;
            } catch (Exception e) {
                return new BaseResponse(-1, e.getMessage());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse updateAgencyInfo(AgencyInfo agencyInfo, AgencyInfoSyncInput input, OperatorDTO operatorDTO) throws BusinessException {
        AgencyInfo oldAgencyInfo = new AgencyInfo();
        BeanCopyUtils.copyProperties(agencyInfo, oldAgencyInfo);
        buildAgencyInfoByContract(agencyInfo, input);
        // 生效中长租合同 或者 企业非合作中  更新企业信息
        if (input.getContractStatus() == 7 || agencyInfo.getStatus() != 1) {
            agencyInfo.setUpdateOperId(operatorDTO.getOperatorId());
            agencyInfo.setUpdatedUser(operatorDTO.getOperatorName());
            agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            int number = updateAgencyInfo(oldAgencyInfo, agencyInfo);
            DiscountRuleSaveDTO discountRuleSaveDTO = new DiscountRuleSaveDTO();
            discountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
            discountRuleSaveDTO.setDiscountRate(input.getContractDiscount());
            discountRuleSaveDTO.setPeakSeasonDiscountRate(input.getContractDiscount());
            discountRuleSaveDTO.setAgencyId(agencyInfo.getAgencyId());
            discountRuleSaveDTO.setDiscountType(0);
            discountRuleSaveDTO.setValidEndTime(input.getExpireTime());
            discountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
            DefaultServiceRespDTO defaultServiceRespDTO = mmpDiscountRuleService.save(discountRuleSaveDTO, null, operatorDTO);
            if (defaultServiceRespDTO.getCode() != 0) {
                log.error("长租合同同步，更新企业会员企业折扣异常，req:{}，res:{}",JSON.toJSON(discountRuleSaveDTO), JSON.toJSON(defaultServiceRespDTO));
                throw new BusinessException("更新企业会员企业折扣失败");
            }
            DiscountRuleSaveDTO personalDiscountRuleSaveDTO = new DiscountRuleSaveDTO();
            personalDiscountRuleSaveDTO.setDiscountRate(input.getContractDiscount());
            personalDiscountRuleSaveDTO.setPeakSeasonDiscountRate(input.getContractDiscount());
            personalDiscountRuleSaveDTO.setAgencyId(agencyInfo.getAgencyId());
            personalDiscountRuleSaveDTO.setDiscountType(1);
            personalDiscountRuleSaveDTO.setValidEndTime(input.getExpireTime());
            personalDiscountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
            defaultServiceRespDTO = mmpDiscountRuleService.save(personalDiscountRuleSaveDTO, null, operatorDTO);
            if (defaultServiceRespDTO.getCode() != 0) {
                log.error("长租合同同步，更新企业会员个人折扣异常，req:{}，res:{}",JSON.toJSON(personalDiscountRuleSaveDTO), JSON.toJSON(defaultServiceRespDTO));
                throw new BusinessException("更新企业会员个人折扣失败");
            }
            if (number > 0) {
                /** 新建企业会员更新redis */
                String agencyRedisKey = "agencyFreeInfo_" + agencyInfo.getAgencyId();
                JedisUtil.hset(agencyRedisKey, "pay_way", String.valueOf(agencyInfo.getPayWay().intValue()));
                OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
                operationLogSaveDTO.setOperationRemark("修改企业会员基础信息");
                operationLogSaveDTO.setOperationTime(new Date());
                operationLogSaveDTO.setAgencyId(agencyInfo.getAgencyId());
                operationLogService.save(operationLogSaveDTO, null, operatorDTO);
            }
            else {
                log.error("长租合同同步，更新企业会员异常, req:{}, res:{}", JSON.toJSON(agencyInfo), number);
                throw new BusinessException("更新企业会员异常");
            }
        } else {
            // 合同为待生效状态，合作中不可修改；合同为生效中，合作中仍修改，
            log.warn("合作中的的机构不可以修改，请检查后重新操作！input={}",JSON.toJSONString(input));
            return new BaseResponse(0, "待生效长租合同同步，合作中的的机构无需修改");
        }
        return new BaseResponse(0, "同步成功");
    }

    private int updateAgencyInfo(AgencyInfo oldAgencyInfo, AgencyInfo newAgencyInfo) {
        int number = agencyInfoMapper.updateByIdSelective(newAgencyInfo);
        //若涉及到折扣相关的字段被更新，则添加企业折扣信息变更履历
        if(ComUtil.checkColUpdated(oldAgencyInfo.getCooperateStartTime(), oldAgencyInfo.getCooperateStartTime())
                || ComUtil.checkColUpdated(oldAgencyInfo.getCooperateEndTime(), oldAgencyInfo.getCooperateEndTime())
                || ComUtil.checkColUpdated(oldAgencyInfo.getCooperateStatus(), newAgencyInfo.getCooperateStatus())
                || ComUtil.checkColUpdated(oldAgencyInfo.getStatus(), newAgencyInfo.getStatus())
                || ComUtil.checkColUpdated(oldAgencyInfo.getVehicleNo(), newAgencyInfo.getVehicleNo())
                || ComUtil.checkColUpdated(oldAgencyInfo.getDiscountId(), newAgencyInfo.getDiscountId())
                || ComUtil.checkColUpdated(oldAgencyInfo.getDiscountPersonalId(), newAgencyInfo.getDiscountPersonalId())) {
            AgencyInfo agencyInfo = agencyInfoMapper.selectById(newAgencyInfo.getAgencyId());
            MmpAgencyDiscountLog record = new MmpAgencyDiscountLog();
            BeanCopyUtils.copyProperties(agencyInfo, record);
            record.setCreateTime(new Date());
            record.setCreateOperId(newAgencyInfo.getUpdateOperId());
            record.setCreateOperName(newAgencyInfo.getUpdatedUser());
            record.setUpdateOperId(null);
            record.setUpdateOperName(null);
            record.setUpdateTime(null);
            record.setOperateType(1);
            mmpAgencyDiscountLogMapper.insertSelective(record);
        }
        return number;
    }

    @Transactional(rollbackFor = Exception.class)
    public String createAgencyInfo(AgencyInfoSyncInput input, OperatorDTO operatorDTO) throws BusinessException {
        AgencyInfo agencyInfo = new AgencyInfo();

        String maxAgencyId = agencyInfoMapper.getMaxAgencyId();
        String last2Digit = maxAgencyId.substring(maxAgencyId.length() - 2, maxAgencyId.length());
        String newAgencyId = maxAgencyId.substring(0, maxAgencyId.length() - 2) + AgencyIdUtils.getNextNodeId(last2Digit);
        agencyInfo.setAgencyId(newAgencyId);
        // 企业折扣：合同折扣，受益人数上限：合同选择
        // 先初始化，默认为100，初始化成功后再生成新规则
        Long discountId = mmpDiscountRuleService.saveInit(newAgencyId, null,operatorDTO, 100.0, 0);
        Long discountPersonId = mmpDiscountRuleService.saveInitForPerson(newAgencyId, null, operatorDTO, input.getContractDiscount());
        mmpDiscountRuleService.saveInitForPackage(newAgencyId, null, operatorDTO);
        agencyInfo.setDiscountId(discountId);
        agencyInfo.setDiscountPersonalId(discountPersonId);
        agencyInfo.setDiscountRule(1);
        agencyInfo.setAppKey("");
        agencyInfo.setOrgName("");
        //0-暂停中，1-合作中，2-未开始，新老状态并行。
        agencyInfo.setStatus(2);
        agencyInfo.setMaxUnitPrice(new BigDecimal(1));
        agencyInfo.setCooperateStatus(0);
        agencyInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        agencyInfo.setCreatedUser(operatorDTO.getOperatorName());
        agencyInfo.setCreateOperId(operatorDTO.getOperatorId());
        agencyInfo.setUpdatedUser(operatorDTO.getOperatorName());
        agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        agencyInfo.setUpdateOperId(operatorDTO.getOperatorId());
        agencyInfo.setReferrerMobile(StringUtils.isNotBlank(input.getReferrerMobile())?input.getReferrerMobile():"");
        agencyInfo.setReferrerName(StringUtils.isNotBlank(input.getReferrerName())?input.getReferrerName():"");

        buildAgencyInfoByContract(agencyInfo, input);

        int number = addAgencyInfo(agencyInfo);
        if (number > 0) {
            /** 新建企业会员更新redis */
            String agencyRedisKey = "agencyFreeInfo_" + agencyInfo.getAgencyId();
            HashMap<String, String> agencyMap = new HashMap<>();
            agencyMap.put("pay_way", String.valueOf(agencyInfo.getPayWay().intValue()));
            JedisUtil.hmset(agencyRedisKey, agencyMap);
            OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
            operationLogSaveDTO.setOperationRemark("新建企业会员");
            operationLogSaveDTO.setAgencyId(newAgencyId);
            operationLogSaveDTO.setOperationTime(new Date());
            operationLogService.save(operationLogSaveDTO, null, operatorDTO);

            /** 新增免押人数*/
            AgencyFreeDepositSaveDTO freeDepositSaveDTO = new AgencyFreeDepositSaveDTO();
            freeDepositSaveDTO.setAgencyId(newAgencyId);
            mmpFreeDepositNumberService.save(freeDepositSaveDTO, null,operatorDTO);

            // 初始化完成后，新增规则
            DiscountRuleSaveDTO discountRuleSaveDTO = new DiscountRuleSaveDTO();
            discountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
            discountRuleSaveDTO.setDiscountRate(input.getContractDiscount());
            discountRuleSaveDTO.setPeakSeasonDiscountRate(input.getContractDiscount());
            discountRuleSaveDTO.setAgencyId(agencyInfo.getAgencyId());
            discountRuleSaveDTO.setDiscountType(0);
            discountRuleSaveDTO.setValidEndTime(input.getExpireTime());
            discountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
            DefaultServiceRespDTO defaultServiceRespDTO = mmpDiscountRuleService.save(discountRuleSaveDTO, null, operatorDTO);
            if (defaultServiceRespDTO.getCode() != 0) {
                log.error("长租合同同步，更新企业会员企业折扣异常，req:{}，res:{}",JSON.toJSON(discountRuleSaveDTO), JSON.toJSON(defaultServiceRespDTO));
                throw new BusinessException("更新企业会员企业折扣失败");
            }
            DiscountRuleSaveDTO personalDiscountRuleSaveDTO = new DiscountRuleSaveDTO();
            personalDiscountRuleSaveDTO.setDiscountRate(input.getContractDiscount());
            personalDiscountRuleSaveDTO.setPeakSeasonDiscountRate(input.getContractDiscount());
            personalDiscountRuleSaveDTO.setAgencyId(agencyInfo.getAgencyId());
            personalDiscountRuleSaveDTO.setDiscountType(1);
            personalDiscountRuleSaveDTO.setValidEndTime(input.getExpireTime());
            personalDiscountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
            defaultServiceRespDTO = mmpDiscountRuleService.save(personalDiscountRuleSaveDTO, null, operatorDTO);
            if (defaultServiceRespDTO.getCode() != 0) {
                log.error("长租合同同步，更新企业会员个人折扣异常，req:{}，res:{}",JSON.toJSON(personalDiscountRuleSaveDTO), JSON.toJSON(defaultServiceRespDTO));
                throw new BusinessException("更新企业会员个人折扣失败");
            }
        }
        else {
            throw new BusinessException("企业会员创建失败");
        }
        return newAgencyId;
    }

    public BaseResponse addSuperAdminAccountInfo(String agencyId, String mobilePhone, String name, OperatorDTO operatorDTO) {
        AgencyAccountSaveDTO agencyAccountSaveDTO = new AgencyAccountSaveDTO();
        agencyAccountSaveDTO.setAgencyId(agencyId);
        agencyAccountSaveDTO.setMobilePhone(mobilePhone);
        // bvm长度限制
        if (name.length() > 7) {
            name = name.substring(0,7);
        }
        agencyAccountSaveDTO.setName(name);
        agencyAccountSaveDTO.setOperatorName(operatorDTO.getOperatorName());
        DefaultServiceRespDTO defaultServiceRespDTO = mmpAgencyAccountService.addSuperAdminAcountInfo(agencyAccountSaveDTO, null, operatorDTO);
        if (defaultServiceRespDTO.getCode() != 0) {
            log.error("长租合同同步，新增超级管理员失败，req:{},res:{}", JSON.toJSON(agencyAccountSaveDTO), JSON.toJSONString(defaultServiceRespDTO));
            return new BaseResponse(-1, "超级管理员创建失败" + defaultServiceRespDTO.getMessage());
        }
        return new BaseResponse(0, "新增超级管理员成功");
    }

    public int addAgencyInfo(AgencyInfo agencyInfo){
        int number = agencyInfoMapper.save(agencyInfo);
        MmpAgencyDiscountLog record = new MmpAgencyDiscountLog();
        BeanCopyUtils.copyProperties(agencyInfo, record);
        record.setCreateTime(new Date());
        record.setCreateOperId(agencyInfo.getCreateOperId());
        record.setCreateOperName(agencyInfo.getCreatedUser());
        record.setOperateType(0);
        mmpAgencyDiscountLogMapper.insertSelective(record);
        return number;
    }

    //根据合同创建/修改企业会员的规则
    private void buildAgencyInfoByContract(AgencyInfo agencyInfo, AgencyInfoSyncInput input) {
        agencyInfo.setAgencyName(input.getAgencyName());
        // 企业性质：外部
        agencyInfo.setOrgProperty("0");
        agencyInfo.setContact(input.getContractsName());
        agencyInfo.setMobilePhone(input.getContractsMobile());
        agencyInfo.setLicenseNo(input.getLicenseNo());
        // 结算方式：订单支付方， 企业支付：后付费 个人支付：预充值
        if (input.getOrderPayer() == 1) {
            agencyInfo.setPayWay(0.0);
        }
        else if (input.getOrderPayer() == 2) {
            agencyInfo.setPayWay(1.0);
        }
        // 月额度限制：不限制
        agencyInfo.setLineLimitMonthly(null);
        // 内循环用车：否
        agencyInfo.setInsideFlag(0);
        // 车牌限制：不限制
        agencyInfo.setVehicleNo("");
        // 用车阈值：最大在租车辆数
        agencyInfo.setVehicleThreshold((double) input.getMaxCarNumber());
        // 企业免押金额 押金担保方式为企业，则为读取合同配置订单免押额度；押金担保方式为个人，则为0
        if (input.getDepositGuarantor() == 1) {
            agencyInfo.setExemptDepositAmount(input.getExemptDepositAmount());
        }
        else if (input.getDepositGuarantor() == 2) {
            agencyInfo.setExemptDepositAmount(BigDecimal.ZERO);
        }
        //失效时间：合同有效期结束时间 新增
        agencyInfo.setExpireTime(input.getExpireTime());
        //框架合同编号：框架合同编号
        agencyInfo.setlongRentContractId(input.getLongRentContractId());
        //押金担保方：基于合同选择
        agencyInfo.setDepositGuarantor(Integer.valueOf(input.getDepositGuarantor()));
        //订单支付方 ：基于合同选择
        agencyInfo.setOrderPayer(Integer.valueOf(input.getOrderPayer()));
        //违约承担方：基于合同选择
        agencyInfo.setDefaultingParty(Integer.valueOf(input.getDefaultingParty()));
        //企业创建方式：更改为政企框架合同
        agencyInfo.setCreateWay(2);
        //业务来源：基于合同选择
        agencyInfo.setBusinessSource(Integer.valueOf(input.getBusinessSource()));
    }
}
