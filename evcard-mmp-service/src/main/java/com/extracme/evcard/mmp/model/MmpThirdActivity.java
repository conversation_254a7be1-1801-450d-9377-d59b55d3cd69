package com.extracme.evcard.mmp.model;

import com.extracme.framework.core.model.Model;

import java.math.BigDecimal;
import java.util.Date;

public class MmpThirdActivity extends Model {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.day_voucher_limit
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer dayVoucherLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.total_voucher_limit
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer totalVoucherLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.day_offer_limit
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer dayOfferLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.total_offer_limit
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer totalOfferLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.pre_total_amount
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private BigDecimal preTotalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.activity_channel_key
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String activityChannelKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.activity_channel
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String activityChannel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.e_amount_greater
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer eAmountGreater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.e_amount_less
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer eAmountLess;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.e_offer_number
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer eOfferNumber;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.period
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer period;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.countLimit
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Integer countLimit;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.imgUrl
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String imgUrl;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.activityUrl
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String activityUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.packages_id
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private Long packagesId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.brand_name
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String brandName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.official_web_address
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String officialWebAddress;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_third_activity.service_telephone
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    private String serviceTelephone;
    
    /**
     * 发券节点，0新用户审核通过 1首单支付
     */
    private Integer offerTiming = 0;
    /**
     * 订单时长，单位分钟
     */
    private Integer orderDuration;
    
    /** 
     * 引导下载弹层 0：显示 1：不显示
     */
    private Integer bootDownloadLayer;
    /** 
     * 活动规则
     */
    private String activityRules;

    /**
     * 活动主标题
     */
    private String activityTitle;

    /**
     * 活动副标题
     */
    private String activitySubtitle;
    
    /**
     * 背景颜色
     */
    private Integer backgroundColor;

    /**
     * 优惠券交易方式，0赠送 1购买
     */
    private Integer transType;
    /**
     * 购买方类别，0无  1企业 2个人
     */
    private Integer couponOwner;

    /**
     * 购买方id (企业id，会员authId)
     */
    private String ownerId;

    /**
     * 订单金额限制
     */
    private BigDecimal orderAmount;
    /**
     * 取车时段限制：开始时间
     */
    private String pickupStartTime;
    /**
     * 取车时段限制：开始时间
     */
    private String pickupEndTime;
    /**
     * 还车时段限制：结束时间
     */
    private String returnStartTime;
    /**
     * 还车时段限制：结束时间
     */
    private String returnEndTime;
    /**
     * 还车日限制，多个以逗号分隔
     */
    private String returnDaysOfWeek;

    /**
     * 订单奖励方式 0优惠券 1现金
     */
    private Integer orderRewardType;

    /**
     * 最低可提现金额，单位元
     */
    private Double withdrawAmountMin;

    /**
     * 个人偶然所得税税率，单位%
     */
    private Integer rewardTaxRate;

    /**
     * 奖励结算间隔时间，单位天，订单支付后n天结算奖励
     */
    private Integer rewardDaysLimit;

    /**
     * 现金奖励比例，单位%, 基于订单现金实付金额
     */
    private Integer rewardAmountRate;

    /**
     * 每单最高奖励金额，单位元
     */
    private Double rewardAmountMax;

    /**
     * 返现订单数限制，0不限制 1仅首单
     */
    private Integer orderNumLimit;
    /**
     * 订单支付距离认证时间，单位天
     */
    private Integer orderPayInterval;

    /**
     * 优惠券-订单金额门槛，单位元
     */
    private Double orderAmountLimit;

    /**
     * 活动海报地址-小程序端
     */
    private String posterMiniProgram;

    /**
     * 产品线限制(已被产品线大类替代)
     */
    private String rentMethods;

    /**
     * 产品线大类 1 分时 2日租, 空表示不限，多个以逗号分隔
     */
    private String rentMethodGroup;

    /**
     * 链接方式，0外部链接，1内部链接
     */
    private Integer linkType;

    /**
     * 跳转外部地址(link_type=0时)
     */
    private String linkUrl;

    /**
     * APP跳转编号(link_type=1时)，0邀请好友、1会员任务中心、2车型宝典、3积分商城、4充值E币、5每日签到、6特惠购卡
     */
    private Integer linkAppId;

    /**
     * 是否可关闭浮动图标：0可关闭，1不可关闭
     */
    private Integer closeFloatImg;

    /**
     * 使用模式
     * 使用模式 0 仅预约上门送取车辆, 空表示不限，包日租所有业务
     */
    private String useMethods;

    /**
     * 领劵方式 0 表示自动领劵, 1 表示手动领劵
     */
    private Integer couponWay;

    /**
     * 兑换码二维码图片集合 打成的zip  oss文件ur
     */
    private String qrCodeZipUrl;

    /**
     * 一码多券开关 0开闭 1打开
     */
    private Integer oneCodeDulCouponFlag;

    /**
     * 生成兑换码数量  只有oneCodeDulCouponFlag 为1 这个才有值
     */
    private Integer couponCodeNum;

    /**
     * 兑换码有效期结束时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private Date cdkExpiresTime;

    /**
     * 兑换码有效期开始时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private Date cdkStartTime;


    public Integer getOneCodeDulCouponFlag() {
        return oneCodeDulCouponFlag;
    }

    public void setOneCodeDulCouponFlag(Integer oneCodeDulCouponFlag) {
        this.oneCodeDulCouponFlag = oneCodeDulCouponFlag;
    }

    public Integer getCouponCodeNum() {
        return couponCodeNum;
    }

    public void setCouponCodeNum(Integer couponCodeNum) {
        this.couponCodeNum = couponCodeNum;
    }

    public Date getCdkExpiresTime() {
        return cdkExpiresTime;
    }

    public void setCdkExpiresTime(Date cdkExpiresTime) {
        this.cdkExpiresTime = cdkExpiresTime;
    }

    public Date getCdkStartTime() {
        return cdkStartTime;
    }

    public void setCdkStartTime(Date cdkStartTime) {
        this.cdkStartTime = cdkStartTime;
    }

    public String getQrCodeZipUrl() {
        return qrCodeZipUrl;
    }

    public void setQrCodeZipUrl(String qrCodeZipUrl) {
        this.qrCodeZipUrl = qrCodeZipUrl;
    }

    public Integer getCouponWay() {
        return couponWay;
    }

    public void setCouponWay(Integer couponWay) {
        this.couponWay = couponWay;
    }

    public String getUseMethods() {
        return useMethods;
    }

    public void setUseMethods(String useMethods) {
        this.useMethods = useMethods;
    }

    public Integer getLinkType() {
        return linkType;
    }

    public void setLinkType(Integer linkType) {
        this.linkType = linkType;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public Integer getLinkAppId() {
        return linkAppId;
    }

    public void setLinkAppId(Integer linkAppId) {
        this.linkAppId = linkAppId;
    }

    public Integer getCloseFloatImg() {
        return closeFloatImg;
    }

    public void setCloseFloatImg(Integer closeFloatImg) {
        this.closeFloatImg = closeFloatImg;
    }

    public String getRentMethods() {
        return rentMethods;
    }

    public void setRentMethods(String rentMethods) {
        this.rentMethods = rentMethods;
    }

    /*
     *无门槛优惠券预算金额
     */
    private BigDecimal preCouponTotalAmount = new BigDecimal(0);
    /*
     *非收入无门槛券预算金额
     */
    private BigDecimal preNonRevenueCouponTotalAmount = new BigDecimal(0);

    public BigDecimal getPreCouponTotalAmount() {
        return preCouponTotalAmount;
    }

    public void setPreCouponTotalAmount(BigDecimal preCouponTotalAmount) {
        this.preCouponTotalAmount = preCouponTotalAmount;
    }

    public BigDecimal getPreNonRevenueCouponTotalAmount() {
        return preNonRevenueCouponTotalAmount;
    }

    public void setPreNonRevenueCouponTotalAmount(BigDecimal preNonRevenueCouponTotalAmount) {
        this.preNonRevenueCouponTotalAmount = preNonRevenueCouponTotalAmount;
    }

    public Integer getTransType() {
        return transType;
    }

    public void setTransType(Integer transType) {
        this.transType = transType;
    }

    public Integer getCouponOwner() {
        return couponOwner;
    }

    public void setCouponOwner(Integer couponOwner) {
        this.couponOwner = couponOwner;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getDayVoucherLimit() {
        return dayVoucherLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.day_voucher_limit
     *
     * @param dayVoucherLimit the value for mmp_third_activity.day_voucher_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setDayVoucherLimit(Integer dayVoucherLimit) {
        this.dayVoucherLimit = dayVoucherLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.total_voucher_limit
     *
     * @return the value of mmp_third_activity.total_voucher_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer getTotalVoucherLimit() {
        return totalVoucherLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.total_voucher_limit
     *
     * @param totalVoucherLimit the value for mmp_third_activity.total_voucher_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setTotalVoucherLimit(Integer totalVoucherLimit) {
        this.totalVoucherLimit = totalVoucherLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.day_offer_limit
     *
     * @return the value of mmp_third_activity.day_offer_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer getDayOfferLimit() {
        return dayOfferLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.day_offer_limit
     *
     * @param dayOfferLimit the value for mmp_third_activity.day_offer_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setDayOfferLimit(Integer dayOfferLimit) {
        this.dayOfferLimit = dayOfferLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.total_offer_limit
     *
     * @return the value of mmp_third_activity.total_offer_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer getTotalOfferLimit() {
        return totalOfferLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.total_offer_limit
     *
     * @param totalOfferLimit the value for mmp_third_activity.total_offer_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setTotalOfferLimit(Integer totalOfferLimit) {
        this.totalOfferLimit = totalOfferLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.pre_total_amount
     *
     * @return the value of mmp_third_activity.pre_total_amount
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public BigDecimal getPreTotalAmount() {
        return preTotalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.pre_total_amount
     *
     * @param preTotalAmount the value for mmp_third_activity.pre_total_amount
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setPreTotalAmount(BigDecimal preTotalAmount) {
        this.preTotalAmount = preTotalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.activity_channel_key
     *
     * @return the value of mmp_third_activity.activity_channel_key
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public String getActivityChannelKey() {
        return activityChannelKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.activity_channel_key
     *
     * @param activityChannelKey the value for mmp_third_activity.activity_channel_key
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setActivityChannelKey(String activityChannelKey) {
        this.activityChannelKey = activityChannelKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.activity_channel
     *
     * @return the value of mmp_third_activity.activity_channel
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public String getActivityChannel() {
        return activityChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.activity_channel
     *
     * @param activityChannel the value for mmp_third_activity.activity_channel
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setActivityChannel(String activityChannel) {
        this.activityChannel = activityChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.e_amount_greater
     *
     * @return the value of mmp_third_activity.e_amount_greater
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer geteAmountGreater() {
        return eAmountGreater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.e_amount_greater
     *
     * @param eAmountGreater the value for mmp_third_activity.e_amount_greater
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void seteAmountGreater(Integer eAmountGreater) {
        this.eAmountGreater = eAmountGreater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.e_amount_less
     *
     * @return the value of mmp_third_activity.e_amount_less
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer geteAmountLess() {
        return eAmountLess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.e_amount_less
     *
     * @param eAmountLess the value for mmp_third_activity.e_amount_less
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void seteAmountLess(Integer eAmountLess) {
        this.eAmountLess = eAmountLess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.e_offer_number
     *
     * @return the value of mmp_third_activity.e_offer_number
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer geteOfferNumber() {
        return eOfferNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.e_offer_number
     *
     * @param eOfferNumber the value for mmp_third_activity.e_offer_number
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void seteOfferNumber(Integer eOfferNumber) {
        this.eOfferNumber = eOfferNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.period
     *
     * @return the value of mmp_third_activity.period
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer getPeriod() {
        return period;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.e_offer_number
     *
     * @param period the value for mmp_third_activity.period
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setPeriod(Integer period) {
        this.period = period;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.count_limit
     *
     * @return the value of mmp_third_activity.count_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Integer getCountLimit() {
        return countLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.count_limit
     *
     * @param countLimit the value for mmp_third_activity.count_limit
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setCountLimit(Integer countLimit) {
        this.countLimit = countLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.img_url
     *
     * @return the value of mmp_third_activity.img_url
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public String getImgUrl() {
        return imgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.img_url
     *
     * @param imgUrl the value for mmp_third_activity.img_url
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.activity_url
     *
     * @return the value of mmp_third_activity.activity_url
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public String getActivityUrl() {
        return activityUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.activity_url
     *
     * @param activityUrl the value for mmp_third_activity.activity_url
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public void setActivityUrl(String activityUrl) {
        this.activityUrl = activityUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_third_activity.packages_id
     *
     * @return the value of mmp_third_activity.packages_id
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    public Long getPackagesId() {
        return packagesId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_third_activity.packages_id
     *
     * @param packagesId the value for mmp_third_activity.packages_id
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     *
     */
    public void setPackagesId(Long packagesId) {
        this.packagesId = packagesId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getOfficialWebAddress() {
        return officialWebAddress;
    }

    public void setOfficialWebAddress(String officialWebAddress) {
        this.officialWebAddress = officialWebAddress;
    }

    public String getServiceTelephone() {
        return serviceTelephone;
    }

    public void setServiceTelephone(String serviceTelephone) {
        this.serviceTelephone = serviceTelephone;
    }

	public Integer getOfferTiming() {
		return offerTiming;
	}

	public void setOfferTiming(Integer offerTiming) {
		this.offerTiming = offerTiming;
	}

	public Integer getOrderDuration() {
		return orderDuration;
	}

	public void setOrderDuration(Integer orderDuration) {
		this.orderDuration = orderDuration;
	}

	public Integer getBootDownloadLayer() {
		return bootDownloadLayer;
	}

	public void setBootDownloadLayer(Integer bootDownloadLayer) {
		this.bootDownloadLayer = bootDownloadLayer;
	}

	public String getActivityRules() {
		return activityRules;
	}

	public void setActivityRules(String activityRules) {
		this.activityRules = activityRules;
	}

    public String getActivityTitle() {
        return activityTitle;
    }

    public void setActivityTitle(String activityTitle) {
        this.activityTitle = activityTitle;
    }

    public String getActivitySubtitle() {
        return activitySubtitle;
    }

    public void setActivitySubtitle(String activitySubtitle) {
        this.activitySubtitle = activitySubtitle;
    }

	public Integer getBackgroundColor() {
		return backgroundColor;
	}

	public void setBackgroundColor(Integer backgroundColor) {
		this.backgroundColor = backgroundColor;
	}

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getPickupStartTime() {
        return pickupStartTime;
    }

    public void setPickupStartTime(String pickupStartTime) {
        this.pickupStartTime = pickupStartTime;
    }

    public String getPickupEndTime() {
        return pickupEndTime;
    }

    public void setPickupEndTime(String pickupEndTime) {
        this.pickupEndTime = pickupEndTime;
    }

    public String getReturnStartTime() {
        return returnStartTime;
    }

    public void setReturnStartTime(String returnStartTime) {
        this.returnStartTime = returnStartTime;
    }

    public String getReturnEndTime() {
        return returnEndTime;
    }

    public void setReturnEndTime(String returnEndTime) {
        this.returnEndTime = returnEndTime;
    }

    public String getReturnDaysOfWeek() {
        return returnDaysOfWeek;
    }

    public void setReturnDaysOfWeek(String returnDaysOfWeek) {
        this.returnDaysOfWeek = returnDaysOfWeek;
    }


    public Integer getOrderRewardType() {
        return orderRewardType;
    }

    public void setOrderRewardType(Integer orderRewardType) {
        this.orderRewardType = orderRewardType;
    }

    public Double getWithdrawAmountMin() {
        return withdrawAmountMin;
    }

    public void setWithdrawAmountMin(Double withdrawAmountMin) {
        this.withdrawAmountMin = withdrawAmountMin;
    }

    public Integer getRewardTaxRate() {
        return rewardTaxRate;
    }

    public void setRewardTaxRate(Integer rewardTaxRate) {
        this.rewardTaxRate = rewardTaxRate;
    }

    public Integer getRewardDaysLimit() {
        return rewardDaysLimit;
    }

    public void setRewardDaysLimit(Integer rewardDaysLimit) {
        this.rewardDaysLimit = rewardDaysLimit;
    }

    public Integer getRewardAmountRate() {
        return rewardAmountRate;
    }

    public void setRewardAmountRate(Integer rewardAmountRate) {
        this.rewardAmountRate = rewardAmountRate;
    }

    public Double getRewardAmountMax() {
        return rewardAmountMax;
    }

    public void setRewardAmountMax(Double rewardAmountMax) {
        this.rewardAmountMax = rewardAmountMax;
    }

    public Integer getOrderNumLimit() {
        return orderNumLimit;
    }

    public void setOrderNumLimit(Integer orderNumLimit) {
        this.orderNumLimit = orderNumLimit;
    }

    public Integer getOrderPayInterval() {
        return orderPayInterval;
    }

    public void setOrderPayInterval(Integer orderPayInterval) {
        this.orderPayInterval = orderPayInterval;
    }

    public Double getOrderAmountLimit() {
        return orderAmountLimit;
    }

    public void setOrderAmountLimit(Double orderAmountLimit) {
        this.orderAmountLimit = orderAmountLimit;
    }

    public String getPosterMiniProgram() {
        return posterMiniProgram;
    }

    public void setPosterMiniProgram(String posterMiniProgram) {
        this.posterMiniProgram = posterMiniProgram;
    }

    public String getRentMethodGroup() {
        return rentMethodGroup;
    }

    public void setRentMethodGroup(String rentMethodGroup) {
        this.rentMethodGroup = rentMethodGroup;
    }
}