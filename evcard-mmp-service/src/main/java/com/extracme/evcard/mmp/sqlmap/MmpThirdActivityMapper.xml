<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.mmp.dao.MmpThirdActivityMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.mmp.model.MmpThirdActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="day_voucher_limit" property="dayVoucherLimit" jdbcType="INTEGER" />
    <result column="total_voucher_limit" property="totalVoucherLimit" jdbcType="INTEGER" />
    <result column="day_offer_limit" property="dayOfferLimit" jdbcType="INTEGER" />
    <result column="total_offer_limit" property="totalOfferLimit" jdbcType="INTEGER" />
    <result column="pre_total_amount" property="preTotalAmount" jdbcType="DOUBLE" />
    <result column="activity_channel_key" property="activityChannelKey" jdbcType="VARCHAR" />
    <result column="activity_channel" property="activityChannel" jdbcType="VARCHAR" />
    <result column="e_amount_greater" property="eAmountGreater" jdbcType="INTEGER" />
    <result column="e_amount_less" property="eAmountLess" jdbcType="INTEGER" />
    <result column="e_offer_number" property="eOfferNumber" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
    <result column="period" property="period" jdbcType="INTEGER" />
    <result column="count_limit" property="countLimit" jdbcType="INTEGER" />
    <result column="img_url" property="imgUrl" jdbcType="VARCHAR" />
    <result column="activity_url" property="activityUrl" jdbcType="VARCHAR" />
    <result column="packages_id" property="packagesId" jdbcType="BIGINT" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="official_web_address" property="officialWebAddress" jdbcType="VARCHAR" />
    <result column="service_telephone" property="serviceTelephone" jdbcType="VARCHAR" />
    <result column="offer_timing" property="offerTiming" jdbcType="INTEGER" />
    <result column="order_duration" property="orderDuration" jdbcType="INTEGER" />
    <result column="boot_download_layer" property="bootDownloadLayer" jdbcType="INTEGER" />
    <result column="activity_rules" property="activityRules" jdbcType="VARCHAR" />
    <result column="activity_title" property="activityTitle" jdbcType="VARCHAR" />
    <result column="activity_subtitle" property="activitySubtitle" jdbcType="VARCHAR" />
    <result column="background_color" property="backgroundColor" jdbcType="INTEGER" />
    <result column="trans_type" property="transType" jdbcType="BIGINT"/>
    <result column="coupon_owner" property="couponOwner" jdbcType="BIGINT"/>
    <result column="owner_id" property="ownerId" jdbcType="VARCHAR"/>
    <result column="pre_coupon_total_amount" property="preCouponTotalAmount" jdbcType="DOUBLE" />
    <result column="pre_nonrevenue_coupon_total_amount" property="preNonRevenueCouponTotalAmount" jdbcType="DOUBLE" />

    <result column="ORDER_AMOUNT" jdbcType="DOUBLE" property="orderAmount" />
    <result column="RETURN_DAYS_OF_WEEK" jdbcType="VARCHAR" property="returnDaysOfWeek"/>
    <result column="PICKUP_START_TIME" jdbcType="VARCHAR" property="pickupStartTime" />
    <result column="PICKUP_END_TIME" jdbcType="VARCHAR" property="pickupEndTime" />
    <result column="RETURN_START_TIME" jdbcType="VARCHAR" property="returnStartTime" />
    <result column="RETURN_END_TIME" jdbcType="VARCHAR" property="returnEndTime" />

    <result column="order_reward_type" jdbcType="INTEGER" property="orderRewardType" />
    <result column="withdraw_amount_min" jdbcType="DOUBLE" property="withdrawAmountMin"/>
    <result column="reward_tax_rate" jdbcType="INTEGER" property="rewardTaxRate" />
    <result column="reward_days_limit" jdbcType="INTEGER" property="rewardDaysLimit" />
    <result column="reward_amount_rate" jdbcType="INTEGER" property="rewardAmountRate" />
    <result column="reward_amount_max" jdbcType="DOUBLE" property="rewardAmountMax" />
    <result column="order_num_limit" jdbcType="INTEGER" property="orderNumLimit" />
    <result column="order_pay_interval" jdbcType="INTEGER" property="orderPayInterval"/>
    <result column="order_amount_limit" jdbcType="DOUBLE" property="orderAmountLimit" />

    <result column="poster_mini_program" jdbcType="VARCHAR" property="posterMiniProgram" />
    <result column="rent_methods" jdbcType="VARCHAR" property="rentMethods" />
    <result column="rent_method_group" jdbcType="VARCHAR" property="rentMethodGroup" />

    <result column="link_type" jdbcType="INTEGER" property="linkType"/>
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="link_app_id" jdbcType="INTEGER" property="linkAppId"/>
    <result column="close_float_img" jdbcType="INTEGER" property="closeFloatImg"/>

    <result column="use_methods" jdbcType="VARCHAR" property="useMethods" />
    <result column="coupon_way" jdbcType="INTEGER" property="couponWay" />
    <result column="qr_code_zip_url" jdbcType="VARCHAR" property="qrCodeZipUrl" />

    <result column="cdk_start_time" jdbcType="TIMESTAMP" property="cdkStartTime" />
    <result column="cdk_expires_time" jdbcType="TIMESTAMP" property="cdkExpiresTime" />
    <result column="one_code_mul_coupon_flag" jdbcType="INTEGER" property="oneCodeDulCouponFlag" />
    <result column="coupon_code_num" jdbcType="INTEGER" property="couponCodeNum" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    id, day_voucher_limit, total_voucher_limit, day_offer_limit, total_offer_limit, pre_total_amount,
    activity_channel_key, activity_channel, e_amount_greater, e_amount_less , misc_desc,
    status, create_time, create_oper_id, create_oper_name, update_time, update_oper_id,
    update_oper_name, e_offer_number, period, count_limit, img_url, activity_url, packages_id,
    brand_name, official_web_address, service_telephone,offer_timing,order_duration,boot_download_layer,activity_rules,
    activity_title,activity_subtitle,background_color,trans_type,coupon_owner,owner_id,pre_coupon_total_amount,
    pre_nonrevenue_coupon_total_amount,
    order_amount, return_days_of_week, pickup_start_time, pickup_end_time, return_start_time,return_end_time,
    order_reward_type,withdraw_amount_min,reward_tax_rate,reward_days_limit,reward_amount_rate,
    reward_amount_max,order_num_limit,order_pay_interval,order_amount_limit,poster_mini_program, rent_methods,rent_method_group,
    link_type, link_url, link_app_id, close_float_img, use_methods, coupon_way,qr_code_zip_url,cdk_start_time,cdk_expires_time,one_code_mul_coupon_flag,coupon_code_num
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    select
    <include refid="Base_Column_List" />
    from ${mmpSchema}.mmp_third_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    delete from ${mmpSchema}.mmp_third_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.mmp.model.MmpThirdActivity"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    insert into ${mmpSchema}.mmp_third_activity (id, day_voucher_limit, total_voucher_limit,
    day_offer_limit, total_offer_limit, pre_total_amount,
    activity_channel_key, activity_channel, e_amount_greater,
    e_amount_less, misc_desc, status,
    create_time, create_oper_id, create_oper_name,
    update_time, update_oper_id, update_oper_name, e_offer_number,offer_timing,order_duration,boot_download_layer,
    activity_rules,activity_title,activity_subtitle,background_color,
    order_amount, return_days_of_week,
    pickup_start_time, pickup_end_time, return_start_time, return_end_time,
    order_reward_type,withdraw_amount_min,reward_tax_rate,reward_days_limit,reward_amount_rate,
    reward_amount_max,order_num_limit,order_pay_interval, order_amount_limit, poster_mini_program, rent_methods,rent_method_group,
    link_type, link_url, link_app_id, close_float_img, use_methods, coupon_way)
    values (#{id,jdbcType=BIGINT}, #{dayVoucherLimit,jdbcType=INTEGER}, #{totalVoucherLimit,jdbcType=INTEGER},
    #{dayOfferLimit,jdbcType=INTEGER}, #{totalOfferLimit,jdbcType=INTEGER}, #{preTotalAmount,jdbcType=DOUBLE},
    #{activityChannelKey,jdbcType=VARCHAR}, #{activityChannel,jdbcType=VARCHAR}, #{eAmountGreater,jdbcType=INTEGER},
    #{eAmountLess,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR},
    #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR},
    #{eOfferNumber,jdbcType=INTEGER},#{offerTiming,jdbcType=INTEGER},#{orderDuration,jdbcType=INTEGER},
    #{bootDownloadLayer,jdbcType=INTEGER},#{activityRules,jdbcType=VARCHAR},
    #{activityTitle,jdbcType=VARCHAR},#{activitySubtitle,jdbcType=VARCHAR},#{backgroundColor,jdbcType=INTEGER},
    #{orderAmount,jdbcType=DOUBLE},#{returnDaysOfWeek,jdbcType=VARCHAR},
    #{pickupStartTime,jdbcType=VARCHAR},#{pickupEndTime,jdbcType=VARCHAR},
    #{returnStartTime,jdbcType=VARCHAR},#{returnEndTime,jdbcType=VARCHAR},
    #{orderRewardType,jdbcType=INTEGER},#{withdrawAmountMin,jdbcType=DOUBLE},#{rewardTaxRate,jdbcType=INTEGER},
    #{rewardDaysLimit,jdbcType=INTEGER},#{rewardAmountRate,jdbcType=INTEGER},#{rewardAmountMax,jdbcType=DOUBLE},
    #{orderNumLimit,jdbcType=INTEGER},#{orderPayInterval,jdbcType=INTEGER},#{orderAmountLimit,jdbcType=DOUBLE},
    #{posterMiniProgram,jdbcType=VARCHAR},#{rentMethods,jdbcType=VARCHAR},#{rentMethodGroup,jdbcType=VARCHAR},
    #{linkType,jdbcType=INTEGER},#{linkUrl,jdbcType=VARCHAR},#{linkAppId,jdbcType=INTEGER},#{closeFloatImg,jdbcType=INTEGER},
    #{useMethods,jdbcType=VARCHAR},#{couponWay,jdbcType=INTEGER}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.mmp.model.MmpThirdActivity"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    insert into ${mmpSchema}.mmp_third_activity
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="dayVoucherLimit != null" >
        day_voucher_limit,
      </if>
      <if test="totalVoucherLimit != null" >
        total_voucher_limit,
      </if>
      <if test="dayOfferLimit != null" >
        day_offer_limit,
      </if>
      <if test="totalOfferLimit != null" >
        total_offer_limit,
      </if>
      <if test="preTotalAmount != null" >
        pre_total_amount,
      </if>
      <if test="activityChannelKey != null" >
        activity_channel_key,
      </if>
      <if test="activityChannel != null" >
        activity_channel,
      </if>
      <if test="eAmountGreater != null" >
        e_amount_greater,
      </if>
      <if test="eAmountLess != null" >
        e_amount_less,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
      <if test="eOfferNumber != null" >
        e_offer_number,
      </if>
      <if test="period != null" >
        period,
      </if>
      <if test="countLimit != null" >
        count_limit,
      </if>
      <if test="imgUrl != null" >
        img_url,
      </if>
      <if test="activityUrl != null" >
        activity_url,
      </if>
      <if test="packagesId != null" >
        packages_id,
      </if>
      <if test="brandName != null" >
        brand_name,
      </if>
      <if test="officialWebAddress != null" >
        official_web_address,
      </if>
      <if test="serviceTelephone != null" >
        service_telephone,
      </if>
      <if test="offerTiming != null" >
        offer_timing,
      </if>
      <if test="orderDuration != null" >
        order_duration,
      </if>
      <if test="bootDownloadLayer != null" >
        boot_download_layer,
      </if>
      <if test="activityRules != null" >
        activity_rules,
      </if>
      <if test="activityTitle != null" >
        activity_title,
      </if>
      <if test="activitySubtitle != null" >
        activity_subtitle,
      </if>
      <if test="backgroundColor != null" >
        background_color,
      </if>
      <if test="transType != null">
        trans_type,
      </if>
      <if test="couponOwner != null">
        coupon_owner,
      </if>
      <if test="ownerId != null">
        owner_id,
      </if>
      <if test="preCouponTotalAmount != null">
        pre_coupon_total_amount,
      </if>
      <if test="preNonRevenueCouponTotalAmount != null">
        pre_nonrevenue_coupon_total_amount,
      </if>

      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="returnDaysOfWeek != null">
        return_days_of_week,
      </if>
      <if test="pickupStartTime != null">
        pickup_start_time,
      </if>
      <if test="pickupEndTime != null">
        pickup_end_time,
      </if>
      <if test="returnStartTime != null">
        return_start_time,
      </if>
      <if test="returnEndTime != null">
        return_end_time,
      </if>

      <if test="orderRewardType != null">
        order_reward_type,
      </if>
      <if test="withdrawAmountMin != null">
        withdraw_amount_min,
      </if>

      <if test="rewardTaxRate != null">
        reward_tax_rate,
      </if>
      <if test="rewardDaysLimit != null">
        reward_days_limit,
      </if>
      <if test="rewardAmountRate != null">
        reward_amount_rate,
      </if>
      <if test="rewardAmountMax != null">
        reward_amount_max,
      </if>
      <if test="orderNumLimit != null">
        order_num_limit,
      </if>
      <if test="orderPayInterval != null">
        order_pay_interval,
      </if>
      <if test="orderAmountLimit != null">
        order_amount_limit,
      </if>
      <if test="posterMiniProgram != null">
        poster_mini_program,
      </if>
      <if test="rentMethods != null">
        rent_methods,
      </if>
      <if test="rentMethodGroup != null">
        rent_method_group,
      </if>
      <if test="linkType != null">
        link_type,
      </if>
      <if test="linkUrl != null">
        link_url,
      </if>
      <if test="linkAppId != null">
        link_app_id,
      </if>
      <if test="closeFloatImg != null">
        close_float_img,
      </if>
      <if test="useMethods != null">
        use_methods,
      </if>
      <if test="couponWay != null">
        coupon_way,
      </if>
      <if test="cdkStartTime != null">
        cdk_start_time,
      </if>
      <if test="cdkExpiresTime != null">
        cdk_expires_time,
      </if>
      <if test="oneCodeDulCouponFlag != null">
        one_code_mul_coupon_flag,
      </if>
      <if test="couponCodeNum != null">
        coupon_code_num,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dayVoucherLimit != null" >
        #{dayVoucherLimit,jdbcType=INTEGER},
      </if>
      <if test="totalVoucherLimit != null" >
        #{totalVoucherLimit,jdbcType=INTEGER},
      </if>
      <if test="dayOfferLimit != null" >
        #{dayOfferLimit,jdbcType=INTEGER},
      </if>
      <if test="totalOfferLimit != null" >
        #{totalOfferLimit,jdbcType=INTEGER},
      </if>
      <if test="preTotalAmount != null" >
        #{preTotalAmount,jdbcType=DOUBLE},
      </if>
      <if test="activityChannelKey != null" >
        #{activityChannelKey,jdbcType=VARCHAR},
      </if>
      <if test="activityChannel != null" >
        #{activityChannel,jdbcType=VARCHAR},
      </if>
      <if test="eAmountGreater != null" >
        #{eAmountGreater,jdbcType=INTEGER},
      </if>
      <if test="eAmountLess != null" >
        #{eAmountLess,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="eOfferNumber != null" >
        #{eOfferNumber,jdbcType=INTEGER},
      </if>
      <if test="period != null" >
        #{period,jdbcType=INTEGER},
      </if>
      <if test="countLimit != null" >
        #{countLimit,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null" >
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="activityUrl != null" >
        #{activityUrl,jdbcType=VARCHAR},
      </if>
      <if test="packagesId != null" >
        #{packagesId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="officialWebAddress != null" >
        #{officialWebAddress,jdbcType=VARCHAR},
      </if>
      <if test="serviceTelephone != null" >
        #{serviceTelephone,jdbcType=VARCHAR},
      </if>
      <if test="offerTiming != null" >
        #{offerTiming,jdbcType=INTEGER},
      </if>
      <if test="orderDuration != null" >
        #{orderDuration,jdbcType=INTEGER},
      </if>
      <if test="bootDownloadLayer != null" >
        #{bootDownloadLayer,jdbcType=INTEGER},
      </if>
      <if test="activityRules != null" >
        #{activityRules,jdbcType=VARCHAR},
      </if>
      <if test="activityTitle != null" >
        #{activityTitle,jdbcType=VARCHAR},
      </if>
      <if test="activitySubtitle != null" >
        #{activitySubtitle,jdbcType=VARCHAR},
      </if>
      <if test="backgroundColor != null" >
        #{backgroundColor,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        #{transType,jdbcType=BIGINT},
      </if>
      <if test="couponOwner != null">
        #{couponOwner,jdbcType=BIGINT},
      </if>
      <if test="ownerId != null">
        #{ownerId,jdbcType=BIGINT},

      </if>
      <if test="preCouponTotalAmount != null">
        #{preCouponTotalAmount,jdbcType=DOUBLE},
      </if>
      <if test="preNonRevenueCouponTotalAmount != null">
        #{preNonRevenueCouponTotalAmount,jdbcType=DOUBLE},
      </if>

      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DOUBLE},
      </if>
      <if test="returnDaysOfWeek != null">
        #{returnDaysOfWeek,jdbcType=VARCHAR},
      </if>

      <if test="pickupStartTime != null">
        #{pickupStartTime,jdbcType=VARCHAR},
      </if>
      <if test="pickupEndTime != null">
        #{pickupEndTime,jdbcType=VARCHAR},
      </if>
      <if test="returnStartTime != null">
        #{returnStartTime,jdbcType=VARCHAR},
      </if>
      <if test="returnEndTime != null">
        #{returnEndTime,jdbcType=VARCHAR},
      </if>
      <if test="orderRewardType != null" >
        #{orderRewardType,jdbcType=INTEGER},
      </if>
      <if test="withdrawAmountMin != null" >
        #{withdrawAmountMin,jdbcType=DOUBLE},
      </if>
      <if test="rewardTaxRate != null" >
        #{rewardTaxRate,jdbcType=INTEGER},
      </if>
      <if test="rewardDaysLimit != null" >
        #{rewardDaysLimit,jdbcType=INTEGER},
      </if>
      <if test="rewardAmountRate != null" >
        #{rewardAmountRate,jdbcType=INTEGER},
      </if>
      <if test="rewardAmountMax != null" >
        #{rewardAmountMax,jdbcType=DOUBLE},
      </if>
      <if test="orderNumLimit != null" >
        #{orderNumLimit,jdbcType=INTEGER},
      </if>
      <if test="orderPayInterval != null" >
        #{orderPayInterval,jdbcType=INTEGER},
      </if>
      <if test="orderAmountLimit != null" >
        #{orderAmountLimit,jdbcType=DOUBLE},
      </if>
      <if test="posterMiniProgram != null" >
        #{posterMiniProgram,jdbcType=VARCHAR},
      </if>
      <if test="rentMethods != null" >
        #{rentMethods,jdbcType=VARCHAR},
      </if>
      <if test="rentMethodGroup != null">
        #{rentMethodGroup,jdbcType=VARCHAR},
      </if>
      <if test="linkType != null">
        #{linkType,jdbcType=INTEGER},
      </if>
      <if test="linkUrl != null">
        #{linkUrl,jdbcType=VARCHAR},
      </if>
      <if test="linkAppId != null">
        #{linkAppId,jdbcType=INTEGER},
      </if>
      <if test="closeFloatImg != null">
        #{closeFloatImg,jdbcType=INTEGER},
      </if>

      <if test="useMethods != null" >
        #{useMethods,jdbcType=VARCHAR},
      </if>
      <if test="couponWay != null">
        #{couponWay,jdbcType=INTEGER},
      </if>
      <if test="cdkStartTime != null">
        #{cdkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cdkExpiresTime != null">
        #{cdkExpiresTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oneCodeDulCouponFlag != null">
        #{oneCodeDulCouponFlag,jdbcType=INTEGER},
      </if>
      <if test="couponCodeNum != null">
        #{couponCodeNum,jdbcType=INTEGER},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.mmp.model.MmpThirdActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    update ${mmpSchema}.mmp_third_activity
    <set >
      <if test="dayVoucherLimit != null" >
        day_voucher_limit = #{dayVoucherLimit,jdbcType=INTEGER},
      </if>
      <if test="totalVoucherLimit != null" >
        total_voucher_limit = #{totalVoucherLimit,jdbcType=INTEGER},
      </if>
      <if test="dayOfferLimit != null" >
        day_offer_limit = #{dayOfferLimit,jdbcType=INTEGER},
      </if>
      <if test="totalOfferLimit != null" >
        total_offer_limit = #{totalOfferLimit,jdbcType=INTEGER},
      </if>
      <if test="preTotalAmount != null" >
        pre_total_amount = #{preTotalAmount,jdbcType=DOUBLE},
      </if>
      <if test="activityChannelKey != null" >
        activity_channel_key = #{activityChannelKey,jdbcType=VARCHAR},
      </if>
      <if test="activityChannel != null" >
        activity_channel = #{activityChannel,jdbcType=VARCHAR},
      </if>
      <if test="eAmountGreater != null" >
        e_amount_greater = #{eAmountGreater,jdbcType=INTEGER},
      </if>
      <if test="eAmountLess != null" >
        e_amount_less = #{eAmountLess,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="eOfferNumber != null" >
        e_offer_number = #{eOfferNumber,jdbcType=INTEGER},
      </if>
      <if test="period != null" >
        period = #{period,jdbcType=INTEGER},
      </if>
      <if test="countLimit != null" >
        count_limit = #{countLimit,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null" >
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="activityUrl != null" >
        activity_url = #{activityUrl,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="officialWebAddress != null" >
        official_web_address = #{officialWebAddress,jdbcType=VARCHAR},
      </if>
      <if test="serviceTelephone != null" >
        service_telephone = #{serviceTelephone,jdbcType=VARCHAR},
      </if>
      <if test="offerTiming != null" >
        offer_timing = #{offerTiming,jdbcType=INTEGER},
      </if>
      <if test="orderDuration != null" >
        order_duration = #{orderDuration,jdbcType=INTEGER},
      </if>
      <if test="bootDownloadLayer != null" >
        boot_download_layer = #{bootDownloadLayer,jdbcType=INTEGER},
      </if>
      <if test="activityRules != null" >
        activity_rules = #{activityRules,jdbcType=VARCHAR},
      </if>
      <if test="activityTitle != null" >
        activity_title = #{activityTitle,jdbcType=VARCHAR},
      </if>
      <if test="activitySubtitle != null" >
        activity_subtitle = #{activitySubtitle,jdbcType=VARCHAR},
      </if>
      <if test="backgroundColor != null" >
        background_color = #{backgroundColor,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        trans_type = #{transType,jdbcType=BIGINT},
      </if>
      <if test="couponOwner != null">
        coupon_owner = #{couponOwner,jdbcType=BIGINT},
      </if>
      <if test="ownerId != null">
        owner_id = #{ownerId,jdbcType=BIGINT},
      </if>
      <if test="preCouponTotalAmount != null">
        pre_coupon_total_amount = #{preCouponTotalAmount,jdbcType=DOUBLE},
      </if>
      <if test="preNonRevenueCouponTotalAmount != null">
        pre_nonrevenue_coupon_total_amount = #{preNonRevenueCouponTotalAmount,jdbcType=DOUBLE},
      </if>

      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DOUBLE},
      </if>
      <if test="returnDaysOfWeek != null" >
        return_days_of_week = #{returnDaysOfWeek,jdbcType=VARCHAR},
      </if>
      <if test="pickupStartTime != null" >
        pickup_start_time = #{pickupStartTime,jdbcType=VARCHAR},
      </if>
      <if test="pickupEndTime != null" >
        pickup_end_time = #{pickupEndTime,jdbcType=VARCHAR},
      </if>
      <if test="returnStartTime != null" >
        return_start_time = #{returnStartTime,jdbcType=VARCHAR},
      </if>
      <if test="returnEndTime != null" >
        return_end_time = #{returnEndTime,jdbcType=VARCHAR},
      </if>

      <if test="orderRewardType != null">
        order_reward_type= #{orderRewardType,jdbcType=INTEGER},
      </if>
      <if test="withdrawAmountMin != null">
        withdraw_amount_min= #{withdrawAmountMin,jdbcType=DOUBLE},
      </if>

      <if test="rewardTaxRate != null">
        reward_tax_rate= #{rewardTaxRate,jdbcType=INTEGER},
      </if>
      <if test="rewardDaysLimit != null">
        reward_days_limit= #{rewardDaysLimit,jdbcType=INTEGER},
      </if>
      <if test="rewardAmountRate != null">
        reward_amount_rate= #{rewardAmountRate,jdbcType=INTEGER},
      </if>
      <if test="rewardAmountMax != null">
        reward_amount_max= #{rewardAmountMax,jdbcType=DOUBLE},
      </if>
      <if test="orderNumLimit != null">
        order_num_limit= #{orderNumLimit,jdbcType=INTEGER},
      </if>
      <if test="orderPayInterval != null">
        order_pay_interval= #{orderPayInterval,jdbcType=INTEGER},
      </if>
      <if test="orderAmountLimit != null">
        order_amount_limit= #{orderAmountLimit,jdbcType=DOUBLE},
      </if>
      <if test="posterMiniProgram != null">
        poster_mini_program= #{posterMiniProgram,jdbcType=VARCHAR},
      </if>
      <if test="rentMethods != null">
        rent_methods= #{rentMethods,jdbcType=VARCHAR},
      </if>
      <if test="rentMethodGroup != null">
        rent_method_group=#{rentMethodGroup,jdbcType=VARCHAR},
      </if>
      <if test="linkType != null">
        link_type= #{linkType,jdbcType=INTEGER},
      </if>
      <if test="linkUrl != null">
        link_url= #{linkUrl,jdbcType=VARCHAR},
      </if>
      <if test="linkAppId != null">
        link_app_id= #{linkAppId,jdbcType=INTEGER},
      </if>
      <if test="closeFloatImg != null">
        close_float_img= #{closeFloatImg,jdbcType=INTEGER},
      </if>

      <if test="useMethods != null">
        use_methods= #{useMethods,jdbcType=VARCHAR},
      </if>
      <if test="couponWay != null">
        coupon_way= #{couponWay,jdbcType=INTEGER}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.mmp.model.MmpThirdActivity" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 13 14:07:18 CST 2017.
    -->
    update ${mmpSchema}.mmp_third_activity
    set day_voucher_limit = #{dayVoucherLimit,jdbcType=INTEGER},
    total_voucher_limit = #{totalVoucherLimit,jdbcType=INTEGER},
    day_offer_limit = #{dayOfferLimit,jdbcType=INTEGER},
    total_offer_limit = #{totalOfferLimit,jdbcType=INTEGER},
    pre_total_amount = #{preTotalAmount,jdbcType=DOUBLE},
    activity_channel_key = #{activityChannelKey,jdbcType=VARCHAR},
    activity_channel = #{activityChannel,jdbcType=VARCHAR},
    e_amount_greater = #{eAmountGreater,jdbcType=INTEGER},
    e_amount_less = #{eAmountLess,jdbcType=INTEGER},
    misc_desc = #{miscDesc,jdbcType=VARCHAR},
    status = #{status,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    create_oper_id = #{createOperId,jdbcType=BIGINT},
    create_oper_name = #{createOperName,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR},
    e_offer_number = #{eOfferNumber,jdbcType=INTEGER},
    offer_timing = #{offerTiming,jdbcType=INTEGER},
    order_duration = #{orderDuration,jdbcType=INTEGER},
    boot_download_layer = #{bootDownloadLayer,jdbcType=INTEGER},
    activity_rules = #{activityRules,jdbcType=VARCHAR},
    activity_title = #{activityTitle,jdbcType=VARCHAR},
    activity_subtitle = #{activitySubtitle,jdbcType=VARCHAR},
    background_color = #{backgroundColor,jdbcType=VARCHAR},
    order_amount = #{orderAmount,jdbcType=INTEGER},
    return_days_of_week = #{returnDaysOfWeek,jdbcType=VARCHAR},
    pickup_start_time = #{pickupStartTime,jdbcType=VARCHAR},
    pickup_end_time = #{pickupEndTime,jdbcType=VARCHAR},
    return_start_time = #{returnStartTime,jdbcType=VARCHAR},
    return_end_time = #{returnEndTime,jdbcType=VARCHAR},

    order_reward_type= #{orderRewardType,jdbcType=INTEGER},
    withdraw_amount_min= #{withdrawAmountMin,jdbcType=DOUBLE},
    reward_tax_rate= #{rewardTaxRate,jdbcType=INTEGER},
    reward_days_limit= #{rewardDaysLimit,jdbcType=INTEGER},
    reward_amount_rate= #{rewardAmountRate,jdbcType=INTEGER},
    reward_amount_max= #{rewardAmountMax,jdbcType=DOUBLE},
    order_num_limit= #{orderNumLimit,jdbcType=INTEGER},
    order_pay_interval= #{orderPayInterval,jdbcType=INTEGER},
    order_amount_limit= #{orderAmountLimit,jdbcType=DOUBLE},
    poster_mini_program= #{posterMiniProgram,jdbcType=VARCHAR},
    rent_methods = #{rentMethods,jdbcType=VARCHAR},
    rent_method_group = #{rentMethodGroup,jdbcType=VARCHAR},

    link_type= #{linkType,jdbcType=INTEGER},
    link_url= #{linkUrl,jdbcType=VARCHAR},
    link_app_id= #{linkAppId,jdbcType=INTEGER},
    close_float_img= #{closeFloatImg,jdbcType=INTEGER},

    use_methods = #{useMethods,jdbcType=VARCHAR},
    coupon_way= #{couponWay,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectChannelThirdActivityByAppKey" resultMap="BaseResultMap">
    SELECT
       m.id, m.pre_total_amount
    FROM
    ${mmpSchema}.mmp_third_activity m
    LEFT JOIN ${mmpSchema}.mmp_pack_night_activity t ON m.id = t.third_activity_id
    WHERE
    m.activity_channel_key = #{appKey,jdbcType=VARCHAR}
    AND t.type = '4'
    AND t.activity_status = '2'
  </select>

  <select id="selectActivityByIds" resultMap="BaseResultMap">
    select id, activity_channel_key
    from ${mmpSchema}.mmp_third_activity
    where id IN
    <foreach collection="list" separator="," item="item" open="(" close=")">
      #{item}
    </foreach>
  </select>
</mapper>