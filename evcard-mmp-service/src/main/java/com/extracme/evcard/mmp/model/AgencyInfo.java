package com.extracme.evcard.mmp.model;

import java.math.BigDecimal;
import java.util.Date;

import com.extracme.framework.core.model.Model;
import com.extracme.framework.core.util.DateTimeUtils;

/**
 * 模型类，，对应表agency_info.
 */
public class AgencyInfo extends Model {
    private String agencyName;
    private String agencyId;
    private Double discountInner;
    private Double discountOuter;
    private String createdUser;
    private String createdTime;
    private String updatedUser;
    private String updatedTime;
    private String pickupWeeks;
    private String returnWeeks;
    private String pickupTime;
    private String returnTime;
    private String maxUserHour;
    /** 机构免押金 */
    private Integer exemptDeposit;
    private String contact;
    private String tel;
    private String mobilePhone;
    private String mail;
    private String licenseNo;
    private String fax;
    private String licenseNoImgUrl;
    private String contractImgUrl;
    private String taxRegistrationImgUrl;
    private String orgCodeImgUrl;
    private String remark;
    private Double payWay;
    private Double deposit;
    private Double rentMins;
    private String checkDate;
    private Integer checkAlert;
    /** 企业性质：0（外部）1（内部） */
    private String orgProperty;
    private Integer insideFlag;
    private String address;
    private Date lastRemindDate;
    private Date cooperateStartTime;
    private Date cooperateEndTime;
    /** 0-未开始；1-合作中；2-已暂停*/
    private Integer cooperateStatus;
    private Double vehicleThreshold;
    /** 企业折扣ID*/
    private Long discountId;
    /** 个人折扣ID*/
    private Long discountPersonalId;
    /** 套餐不同享ID*/
    private Long discountPackageId;

    /** 每月可透支额度*/
    private Double lineLimitMonthly;
    private String appKey;
    private Integer discountRule;
    private BigDecimal maxUnitPrice;
    private String orgName;
    private String vehicleNo;

    private String packageType;

    // 企业免押金额（新）
    private BigDecimal exemptDepositAmount;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 创建方式 1会员系统 2政企框架合同 3长租客户
     */
    private Integer createWay;

    /**
     * 长租框架合同编号
     */
    private String longRentContractId;

    /**
     * 长租框架合同名称
     */
    private String longRentContractName;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private Integer depositGuarantor;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private Integer orderPayer;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private Integer defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店
     */
    private Integer businessSource;

    /**
     * 推荐人姓名
     */
    private String referrerName;

    /**
     * 推荐人手机号
     */
    private String referrerMobile;

    public String getReferrerName() {
        return referrerName;
    }

    public void setReferrerName(String referrerName) {
        this.referrerName = referrerName;
    }

    public String getReferrerMobile() {
        return referrerMobile;
    }

    public void setReferrerMobile(String referrerMobile) {
        this.referrerMobile = referrerMobile;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        entityMap.put("expireTime", expireTime);
        this.expireTime = expireTime;
    }

    public Integer getCreateWay() {
        return createWay;
    }

    public void setCreateWay(Integer createWay) {
        entityMap.put("createWay", createWay);
        this.createWay = createWay;
    }

    public String getlongRentContractId() {
        return longRentContractId;
    }

    public void setlongRentContractId(String longRentContractId) {
        entityMap.put("longRentContractId", longRentContractId);
        this.longRentContractId = longRentContractId;
    }

    public String getLongRentContractName() {
        return longRentContractName;
    }

    public void setLongRentContractName(String longRentContractName) {
        entityMap.put("longRentContractName", longRentContractName);
        this.longRentContractName = longRentContractName;
    }

    public Integer getDepositGuarantor() {
        return depositGuarantor;
    }

    public void setDepositGuarantor(Integer depositGuarantor) {
        entityMap.put("depositGuarantor", depositGuarantor);
        this.depositGuarantor = depositGuarantor;
    }

    public Integer getOrderPayer() {
        return orderPayer;
    }

    public void setOrderPayer(Integer orderPayer) {
        entityMap.put("orderPayer", orderPayer);
        this.orderPayer = orderPayer;
    }

    public Integer getDefaultingParty() {
        return defaultingParty;
    }

    public void setDefaultingParty(Integer defaultingParty) {
        entityMap.put("defaultingParty", defaultingParty);
        this.defaultingParty = defaultingParty;
    }

    public Integer getBusinessSource() {
        return businessSource;
    }

    public void setBusinessSource(Integer businessSource) {
        entityMap.put("businessSource", businessSource);
        this.businessSource = businessSource;
    }

    public BigDecimal getExemptDepositAmount() {
        return exemptDepositAmount;
    }

    public void setExemptDepositAmount(BigDecimal exemptDepositAmount) {
        entityMap.put("exemptDepositAmount", exemptDepositAmount);
        this.exemptDepositAmount = exemptDepositAmount;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        entityMap.put("agencyName", agencyName);
        this.agencyName = agencyName;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        entityMap.put("agencyId", agencyId);
        this.agencyId = agencyId;
    }

    public Double getDiscountInner() {
        return discountInner;
    }

    public void setDiscountInner(Double discountInner) {
        entityMap.put("discountInner", discountInner);
        this.discountInner = discountInner;
    }

    public Double getDiscountOuter() {
        return discountOuter;
    }

    public void setDiscountOuter(Double discountOuter) {
        entityMap.put("discountOuter", discountOuter);
        this.discountOuter = discountOuter;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        entityMap.put("createdUser", createdUser);
        this.createdUser = createdUser;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        entityMap.put("createdTime", createdTime);
        this.createdTime = createdTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        entityMap.put("updatedUser", updatedUser);
        this.updatedUser = updatedUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        entityMap.put("updatedTime", updatedTime);
        this.updatedTime = updatedTime;
    }

    public String getPickupWeeks() {
        return pickupWeeks;
    }

    public void setPickupWeeks(String pickupWeeks) {
        entityMap.put("pickupWeeks", pickupWeeks);
        this.pickupWeeks = pickupWeeks;
    }

    public String getReturnWeeks() {
        return returnWeeks;
    }

    public void setReturnWeeks(String returnWeeks) {
        entityMap.put("returnWeeks", returnWeeks);
        this.returnWeeks = returnWeeks;
    }

    public String getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(String pickupTime) {
        entityMap.put("pickupTime", pickupTime);
        this.pickupTime = pickupTime;
    }

    public String getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(String returnTime) {
        entityMap.put("returnTime", returnTime);
        this.returnTime = returnTime;
    }

    public String getMaxUserHour() {
        return maxUserHour;
    }

    public void setMaxUserHour(String maxUserHour) {
        entityMap.put("maxUserHour", maxUserHour);
        this.maxUserHour = maxUserHour;
    }

    /** 机构免押金 */
    public Integer getExemptDeposit() {
        return exemptDeposit;
    }

    /** 机构免押金 */
    public void setExemptDeposit(Integer exemptDeposit) {
        entityMap.put("exemptDeposit", exemptDeposit);
        this.exemptDeposit = exemptDeposit;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        entityMap.put("contact", contact);
        this.contact = contact;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        entityMap.put("tel", tel);
        this.tel = tel;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        entityMap.put("mobilePhone", mobilePhone);
        this.mobilePhone = mobilePhone;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        entityMap.put("mail", mail);
        this.mail = mail;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        entityMap.put("licenseNo", licenseNo);
        this.licenseNo = licenseNo;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        entityMap.put("fax", fax);
        this.fax = fax;
    }

    public String getLicenseNoImgUrl() {
        return licenseNoImgUrl;
    }

    public void setLicenseNoImgUrl(String licenseNoImgUrl) {
        entityMap.put("licenseNoImgUrl", licenseNoImgUrl);
        this.licenseNoImgUrl = licenseNoImgUrl;
    }

    public String getContractImgUrl() {
        return contractImgUrl;
    }

    public void setContractImgUrl(String contractImgUrl) {
        entityMap.put("contractImgUrl", contractImgUrl);
        this.contractImgUrl = contractImgUrl;
    }

    public String getTaxRegistrationImgUrl() {
        return taxRegistrationImgUrl;
    }

    public void setTaxRegistrationImgUrl(String taxRegistrationImgUrl) {
        entityMap.put("taxRegistrationImgUrl", taxRegistrationImgUrl);
        this.taxRegistrationImgUrl = taxRegistrationImgUrl;
    }

    public String getOrgCodeImgUrl() {
        return orgCodeImgUrl;
    }

    public void setOrgCodeImgUrl(String orgCodeImgUrl) {
        entityMap.put("orgCodeImgUrl", orgCodeImgUrl);
        this.orgCodeImgUrl = orgCodeImgUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        entityMap.put("remark", remark);
        this.remark = remark;
    }

    public Double getPayWay() {
        return payWay;
    }

    public void setPayWay(Double payWay) {
        entityMap.put("payWay", payWay);
        this.payWay = payWay;
    }

    public Double getDeposit() {
        return deposit;
    }

    public void setDeposit(Double deposit) {
        entityMap.put("deposit", deposit);
        this.deposit = deposit;
    }

    public Double getRentMins() {
        return rentMins;
    }

    public void setRentMins(Double rentMins) {
        entityMap.put("rentMins", rentMins);
        this.rentMins = rentMins;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        entityMap.put("checkDate", checkDate);
        this.checkDate = checkDate;
    }

    public Integer getCheckAlert() {
        return checkAlert;
    }

    public void setCheckAlert(Integer checkAlert) {
        entityMap.put("checkAlert", checkAlert);
        this.checkAlert = checkAlert;
    }

    /** 企业性质：0（外部）1（内部） */
    public String getOrgProperty() {
        return orgProperty;
    }

    /** 企业性质：0（外部）1（内部） */
    public void setOrgProperty(String orgProperty) {
        entityMap.put("orgProperty", orgProperty);
        this.orgProperty = orgProperty;
    }

    public Integer getInsideFlag() {
        return insideFlag;
    }

    public void setInsideFlag(Integer insideFlag) {
        entityMap.put("insideFlag", insideFlag);
        this.insideFlag = insideFlag;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        entityMap.put("address", address);
        this.address = address;
    }

    public Date getLastRemindDate() {
        return lastRemindDate;
    }

    public void setLastRemindDate(Date lastRemindDate) {
        entityMap.put("lastRemindDate", lastRemindDate);
        this.lastRemindDate = lastRemindDate;
    }

    /** redis中存放的Id生成的key：每日自增 */
    public static String idWorkKey() {
        return String.format("idwork_%s_%s", DateTimeUtils.getCurrentDateToString(DateTimeUtils.SHOT_DATE_FORMAT), "agency_info");
    }

    public Date getCooperateStartTime() {
        return cooperateStartTime;
    }

    public void setCooperateStartTime(Date cooperateStartTime) {
        entityMap.put("cooperateStartTime", cooperateStartTime);
        this.cooperateStartTime = cooperateStartTime;
    }

    public Date getCooperateEndTime() {
        return cooperateEndTime;
    }

    public void setCooperateEndTime(Date cooperateEndTime) {
        entityMap.put("cooperateEndTime", cooperateEndTime);
        this.cooperateEndTime = cooperateEndTime;
    }

    public Integer getCooperateStatus() {
        return cooperateStatus;
    }

    public void setCooperateStatus(Integer cooperateStatus) {
        entityMap.put("cooperateStatus", cooperateStatus);
        this.cooperateStatus = cooperateStatus;
    }

    public Double getVehicleThreshold() {
        return vehicleThreshold;
    }

    public void setVehicleThreshold(Double vehicleThreshold) {
        entityMap.put("vehicleThreshold", vehicleThreshold);
        this.vehicleThreshold = vehicleThreshold;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        entityMap.put("discountId", discountId);
        this.discountId = discountId;
    }

    public Long getDiscountPersonalId() {
        return discountPersonalId;
    }

    public void setDiscountPersonalId(Long discountPersonalId) {
        entityMap.put("discountPersonalId", discountPersonalId);
        this.discountPersonalId = discountPersonalId;
    }

    public Double getLineLimitMonthly() {
        return lineLimitMonthly;
    }

    public void setLineLimitMonthly(Double lineLimitMonthly) {
        entityMap.put("lineLimitMonthly", lineLimitMonthly);
        this.lineLimitMonthly = lineLimitMonthly;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        entityMap.put("appKey", appKey);
        this.appKey = appKey;
    }

    public Integer getDiscountRule() {
        entityMap.put("discountRule", discountRule);
        return discountRule;
    }

    public void setDiscountRule(Integer discountRule) {
        this.discountRule = discountRule;
    }

    public BigDecimal getMaxUnitPrice() {
        entityMap.put("maxUnitPrice", maxUnitPrice);
        return maxUnitPrice;
    }

    public void setMaxUnitPrice(BigDecimal maxUnitPrice) {
        this.maxUnitPrice = maxUnitPrice;
    }

    public String getOrgName() {
        entityMap.put("orgName", orgName);
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        entityMap.put("vehicleNo", vehicleNo);
        this.vehicleNo = vehicleNo;
    }
}