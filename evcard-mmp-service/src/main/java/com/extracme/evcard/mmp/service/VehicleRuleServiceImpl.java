package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.mmp.common.HttpUtils;
import com.extracme.evcard.mmp.common.PropertyUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 用车规则服务实现类
 */
@Slf4j
@Service
public class VehicleRuleServiceImpl implements IVehicleRuleService {

    @Override
    public BaseResponse createDefaultVehicleRule(String agencyId, Long operatorId, String operatorName) {
        log.info("开始创建默认用车规则，企业ID：{}，操作人：{}", agencyId, operatorName);
        
        if (StringUtils.isEmpty(agencyId)) {
            return new BaseResponse(-1, "企业ID不能为空");
        }
        
        try {
            // 构建默认用车规则参数
            Map<String, Object> paramMap = buildDefaultVehicleRuleParams(agencyId, operatorId, operatorName);
            
            // 调用BVM系统创建用车规则接口
            String url = PropertyUtils.getProperty("url.createDefaultAgencyRole");
            if (StringUtils.isEmpty(url)) {
                log.warn("未配置创建用车规则的URL，使用模拟创建");
                return simulateCreateVehicleRule(agencyId);
            }
            
            JSONObject result = HttpUtils.httpPostWithJSON(url, paramMap);
            log.info("调用BVM创建用车规则接口返回：{}", JSON.toJSONString(result));
            
            if (result != null && "0".equals(String.valueOf(result.get("code")))) {
                String agencyRoleId = result.getString("data");
                log.info("创建默认用车规则成功，企业ID：{}，规则ID：{}", agencyId, agencyRoleId);
                return new BaseResponse(0, "创建默认用车规则成功", agencyRoleId);
            } else {
                String errorMsg = result != null ? result.getString("message") : "调用BVM接口失败";
                log.error("创建默认用车规则失败，企业ID：{}，错误信息：{}", agencyId, errorMsg);
                return new BaseResponse(-1, "创建默认用车规则失败：" + errorMsg);
            }
            
        } catch (Exception e) {
            log.error("创建默认用车规则异常，企业ID：{}", agencyId, e);
            return new BaseResponse(-1, "创建默认用车规则异常：" + e.getMessage());
        }
    }

    @Override
    public BaseResponse updateToDefaultVehicleRule(String agencyId, String agencyRoleId, Long operatorId, String operatorName) {
        log.info("开始更新用车规则为默认规则，企业ID：{}，规则ID：{}，操作人：{}", agencyId, agencyRoleId, operatorName);
        
        if (StringUtils.isEmpty(agencyId) || StringUtils.isEmpty(agencyRoleId)) {
            return new BaseResponse(-1, "企业ID和规则ID不能为空");
        }
        
        try {
            // 构建更新用车规则参数
            Map<String, Object> paramMap = buildUpdateVehicleRuleParams(agencyId, agencyRoleId, operatorId, operatorName);
            
            // 调用BVM系统更新用车规则接口
            String url = PropertyUtils.getProperty("url.updateAgencyRoleToDefault");
            if (StringUtils.isEmpty(url)) {
                log.warn("未配置更新用车规则的URL，使用模拟更新");
                return simulateUpdateVehicleRule(agencyId, agencyRoleId);
            }
            
            JSONObject result = HttpUtils.httpPostWithJSON(url, paramMap);
            log.info("调用BVM更新用车规则接口返回：{}", JSON.toJSONString(result));
            
            if (result != null && "0".equals(String.valueOf(result.get("code")))) {
                log.info("更新用车规则为默认规则成功，企业ID：{}，规则ID：{}", agencyId, agencyRoleId);
                return new BaseResponse(0, "更新用车规则为默认规则成功");
            } else {
                String errorMsg = result != null ? result.getString("message") : "调用BVM接口失败";
                log.error("更新用车规则为默认规则失败，企业ID：{}，规则ID：{}，错误信息：{}", agencyId, agencyRoleId, errorMsg);
                return new BaseResponse(-1, "更新用车规则为默认规则失败：" + errorMsg);
            }
            
        } catch (Exception e) {
            log.error("更新用车规则为默认规则异常，企业ID：{}，规则ID：{}", agencyId, agencyRoleId, e);
            return new BaseResponse(-1, "更新用车规则为默认规则异常：" + e.getMessage());
        }
    }

    @Override
    public boolean hasVehicleRule(String agencyId) {
        log.debug("检查企业是否存在用车规则，企业ID：{}", agencyId);
        
        if (StringUtils.isEmpty(agencyId)) {
            return false;
        }
        
        try {
            // 调用BVM系统查询企业用车规则接口
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("agencyId", agencyId);
            
            String url = PropertyUtils.getProperty("url.getAgencyRoleList");
            if (StringUtils.isEmpty(url)) {
                log.warn("未配置查询用车规则的URL，返回false");
                return false;
            }
            
            JSONObject result = HttpUtils.httpPostWithJSON(url, paramMap);
            log.debug("调用BVM查询用车规则接口返回：{}", JSON.toJSONString(result));
            
            if (result != null && "0".equals(String.valueOf(result.get("code")))) {
                Object data = result.get("data");
                // 如果返回的数据不为空且包含规则，则认为存在用车规则
                return data != null && !data.toString().equals("[]");
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("检查企业用车规则异常，企业ID：{}", agencyId, e);
            return false;
        }
    }

    @Override
    public String getDefaultVehicleRuleId(String agencyId) {
        log.debug("获取企业的默认用车规则ID，企业ID：{}", agencyId);
        
        if (StringUtils.isEmpty(agencyId)) {
            return null;
        }
        
        try {
            // 调用BVM系统查询企业默认用车规则接口
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("agencyId", agencyId);
            
            String url = PropertyUtils.getProperty("url.getDefaultAgencyRole");
            if (StringUtils.isEmpty(url)) {
                log.warn("未配置查询默认用车规则的URL，返回null");
                return null;
            }
            
            JSONObject result = HttpUtils.httpPostWithJSON(url, paramMap);
            log.debug("调用BVM查询默认用车规则接口返回：{}", JSON.toJSONString(result));
            
            if (result != null && "0".equals(String.valueOf(result.get("code")))) {
                return result.getString("data");
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("获取企业默认用车规则ID异常，企业ID：{}", agencyId, e);
            return null;
        }
    }

    /**
     * 构建默认用车规则参数
     */
    private Map<String, Object> buildDefaultVehicleRuleParams(String agencyId, Long operatorId, String operatorName) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agencyId", agencyId);
        paramMap.put("roleName", "默认用车规则");
        paramMap.put("exemptionLevel", 0); // 免押金等级：0
        paramMap.put("businessPayFlag", 0); // 是否企业支付：0-否
        paramMap.put("personalPayFlag", 1); // 是否个人支付：1-是
        paramMap.put("personalRestrictFlag", 0); // 是否个人限制：0-否
        paramMap.put("restrictTimeFlag", 0); // 是否限制用车时间段：0-否
        paramMap.put("restrictPickupShopFlag", 0); // 是否限制取车网点：0-否
        paramMap.put("restrictReturnShopFlag", 0); // 是否限制还车网点：0-否
        paramMap.put("vehicleModelRestrictFlag", 0); // 是否限制车型价格：0-否
        paramMap.put("orderMaxRestrictFlag", 0); // 是否限制单笔订单金额上限：0-否
        paramMap.put("operatorId", operatorId);
        paramMap.put("operatorName", operatorName);
        return paramMap;
    }

    /**
     * 构建更新用车规则参数
     */
    private Map<String, Object> buildUpdateVehicleRuleParams(String agencyId, String agencyRoleId, Long operatorId, String operatorName) {
        Map<String, Object> paramMap = buildDefaultVehicleRuleParams(agencyId, operatorId, operatorName);
        paramMap.put("agencyRoleId", agencyRoleId);
        return paramMap;
    }

    /**
     * 模拟创建用车规则（当未配置BVM接口时使用）
     */
    private BaseResponse simulateCreateVehicleRule(String agencyId) {
        log.info("模拟创建默认用车规则，企业ID：{}", agencyId);
        // 生成一个模拟的规则ID
        String mockRuleId = "MOCK_RULE_" + agencyId + "_" + System.currentTimeMillis();
        return new BaseResponse(0, "模拟创建默认用车规则成功", mockRuleId);
    }

    /**
     * 模拟更新用车规则（当未配置BVM接口时使用）
     */
    private BaseResponse simulateUpdateVehicleRule(String agencyId, String agencyRoleId) {
        log.info("模拟更新用车规则为默认规则，企业ID：{}，规则ID：{}", agencyId, agencyRoleId);
        return new BaseResponse(0, "模拟更新用车规则为默认规则成功");
    }
}
