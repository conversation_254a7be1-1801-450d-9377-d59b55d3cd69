<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- MmpPackNightActivityMapper，，对应表mmp_pack_night_activity -->
<mapper namespace="com.extracme.evcard.mmp.dao.MmpPackNightActivityMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.mmp.model.MmpPackNightActivity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="activity_status" jdbcType="BIGINT" property="activityStatus"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="activity_start_date" jdbcType="TIMESTAMP" property="activityStartDate"/>
        <result column="activity_end_date" jdbcType="TIMESTAMP" property="activityEndDate"/>
        <result column="activity_start_time" jdbcType="VARCHAR" property="activityStartTime"/>
        <result column="activity_end_time" jdbcType="VARCHAR" property="activityEndTime"/>
        <result column="activity_license_plate" jdbcType="VARCHAR" property="activityLicensePlate"/>
        <result column="activity_weeds" jdbcType="VARCHAR" property="activityWeeds"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_oper_id" jdbcType="BIGINT" property="createOperId"/>
        <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId"/>
        <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="third_activity_id" jdbcType="BIGINT" property="thirdActivityId"/>
        <result column="quota_org_id" jdbcType="VARCHAR" property="quotaOrgId"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
        <result column="org_ids" jdbcType="VARCHAR" property="orgIds"/>
        <result column="sms_content" jdbcType="VARCHAR" property="smsContent"/>
        <result column="template_type" jdbcType="VARCHAR" property="templateType"/>
    </resultMap>

    <!--数据列-->
    <sql id="Base_Column_List">
            id,
            activity_name,
            activity_status,
            org_id,
            activity_start_date,
            activity_end_date,
            activity_start_time,
            activity_end_time,
            activity_license_plate,
            activity_weeds,
            remark,
            create_time,
            create_oper_id,
            create_oper_name,
            update_time,
            update_oper_id,
            update_oper_name,
            type,
            third_activity_id,
            quota_org_id,group_id,org_ids,
            sms_content
    </sql>

    <!-- 保存数据 -->
    <insert id="save" parameterType="com.extracme.evcard.mmp.model.MmpPackNightActivity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ${mmpSchema}.mmp_pack_night_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id!=null">id,</if>
            <if test="activityName!=null">activity_name,</if>
            <if test="activityStatus!=null">activity_status,</if>
            <if test="orgId!=null">org_id,</if>
            <if test="activityStartDate!=null">activity_start_date,</if>
            <if test="activityEndDate!=null">activity_end_date,</if>
            <if test="activityStartTime!=null">activity_start_time,</if>
            <if test="activityEndTime!=null">activity_end_time,</if>
            <if test="activityLicensePlate!=null">activity_license_plate,</if>
            <if test="activityWeeds!=null">activity_weeds,</if>
            <if test="remark!=null">remark,</if>
            <if test="createTime!=null">create_time,</if>
            <if test="createOperId!=null">create_oper_id,</if>
            <if test="createOperName!=null">create_oper_name,</if>
            <if test="updateTime!=null">update_time,</if>
            <if test="updateOperId!=null">update_oper_id,</if>
            <if test="updateOperName!=null">update_oper_name,</if>
            <if test="type!=null">type,</if>
            <if test="thirdActivityId!=null">third_activity_id,</if>
            <if test="password!=null">password,</if>
            <if test="groupId!=null">group_id,</if>
            <if test="quotaOrgId!=null">quota_org_id,</if>
            <if test="orgIds!=null">org_ids,</if>
            <if test="smsContent!=null">sms_content,</if>
            <if test="templateType!=null">template_type</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!=null">#{id,jdbcType=BIGINT},</if>
            <if test="activityName!=null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="activityStatus!=null">#{activityStatus,jdbcType=BIGINT},</if>
            <if test="orgId!=null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="activityStartDate!=null">#{activityStartDate,jdbcType=TIMESTAMP},</if>
            <if test="activityEndDate!=null">#{activityEndDate,jdbcType=TIMESTAMP},</if>
            <if test="activityStartTime!=null">#{activityStartTime,jdbcType=VARCHAR},</if>
            <if test="activityEndTime!=null">#{activityEndTime,jdbcType=VARCHAR},</if>
            <if test="activityLicensePlate!=null">#{activityLicensePlate,jdbcType=VARCHAR},</if>
            <if test="activityWeeds!=null">#{activityWeeds,jdbcType=VARCHAR},</if>
            <if test="remark!=null">#{remark,jdbcType=VARCHAR},</if>
            <if test="createTime!=null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="createOperId!=null">#{createOperId,jdbcType=BIGINT},</if>
            <if test="createOperName!=null">#{createOperName,jdbcType=VARCHAR},</if>
            <if test="updateTime!=null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateOperId!=null">#{updateOperId,jdbcType=BIGINT},</if>
            <if test="updateOperName!=null">#{updateOperName,jdbcType=VARCHAR},</if>
            <if test="type!=null">#{type,jdbcType=INTEGER},</if>
            <if test="thirdActivityId!=null">#{thirdActivityId,jdbcType=BIGINT},</if>
            <if test="password!=null">#{password,jdbcType=VARCHAR},</if>
            <if test="groupId!=null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="quotaOrgId!=null">#{quotaOrgId,jdbcType=VARCHAR},</if>
            <if test="orgIds!=null">#{orgIds,jdbcType=VARCHAR},</if>
            <if test="smsContent!=null">#{smsContent,jdbcType=VARCHAR},</if>
            <if test="templateType!=null">#{templateType,jdbcType=INTEGER}</if>
        </trim>
    </insert>

    <!-- 根据主键取得数据 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${locationSchema}.mmp_pack_night_activity where id = #{id}
    </select>

    <!--分页获取获取所有数据，分页使用(实际项目中需要自己改造，自己需要几个条件则添加几个条件)-->
    <select id="selectAllPage" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from ${locationSchema}.mmp_pack_night_activity where status = 1
    </select>

    <!-- 根据主键删除数据 -->
    <delete id="deleteById" parameterType="long">
        delete from ${locationSchema}.mmp_pack_night_activity where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 更新数据 -->
    <update id="updateByIdSelective" parameterType="com.extracme.evcard.mmp.model.MmpPackNightActivity">
        update ${mmpSchema}.mmp_pack_night_activity
        <set>
            <if test="entityMap.activityName != null">
                activity_name = #{entityMap.activityName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.activityStatus != null">
                activity_status = #{entityMap.activityStatus,jdbcType=BIGINT},
            </if>
            <if test="entityMap.orgId != null">
                org_id = #{entityMap.orgId,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.groupId != null">
                group_id = #{entityMap.groupId,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.activityStartDate != null">
                activity_start_date = #{entityMap.activityStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.activityEndDate != null">
                activity_end_date = #{entityMap.activityEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.activityStartTime != null">
                activity_start_time = #{entityMap.activityStartTime,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.activityEndTime != null">
                activity_end_time = #{entityMap.activityEndTime,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.activityLicensePlate != null">
                activity_license_plate = #{entityMap.activityLicensePlate,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.activityWeeds != null">
                activity_weeds = #{entityMap.activityWeeds,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.remark != null">
                remark = #{entityMap.remark,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.createOperId != null">
                create_oper_id = #{entityMap.createOperId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.createOperName != null">
                create_oper_name = #{entityMap.createOperName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.updateTime != null">
                update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.updateOperId != null">
                update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.updateOperName != null">
                update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR}
            </if>
            <if test="entityMap.orgIds != null">
                org_ids = #{entityMap.orgIds,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.smsContent != null">
                sms_content = #{entityMap.smsContent,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${locationSchema}.mmp_pack_night_activity (
        id,
        activity_name,
        activity_status,
        org_id,
        activity_start_date,
        activity_end_date,
        activity_start_time,
        activity_end_time,
        activity_license_plate,
        activity_weeds,
        remark,
        sms_content,
        create_time,
        create_oper_id,
        create_oper_name,
        update_time,
        update_oper_id,
        update_oper_name
        )
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT
            #{item.id,jdbcType=BIGINT},
            #{item.activityName,jdbcType=VARCHAR},
            #{item.activityStatus,jdbcType=BIGINT},
            #{item.orgId,jdbcType=VARCHAR},
            #{item.activityStartDate,jdbcType=TIMESTAMP},
            #{item.activityEndDate,jdbcType=TIMESTAMP},
            #{item.activityStartTime,jdbcType=VARCHAR},
            #{item.activityEndTime,jdbcType=VARCHAR},
            #{item.activityLicensePlate,jdbcType=VARCHAR},
            #{item.activityWeeds,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.smsContent,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createOperId,jdbcType=BIGINT},
            #{item.createOperName,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateOperId,jdbcType=BIGINT},
            #{item.updateOperName,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 单条逻辑删除 -->
    <update id="logicalSelectById">
        update ${locationSchema}.mmp_pack_night_activity set status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR} where id = #{id,jdbcType=BIGINT} and status = 1
     </update>

    <!-- 批量逻辑删除 -->
    <update id="batchLogicalSelectById" parameterType="java.util.List">
        update ${locationSchema}.mmp_pack_night_activity set status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP}
        , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR} where
        id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            ${item}
        </foreach>
        and status = 1
    </update>
    <select id="queryNightCarInfoList" resultType="com.extracme.evcard.mmp.dto.NightCarInfoDTO">
        SELECT
        a.id AS id,
        a.activity_name AS activityName,
        CASE a.activity_status
        WHEN 0 THEN '待发布'
        WHEN 1 THEN '待上线'
        WHEN 2 THEN '进行中'
        WHEN 3 THEN '已停止'
        WHEN 4 THEN '已暂停'
        end as activityStatus,
        b.ORG_NAME as orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.type as type,
        a.create_oper_name as createOperName,
        a.sign_id as signId,
        c.activity_url AS activityUrl,
 		c.activity_channel_key AS activityChannelKey,
        a.org_ids orgIds
        FROM
        ${mmpSchema}.mmp_pack_night_activity a LEFT JOIN ${isvSchema}.org_info b ON a.org_id=b.ORG_ID
        LEFT JOIN ${mmpSchema}.mmp_third_activity c ON a.third_activity_id = c.id
        WHERE 1=1
        <if test="id!=null and id!=''">
            and a.id= #{id}
        </if>
        <if test="name!=null and name!=''">
            and a.activity_name like concat('%',#{name},'%')
        </if>
        <if test="activityStatus!=null">
            and a.activity_status= #{activityStatus}
        </if>
        <if test="orgId!=null and orgId!=''">
            and (a.org_id=#{orgId}
            or find_in_set(#{orgId},a.org_ids))
        </if>
        <if test="ids!=null and ids!='' and ids!='00'">
            <if test="orgId==null or orgId==''">
                and find_in_set(#{ids},a.org_ids)
            </if>
        </if>
        <if test="createdStartTime!=null and createdStartTime!=''">
            and a.activity_end_date &gt;=#{createdStartTime}
        </if>

        <if test="createdEndTime!=null and createdEndTime!=''">
            and a.activity_start_date &lt;=#{createdEndTime}
        </if>

        <if test="createOperName!=null and createOperName!=''">
            and a.create_oper_name like concat('%',#{createOperName},'%')
        </if>
        <if test=" type != null ">
            and a.type = #{type}
        </if>
        ORDER BY a.create_time DESC
    </select>
    <update id="updateById" parameterType="java.util.List">
         UPDATE ${mmpSchema}.mmp_pack_night_activity set activity_end_date=#{date},remark=#{remark} WHERE id=#{id} and activity_status=2
      </update>
    <delete id="deleteNightCarInfo" parameterType="long">
         DELETE from ${mmpSchema}.mmp_pack_night_activity WHERE id=#{id} AND
         activity_status in (0, 1)
      </delete>
    <select id="queryNightCar" parameterType="long" resultType="com.extracme.evcard.mmp.dto.NightCarActivityDTO">
             SELECT
                a.id as id,
                a.activity_name AS activityName,
                CASE a.activity_status
            WHEN 1 THEN
                '未开始'
            WHEN 2 THEN
                '进行中'
            WHEN 3 THEN
                '已停止'
            END AS activityStatus,
             b.ORG_NAME AS orgName,
             a.activity_start_date AS activityStartDate,
             a.activity_end_date AS activityEndDate,
             a.activity_license_plate as activityLicensePlate,
             a.activity_weeds AS activityWeeds,
             a.activity_start_time as activityStartTime,
             a.activity_end_time AS activityEndTime,
             a.remark as remark
            FROM 
                ${mmpSchema}.mmp_pack_night_activity a
            LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
            WHERE
                a.id =#{id}
      </select>
    <!--活动上线  -->
    <update id="updateActivityStatus" parameterType="int">
        UPDATE iss.mmp_pack_night_activity SET activity_status=2
        WHERE
        <foreach collection="list" item="id" separator="or">
            id =#{id}
        </foreach>

    </update>

    <!--活动下线 -->
    <update id="updateActivityStatusCompleted" parameterType="int">
        UPDATE iss.mmp_pack_night_activity SET activity_status=3
        WHERE
        <foreach collection="list" item="id" separator="or">
            id =#{id}
        </foreach>
    </update>


    <!-- 晒刷出没开始 -->
    <select id="selectByStatusStart" parameterType="int" resultType="java.lang.Integer">
             select id FROM iss.mmp_pack_night_activity WHERE
             activity_status =#{activityStatus}
             and activity_start_date=date_format(now(),'%y-%m-%d')
             AND type=0
       </select>
    <!--筛选出已开始的  -->
    <select id="selectByStatusCompleted" parameterType="int" resultType="java.lang.Integer">
                select id FROM iss.mmp_pack_night_activity WHERE
             activity_status =#{activityStatus}
             and activity_end_date=date_sub(curdate(),interval 1 day)
             AND type=0
       </select>
    <select id="queryActivityDate" parameterType="string" resultType="com.extracme.evcard.mmp.dto.NightCarInfoDTO">
        SELECT
            a.id AS id,
            a.activity_name AS activityName,
            CASE a.activity_status
        WHEN 1 THEN'未开始'
        WHEN 2 THEN'进行中'
        WHEN 3 THEN'已停止'
        end as activityStatus,
                a.org_id,
        b.ORG_NAME as orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        GROUP_CONCAT(d.vehicle_model_seq) as vehicle
      
        FROM
       iss.mmp_pack_night_activity a INNER JOIN isv.org_info b  ON a.org_id=b.ORG_ID
       INNER JOIN iss.mmp_activity_type c ON a.id = c.pack_night_activity_id
       INNER JOIN iss.mmp_activity_vehicle_model d ON d.activity_type_id = c.id
       WHERE a.org_id=#{orgId} GROUP BY a.id
       </select>
    <select id="queryActivityDate1" parameterType="java.util.List"
            resultType="com.extracme.evcard.mmp.dto.NightCarInfoDTO">
        SELECT
            a.id AS id,
            a.activity_name AS activityName,
            CASE a.activity_status
        WHEN 1 THEN'未开始'
        WHEN 2 THEN'进行中'
        WHEN 3 THEN'已停止'
        end as activityStatus,
                a.org_id,
        b.ORG_NAME as orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        GROUP_CONCAT(d.vehicle_model_seq) as vehicle
      
        FROM
       iss.mmp_pack_night_activity a INNER JOIN isv.org_info b  ON a.org_id=b.ORG_ID
       INNER JOIN iss.mmp_activity_type c ON a.id = c.pack_night_activity_id
       INNER JOIN iss.mmp_activity_vehicle_model d ON d.activity_type_id = c.id
       WHERE a.org_id=#{orgId} and a.id!=#{id} GROUP BY a.id
       </select>
    <select id="queryNightCarActivityList" resultType="com.extracme.evcard.mmp.dto.ActivityNameDTO">
          SELECT DISTINCT activity_name as name from ${mmpSchema}.mmp_pack_night_activity  order by id  desc
              limit 200
    </select>
    <select id="queryLicensePlate" parameterType="java.util.List"
            resultType="com.extracme.evcard.mmp.dto.NightCarActivityDTO">
           SELECT
                a.id,
                a.activity_name AS activityName,
                CASE a.activity_status
            WHEN 1 THEN
                '未开始'
            WHEN 2 THEN
                '进行中'
            WHEN 3 THEN
                '已停止'
            END AS activityStatus,
             b.ORG_NAME AS orgName,
             a.activity_start_date AS activityStartDate,
             a.activity_end_date AS activityEndDate,
             a.activity_license_plate as activityLicensePlate,
             a.activity_weeds AS activityWeeds,
             a.activity_start_time as activityStartTime,
             a.activity_end_time AS activityEndTime
        FROM
       iss.mmp_pack_night_activity a LEFT JOIN isv.org_info b  ON a.org_id=b.ORG_ID
       WHERE a.org_id!=#{orgId} and a.activity_start_date &lt;=#{date1} and a.activity_end_date &gt;=#{date}
       </select>
    <select id="queryLicensePlate1" parameterType="java.util.List"
            resultType="com.extracme.evcard.mmp.dto.NightCarActivityDTO">
           SELECT
                a.id,
                a.activity_name AS activityName,
                CASE a.activity_status
            WHEN 1 THEN
                '未开始'
            WHEN 2 THEN
                '进行中'
            WHEN 3 THEN
                '已停止'
            END AS activityStatus,
             b.ORG_NAME AS orgName,
             a.activity_start_date AS activityStartDate,
             a.activity_end_date AS activityEndDate,
             a.activity_license_plate as activityLicensePlate,
             a.activity_weeds AS activityWeeds,
             a.activity_start_time as activityStartTime,
             a.activity_end_time AS activityEndTime
        FROM
       iss.mmp_pack_night_activity a LEFT JOIN isv.org_info b  ON a.org_id=b.ORG_ID
       WHERE a.org_id!=#{orgId} and a.activity_start_date &lt;=#{date1} and a.activity_end_date &gt;=#{date} and a.id!=#{id}
       </select>

    <select id="queryVehicleModel" resultType="com.extracme.evcard.mmp.dto.VehicleModel">
           SELECT
            VEHICLE_MODEL_SEQ AS vehicleModelSeq,
            VEHICLE_MODEL_INFO AS vehicleModelName
            FROM
            isv.vehicle_model
       </select>

    <select id="selectThirdActivityById" resultType="com.extracme.evcard.mmp.dto.ThirdActivityDetailDTO">
          SELECT
            a.id,
            a.activity_name AS activityName,
            a.remark as remark,
            CASE a.activity_status
                WHEN 0 THEN '待发布'
                WHEN 1 THEN '待上线'
                WHEN 2 THEN '进行中'
                WHEN 3 THEN '已停止'
                WHEN 4 THEN '已暂停'
                END AS activityStatusName,
             b.ORG_NAME AS orgName,
             a.activity_start_date AS activityStartDate,
             a.activity_end_date AS activityEndDate,
             a.type as type,
             a.third_activity_id as thirdActivityId,
             a.org_id as orgId,
             a.activity_status as activityStatus,
             a.sign_id as signId
            FROM ${mmpSchema}.mmp_pack_night_activity a
            LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
            WHERE a.id = #{id}
    </select>


    <select id="selectEActivityById" resultType="com.extracme.evcard.mmp.dto.EActivityDetailDTO">
        SELECT
        a.id,
        a.sms_content AS smsContent,
        a.activity_name AS activityName,
        a.remark as remark,
        CASE a.activity_status
        WHEN 0 THEN '待发布'
        WHEN 1 THEN '待上线'
        WHEN 2 THEN '进行中'
        WHEN 3 THEN '已停止'
        WHEN 4 THEN '已暂停'
        END AS activityStatusName,
        b.ORG_NAME AS orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.type as type,
        a.third_activity_id as thirdActivityId,
        a.org_id as orgId,
        a.org_ids as orgIds,
        a.activity_status as activityStatus
        FROM ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        WHERE a.id = #{id}
    </select>


    <update id="updateActivitySuspendStatus">
        UPDATE
        ${mmpSchema}.mmp_pack_night_activity
        SET activity_end_date = curdate() , activity_status = 3, update_time = now(),
            update_oper_id = #{createOperId}, update_oper_name = #{createOperName}
        WHERE  id = #{id} AND activity_status in ('1','2','4')
    </update>

    <update id="updateActivityResumeStatus">
        UPDATE
        ${mmpSchema}.mmp_pack_night_activity
        SET activity_status = 2, update_time = now(),
        update_oper_id = #{createOperId}, update_oper_name = #{createOperName}
        WHERE  id = #{id} AND activity_status = 4
    </update>

    <update id="updateActivityPauseStatus">
        UPDATE
        ${mmpSchema}.mmp_pack_night_activity
        SET activity_status = 4, update_time = now(),
        update_oper_id = #{createOperId}, update_oper_name = #{createOperName}
        WHERE  id = #{id} AND activity_status = 2
    </update>

    <select id="selectThirdActivityByStatusStart" resultType="integer">
         select id
         FROM  ${mmpSchema}.mmp_pack_night_activity
         WHERE activity_status = 1
         and activity_start_date = curdate()
         AND type =1
    </select>

    <select id="selectThirdActivityByStatusCompleted" resultType="integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE (activity_status = 2 or activity_status = 4)
        and activity_end_date = date_sub(curdate(),interval 1 day)
        AND type =1
    </select>

    <update id="updateActivityImmediateStartStatus">
          UPDATE
        ${mmpSchema}.mmp_pack_night_activity
        SET activity_start_date = curdate() , activity_status = 2, update_time = now(),
            update_oper_id = #{createOperId}, update_oper_name = #{createOperName}
        WHERE  id = #{id} AND activity_status in (0,1)
    </update>

    <update id="updateActivityPublishStatus">
          UPDATE
        ${mmpSchema}.mmp_pack_night_activity
        SET activity_status = 1, update_time = now(),
            update_oper_id = #{createOperId}, update_oper_name = #{createOperName}
        WHERE  id = #{id} AND activity_status = 0
    </update>


    <!-- 更新活动数据 与更新包夜车活动分开  -->
    <update id="updateActivityByIdSelective" parameterType="com.extracme.evcard.mmp.model.MmpPackNightActivity"
            useGeneratedKeys="true" keyProperty="id">
        update ${mmpSchema}.mmp_pack_night_activity
        <set>
            <if test="entityMap.activityName != null">
                activity_name = #{entityMap.activityName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.orgId != null">
                org_id = #{entityMap.orgId,jdbcType=VARCHAR},
            </if>
             <if test="entityMap.remark != null">
                remark = #{entityMap.remark,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.activityStatus != null">
                activity_status = #{entityMap.activityStatus,jdbcType=BIGINT},
            </if>
            <if test="entityMap.activityStartDate != null">
                activity_start_date = #{entityMap.activityStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.activityEndDate != null">
                activity_end_date = #{entityMap.activityEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.activityStartTime != null">
                activity_start_time = #{entityMap.activityStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.activityEndTime != null">
                activity_end_time = #{entityMap.activityEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.updateTime != null">
                update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.updateOperId != null">
                update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.updateOperName != null">
                update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.password != null">
                password = #{entityMap.password,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.groupId != null">
                group_id = #{entityMap.groupId,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.templateFileUrl != null">
                template_file_url = #{entityMap.templateFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.quotaOrgId != null">
                quota_org_id = #{entityMap.quotaOrgId,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.orgIds != null">
                org_ids = #{entityMap.orgIds,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.smsContent != null">
                sms_content = #{entityMap.smsContent,jdbcType=VARCHAR}
            </if>
            <if test="entityMap.templateType != null">
                template_type = #{entityMap.templateType,jdbcType=BIGINT}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectEActivityByOrgId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = 3 AND activity_status = 2
        AND concat(',', org_ids, ',') like concat('%,',#{orgId},',%')
    </select>

    <select id="selectEActivityByStatusStart" resultType="integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status = 1
        and activity_start_date = curdate()
        AND type =3
    </select>

    <select id="selectEActivityByStatusCompleted" resultType="integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE (activity_status = 2 or activity_status = 4)
        and activity_end_date = date_sub(curdate(),interval 1 day)
        AND type =3
    </select>

    <update id="updateSignActivityIdById">
        UPDATE ${mmpSchema}.mmp_pack_night_activity
        SET sign_id = #{signActivityId}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectActivityOrgIdByChannelKey" resultType="integer">
        SELECT
        count(1)
        FROM
        ${mmpSchema}.mmp_pack_night_activity m
        LEFT JOIN ${mmpSchema}.mmp_third_activity t ON m.third_activity_id = t.id
        AND m.activity_status != 3 AND m.type = 4
        WHERE
        t.activity_channel_key = #{activityChannelKey}
        <if test=" orgId == '00' ">
            and m.org_id = '00'
        </if>
        <if test=" orgId != '00' ">
            and m.org_id != '00'
        </if>
        <if test=" id != null ">
            AND m.id != #{id}
        </if>

    </select>

    <select id="selectChannelRewardActivityById"
            resultType="com.extracme.evcard.mmp.dto.ChannelRewardActivityDetailDTO">
            SELECT
            a.id,
            a.activity_name AS activityName,
            a.remark as remark,
            a.sms_content as smsContent,
            CASE a.activity_status
            WHEN 0 THEN '待发布'
            WHEN 1 THEN '待上线'
            WHEN 2 THEN '进行中'
            WHEN 3 THEN '已停止'
            WHEN 4 THEN '已暂停'
            END AS activityStatusName,
             b.ORG_NAME AS orgName,
             a.activity_start_date AS activityStartDate,
             a.activity_end_date AS activityEndDate,
             a.type as type,
             a.third_activity_id as thirdActivityId,
             a.org_id as orgId,
             a.activity_status as activityStatus
            FROM ${mmpSchema}.mmp_pack_night_activity a
            LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
            WHERE a.id = #{id}
    </select>

    <select id="selectActivitiesByThirdIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${mmpSchema}.mmp_pack_night_activity
        WHERE third_activity_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectChannelRewardActivityByStatusStart" resultMap="BaseResultMap">
        select
        id,
        activity_name,
        activity_status,
        org_id,
        activity_start_date,
        activity_end_date,
        type,
        third_activity_id,
        quota_org_id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status = 1
        and activity_start_date = curdate()
        AND type = 4
    </select>

    <select id="selectChannelRewardActivityByStatusCompleted" resultMap="BaseResultMap">
        select
        id,
        activity_name,
        activity_status,
        org_id,
        activity_start_date,
        activity_end_date,
        type,
        third_activity_id,
        quota_org_id,
        org_ids
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status = 2
        and activity_end_date = date_sub(curdate(),interval 1 day)
        AND type = 4
    </select>

    <select id="getCouponBatchImportActivityDetail" parameterType="long"
            resultType="com.extracme.evcard.mmp.dto.CouponBatchImportActivityDetailDTO">
        select
        a.template_file_url as templateFileUrl,
        a.activity_name as activityName,
        a.id,
        a.type,
        a.sms_content as smsContent,
        case
        a.type
        when 5 then N'优惠券批量导入-短模板'
        when 11 then N'优惠券批量导入-长模板'
        when 12 then N'无门槛优惠券批量导入(限额)'
        else '' end as activityType,
        a.activity_status as activityStatus,
        case a.activity_status when 1 then N'未开始' when 2 then N'进行中' when 3 then N'已停止' else '' end as activityStatusName,
        concat(d.name,'(',d.username,')') as issuer,
        b.ORG_NAME as orgName,
        a.org_id as orgId,
        date_format(a.create_time,'%Y-%m-%d %H:%i:%s') as sendTime,
        c.pre_total_amount as totalAmount,
        a.remark,
        a.third_activity_id as thirdActivityId,
        a.create_time as createTime,
        a.quota_org_id as quotaOrgId,
        m.ORG_NAME as quotaOrgName,
        a.template_type as templateType
        from ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        LEFT JOIN ${isvSchema}.org_info m ON a.quota_org_id = m.ORG_ID
        LEFT JOIN ${mmpSchema}.mmp_third_activity c ON a.third_activity_id = c.id
        LEFT JOIN ${mmpSchema}.mmp_user d ON a.create_oper_id = d.id
        where a.id = #{id}
    </select>

    <select id="getActivityNameNum" parameterType="string" resultType="integer">
        select count(*)
        from ${mmpSchema}.mmp_pack_night_activity
        where activity_name = #{activityName}
    </select>

    <select id="queryActivityNameList" resultType="string">
        SELECT 
            DISTINCT activity_name as name 
        from ${mmpSchema}.mmp_pack_night_activity 
        where type !=0
    </select>

    <select id="queryActivityInfoById" parameterType="string" resultType="com.extracme.evcard.mmp.dto.ActivityInfoDTO">
        select
            a.activity_name activityName,
            a.type,
            a.org_id orgId,
            b.ORG_NAME as orgName
        from 
            ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        where a.type !=0 and a.id = #{actionId} 
    </select>

    <select id="getActivityId" parameterType="map" resultType="string">
        select
        id
        from
        ${mmpSchema}.mmp_pack_night_activity
        where
        type !=0
        <if test="activityName!=null and activityName!=''">
            and activity_name = #{activityName}
        </if>
        <if test="type!=null">
            and type = #{type}
        </if>
        <if test="orgId!=null and orgId!=''">
            and org_id = #{orgId}
        </if>
    </select>

    <select id="getActivityIdAndName" parameterType="map" resultType="com.extracme.evcard.mmp.dto.ActivityIdAndNameDTO">
        select
        id "id",activity_name "name"
        from
        ${mmpSchema}.mmp_pack_night_activity
        where
        type !=0
        <if test="activityName!=null and activityName!=''">
            and activity_name = #{activityName}
        </if>
        <if test="type!=null">
            and type = #{type}
        </if>
        <if test="orgId!=null and orgId!=''">
            and org_id = #{orgId}
        </if>
        ORDER BY id DESC
    </select>

    <select id="selectInviteActivityById" resultType="com.extracme.evcard.mmp.dto.activity.InviteActivityDetailDTO">
        SELECT
        a.id,
        a.activity_name AS activityName,
        a.remark as remark,
        CASE a.activity_status
        WHEN 0 THEN '待发布'
        WHEN 1 THEN '待上线'
        WHEN 2 THEN '进行中'
        WHEN 3 THEN '已停止'
        WHEN 4 THEN '已暂停'
        END AS activityStatusName,
        b.ORG_NAME AS orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.type as type,
        a.third_activity_id as thirdActivityId,
        a.org_id as orgId,
        a.org_ids as orgIds,
        a.activity_status as activityStatus
        FROM ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        WHERE a.id = #{id}
    </select>

    <select id="getInviteThirdActivityIdsByActionIds" resultType="com.extracme.evcard.mmp.dto.activity.InviteActivityDetailDTO">
        SELECT
        a.third_activity_id as thirdActivityId,
        b.ORG_NAME AS orgName,
        a.org_id as orgId
        FROM ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        WHERE a.id in
        <foreach collection="array" item="id" separator="," open="(" close=")">
            (#{id})
        </foreach>
    </select>

    <select id="selectOnGoingActivityByOrgIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${mmpSchema}.mmp_pack_night_activity
        where org_id = #{orgId} AND type = #{type} AND activity_status = 2
        limit 1
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${mmpSchema}.mmp_pack_night_activity
        where id = #{id}
        limit 1
    </select>

    <select id="selectSweepActivityById" resultType="com.extracme.evcard.mmp.dto.SweepActivityDetailDTO">
        SELECT
        a.id,
        a.sms_content AS smsContent,
        a.activity_name AS activityName,
        CASE a.activity_status
        WHEN 0 THEN
        '待发布'
        WHEN 1 THEN
        '待上线'
        WHEN 2 THEN
        '进行中'
        WHEN 3 THEN
        '已停止'
        WHEN 4 THEN
        '已暂停'
        END AS activityStatusName,
        b.ORG_NAME AS orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.type as type,
        a.third_activity_id as thirdActivityId,
        a.org_id as orgId,
        a.remark as remark,
        a.activity_status as activityStatus
        FROM ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        WHERE a.id = #{id}
    </select>

    <select id="getCreateRedeemCodeActivityDetail" parameterType="long"
            resultType="com.extracme.evcard.mmp.dto.CouponBatchImportActivityDetailDTO">
        select
        a.activity_name as activityName,
        a.id,
        c.trans_type as transType,
		c.coupon_owner as couponOwner,
		c.owner_id as ownerId,
        case
        a.type
        when 6 then N'生成兑换码'
        else '' end as activityType,
        a.activity_status as activityStatus,
        case a.activity_status
        when 0 then N'待发布'
        when 2 then N'进行中'
        when 3 then N'已停止'
        else '' end as activityStatusName,
        concat(d.name,'(',d.username,')') as issuer,
        b.ORG_NAME as orgName,
        date_format(a.create_time,'%Y-%m-%d %H:%i:%s') as sendTime,
        c.pre_total_amount as totalAmount,
        a.remark,
        a.third_activity_id as thirdActivityId,
        c.qr_code_zip_url as qrCodeZipUrl,
        c.cdk_expires_time as cdkExpiresTimeDate,
        c.one_code_mul_coupon_flag as oneCodeDulCouponFlag,
        c.coupon_code_num as couponCodeNum
        from ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        LEFT JOIN ${mmpSchema}.mmp_third_activity c ON a.third_activity_id = c.id
        LEFT JOIN ${mmpSchema}.mmp_user d ON a.create_oper_id = d.id
        where a.id=#{id}
    </select>

    <select id="selectSweepActivityByStatusStart" resultType="integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status = 1
        and activity_start_date = curdate()
        AND type = 7
    </select>

    <select id="selectSweepActivityByStatusCompleted" resultType="integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status= 2
        and activity_end_date = date_sub(curdate(),interval 1 day)
        AND type = 7
    </select>

    <select id="selectBrandActivityById" resultType="com.extracme.evcard.mmp.dto.activity.BrandActivityDetailDTO">
          SELECT
            a.id,
            a.activity_name AS activityName,
            a.remark as remark,
            CASE a.activity_status
            WHEN 0 THEN '待发布'
            WHEN 1 THEN '待上线'
            WHEN 2 THEN '进行中'
            WHEN 3 THEN '已停止'
            WHEN 4 THEN '已暂停'
            END AS activityStatusName,
            b.ORG_NAME AS orgName,
            a.activity_start_date AS activityStartDate,
            a.activity_end_date AS activityEndDate,
            a.type as type,
            a.third_activity_id as thirdActivityId,
            a.org_id as orgId,
            a.activity_status as activityStatus
            FROM ${mmpSchema}.mmp_pack_night_activity a
            LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
            WHERE a.id = #{id}
    </select>

    <select id="selectBrandActivityByStatusStart" resultType="java.lang.Integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status = 1
        and activity_start_date = curdate()
        AND type = 8
    </select>

    <select id="selectBrandActivityByStatusCompleted" resultType="java.lang.Integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status= 2
        and activity_end_date = date_sub(curdate(),interval 1 day)
        AND type = 8
    </select>

    <select id="selectOrderShareActivityById" resultType="com.extracme.evcard.mmp.dto.OrderShareActivityDetailDTO">
       SELECT
            a.id,
            a.activity_name AS activityName,
            a.remark as remark,
            CASE a.activity_status
            WHEN 0 THEN '待发布'
            WHEN 1 THEN '待上线'
            WHEN 2 THEN '进行中'
            WHEN 3 THEN '已停止'
            WHEN 4 THEN '已暂停'
            END AS activityStatusName,
            b.ORG_NAME AS orgName,
            a.activity_start_date AS activityStartDate,
            a.activity_end_date AS activityEndDate,
            a.type as type,
            a.third_activity_id as shareActivityId,
            a.org_id as orgId,
            a.activity_status as activityStatus
            FROM ${mmpSchema}.mmp_pack_night_activity a
            LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
            WHERE a.id = #{id}
    </select>

    <select id="selectOrderShareActivityByStatusStart" resultType="java.lang.Integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status = 1
        and activity_start_date = curdate()
        AND type = 9
    </select>

    <select id="selectOrderShareActivityByStatusCompleted" resultType="java.lang.Integer">
        select id
        FROM  ${mmpSchema}.mmp_pack_night_activity
        WHERE activity_status= 2
        and activity_end_date = date_sub(curdate(),interval 1 day)
        AND type = 9
    </select>

    <select id="getActivityInfoList" resultType="com.extracme.evcard.mmp.dto.NightCarInfoDTO">
    	  SELECT
        a.id AS id,
        a.activity_name AS activityName,
        CASE a.activity_status
        WHEN 1 THEN'未开始'
        WHEN 2 THEN'进行中'
        WHEN 3 THEN'已停止'
        WHEN 4 THEN'暂停中'
        end as activityStatus,
        b.ORG_NAME as orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.type as type,
        CASE a.type
        WHEN 4 THEN '渠道注册奖励'
        WHEN 7 THEN '扫码发券'
        WHEN 8 THEN '品牌合作发券活动'
        end as typeName,
        a.create_oper_name as createOperName,
        a.sign_id as signId,
        c.activity_url AS activityUrl,
 		c.activity_channel_key AS activityChannelKey
        FROM
        ${mmpSchema}.mmp_pack_night_activity a LEFT JOIN isv.org_info b ON a.org_id=b.ORG_ID
        LEFT JOIN iss.mmp_third_activity c ON a.third_activity_id = c.id
        WHERE 1=1 and (type = 4 || type = 7 || type = 8) and a.activity_status != 3       
             <if test="name!=null and name!=''">
            and a.activity_name like concat('%',#{name},'%')
        </if>
        <if test="ids!=null and ids!=''">
            and a.id = #{ids}
        </if>
        <if test="activityStatus!=null and activityStatus!=''">
            and a.activity_status= #{activityStatus}
        </if>
        <if test="orgId!=null and orgId!=''">
            and a.org_id=#{orgId}
        </if>
        <if test=" type != null ">
            and a.type = #{type}
        </if>
        ORDER BY a.create_time DESC
    </select>
    <select id="getActivityNameList" resultType="com.extracme.evcard.mmp.dto.ActivityNameDTO">
    	SELECT DISTINCT activity_name as name from ${mmpSchema}.mmp_pack_night_activity where (type = 4 || type = 7 || type = 8)
    </select>
    <select id="queryActivityInfoList" resultType="com.extracme.evcard.mmp.dto.ActivityInfoDTO">
        select
        a.activity_name activityName,
        a.type,
        a.org_id orgId,
        b.ORG_NAME as orgName,
        a.id as activityId,
        CASE a.type
        WHEN 1 THEN '第三方发券'
        WHEN 2 THEN '邀请好友'
        WHEN 3 THEN '充值E币发券'
        WHEN 4 THEN '渠道注册奖励'
        WHEN 5 THEN '优惠券批量导入-短模板'
        WHEN 6 THEN '生成兑换码'
        WHEN 7 THEN '扫码发券'
        WHEN 8 THEN '品牌合作发券'
        WHEN 9 THEN '订单分享'
        WHEN 10 THEN '随E停'
        WHEN 11 THEN '优惠券批量导入-长模板'
        WHEN 12 THEN '无门槛优惠券(限额)'
        WHEN 13 THEN '红包网点活动'
        WHEN 14 THEN '首单减免活动'
        WHEN 15 THEN '客服发券活动'
        WHEN 16 THEN '口令红包活动'
        WHEN 17 THEN '订单完成奖励'
        WHEN 18 THEN '增值服务补偿券'
        WHEN 19 THEN '邀新活动'
        WHEN 20 THEN '年度账单'
        end as activityTypeName
        from
        ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        where a.id IN
        <foreach collection="list" separator="," item="item" open="(" close=")">
          #{item}
        </foreach>
    </select>

    <select id="selectStartedActivityId" resultType="string">
      select
        id
      from
        ${mmpSchema}.mmp_pack_night_activity
      where type in (1,4,8,9,10,13,14,15,16,17,18,19,20) AND activity_status > 1
      <![CDATA[
        and activity_start_date<date_format(now(),'%Y-%m-%d')
        and activity_end_date>=date_sub(curdate(),interval 1 day)
        ]]>
    </select>

    <select id="queryActivityTimeConflict" resultType="java.lang.Long">
        SELECT
        id
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = #{type} AND activity_status != 3
        <if test="orgId != null">
            AND org_id = #{orgId}
        </if>
        AND activity_end_date &gt;=#{activityStartDate} AND activity_start_date &lt;=#{activityEndDate}
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>


    <select id="queryActivityTimeConflictByType" resultType="java.util.Map">
        SELECT
        id as id,
        org_ids as orgIds
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = #{type} AND activity_status != 3
        AND activity_end_date &gt;=#{activityStartDate} AND activity_start_date &lt;=#{activityEndDate}
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>



    <select id="selectUnderwayActivityId" resultType="string">
      select
        id
      from
        ${mmpSchema}.mmp_pack_night_activity
      where
        type in (1,4,8,9,10,13,14,15,16,17,18,19,20) and
        (activity_status in (2,4) or (activity_status = 3 and activity_end_date = curdate()))
    </select>

    <select id="queryActivityTimeConflictByPassword" resultType="java.lang.Long">
        SELECT
        id
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = 16 AND activity_status != 3
        AND password = #{password}
        AND activity_end_date &gt;=#{activityStartDate} AND activity_start_date &lt;=#{activityEndDate}
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>

    <select id="queryActivityByPassword" resultType="com.extracme.evcard.mmp.dto.activity.PasswordRedEnvelopeDetailDTO">
        SELECT
        a.id,
        a.activity_name AS activityName,
        a.remark as remark,
        CASE a.activity_status
        WHEN 0 THEN '待发布'
        WHEN 1 THEN '待上线'
        WHEN 2 THEN '进行中'
        WHEN 3 THEN '已停止'
        WHEN 4 THEN '已暂停'
        END AS activityStatusName,
        a.password AS password,
        b.ORG_NAME AS orgName,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.type as type,
        a.third_activity_id as thirdActivityId,
        a.org_id as orgId,
        a.activity_status as activityStatus
        FROM ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        WHERE type = 16 AND activity_status != 3

        <if test="password !=null and password != ''">
            AND password like concat(#{password, jdbcType=VARCHAR},'%')
        </if>
        <if test="startDate !=null and startDate != ''">
            AND activity_end_date &gt;=#{startDate}
        </if>
        <if test="endDate !=null and endDate != ''">
            AND activity_start_date &lt;=#{endDate}
        </if>
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>

    <select id="getPasswordRedEnvelopeById" resultType="com.extracme.evcard.mmp.model.ActivityFullDetail">
        SELECT
        a.id,
        a.activity_name AS activityName,
        a.remark as remark,
        CASE a.activity_status
        WHEN 0 THEN '待发布'
        WHEN 1 THEN '待上线'
        WHEN 2 THEN '进行中'
        WHEN 3 THEN '已停止'
        WHEN 4 THEN '已暂停'
        END AS activityStatusName,
        b.ORG_NAME AS orgName,
        a.password AS password,
        a.activity_start_date AS activityStartDate,
        a.activity_end_date AS activityEndDate,
        a.activity_start_time AS startTime,
        a.activity_end_time AS endTime,
        a.type as type,
        a.third_activity_id as thirdActivityId,
        a.org_id as orgId,
        a.activity_status as activityStatus
        FROM ${mmpSchema}.mmp_pack_night_activity a
        LEFT JOIN ${isvSchema}.org_info b ON a.org_id = b.ORG_ID
        WHERE a.id = #{id}
    </select>

    <select id="queryActivityTimeConflictWithGroup" resultType="java.lang.Long">
        SELECT
        id
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = #{type} AND activity_status != 3
        AND org_id = #{orgId}
        AND group_id = #{groupId}
        AND activity_end_date &gt;=#{activityStartDate} AND activity_start_date &lt;=#{activityEndDate}
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>
    <select id="queryActivityTimeConflictOfOrgIdsWithGroup" resultType="java.lang.Long">
        SELECT
        id
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = #{type} AND activity_status != 3
        AND group_id = #{groupId}
        AND concat(',', org_ids, ',') like concat('%,',#{orgId},',%')
        AND activity_end_date &gt;=#{activityStartDate} AND activity_start_date &lt;=#{activityEndDate}
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>
    <select id="selectOnGoingActivityByOrgIdsAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${mmpSchema}.mmp_pack_night_activity
        where type = #{type} AND activity_status = 2
        AND concat(',', org_ids, ',') like concat('%,',#{orgId},',%')
        limit 1
    </select>
    <select id="queryActivityTimeConflictOfOrgIds" resultType="java.lang.Long">
        SELECT
        id
        FROM
        ${mmpSchema}.mmp_pack_night_activity
        WHERE type = #{type} AND activity_status != 3
        AND concat(',', org_ids, ',') like concat('%,',#{orgId},',%')
        AND activity_end_date &gt;=#{activityStartDate} AND activity_start_date &lt;=#{activityEndDate}
        <if test="id !=null ">
            AND id != #{id}
        </if>
    </select>

</mapper>
