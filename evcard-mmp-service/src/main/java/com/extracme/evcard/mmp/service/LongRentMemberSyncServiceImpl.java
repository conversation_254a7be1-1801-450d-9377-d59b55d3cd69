package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.AgencyIdUtils;
import com.extracme.evcard.mmp.common.BeanCopyUtils;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.dao.AgencyInfoMapper;
import com.extracme.evcard.mmp.dao.MmpAgencyDiscountLogMapper;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.model.AgencyInfo;
import com.extracme.evcard.mmp.util.AgencyIdUtils;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 长租会员同步服务实现类
 */
@Slf4j
@Service
public class LongRentMemberSyncServiceImpl extends LongRentMemberSyncProcessor implements ILongRentMemberSyncService {

    @Resource
    private AgencyInfoMapper agencyInfoMapper;

    @Resource
    private IVehicleRuleService vehicleRuleService;

    @Resource
    private IMmpDiscountRuleService mmpDiscountRuleService;

    @Resource
    private LongShortContractAdapter longShortContractAdapter;

    @Autowired
    MmpAgencyDiscountLogMapper mmpAgencyDiscountLogMapper;

    @Autowired
    IMmpOperationLogService operationLogService;

    @Autowired
    IMmpAgencyAccountService mmpAgencyAccountService;

    @Resource
    IMmpFreeDepositNumberService mmpFreeDepositNumberService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO syncLongRentMember(LongRentMemberSyncInput input) {
        log.info("开始执行长租会员同步逻辑，输入参数：{}", JSON.toJSONString(input));

        // 1. 参数校验
        BaseResponse validationResult = validateInput(input);
        if (validationResult.getCode() != 0) {
            return new DefaultServiceRespDTO(validationResult.getCode(), validationResult.getMessage());
        }
        // 赋默认值
        enhanceInput(input);

        try {
            // 2. 根据企业名称查询现有企业信息
            List<AgencyInfo> existingAgencies = agencyInfoMapper.getAgencyInfoByAgencyName(input.getAgencyName());

            if (CollectionUtils.isEmpty(existingAgencies)) {
                // 场景A：企业会员不存在
                return handleScenarioA(input);
            } else {
                AgencyInfo agencyInfo = existingAgencies.get(0);

                // 检查是否存在用车规则
                boolean hasVehicleRule = vehicleRuleService.hasVehicleRule(agencyInfo.getAgencyId());

                if (!hasVehicleRule) {
                    // 场景B：企业会员存在，但用车规则不存在
                    return handleScenarioB(agencyInfo, input);
                } else {
                    // 场景C：企业会员存在，且用车规则也存在
                    return handleScenarioC(agencyInfo, input);
                }
            }

        } catch (Exception e) {
            log.error("长租会员同步逻辑执行失败，企业名称：{}", input.getAgencyName(), e);
            return new DefaultServiceRespDTO(-1, "长租会员同步失败：" + e.getMessage());
        }
    }

    private void enhanceInput(LongRentMemberSyncInput input) {
        // 企业性质：外部
        String orgProperty = input.getOrgProperty();
        if (StringUtils.isEmpty(orgProperty)) {
            input.setOrgProperty("0");
        }

        // 结算方式：默认预付费 1：预付费
        Double payWay = input.getPayWay();
        if (payWay == null) {
            input.setPayWay(1.0);
        }

        // 订单支付方式 默认个人支付
        Integer orderPayer = input.getOrderPayer();
        if (orderPayer == null) {
            input.setOrderPayer(2);
        }

        // 押金担保方式 默认个人支付
        Integer depositGuarantor = input.getDepositGuarantor();
        if (depositGuarantor == null) {
            input.setDepositGuarantor(2);
        }

        // 违约承担方 默认 2个人承担
        Integer defaultingParty = input.getDefaultingParty();
        if (defaultingParty == null) {
            input.setDefaultingParty(2);
        }

        // 业务来源 默认 1 政企客户
        Integer businessSource = input.getBusinessSource();
        if (businessSource == null) {
            input.setBusinessSource(1);
        }

        // 创建方式 默认 3长租客户
        input.setCreateWay(3);

        Date date = new Date();
        // 默认为当前日期+3年
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, 3);
        Date nowAfter3Years = calendar.getTime();

        // 过期时间 默认 当前时间+3年
        Date expireTime = input.getExpireTime();
        if (expireTime == null) {
            input.setExpireTime(nowAfter3Years);
        }

        Double personDiscountRate = input.getPersonDiscountRate();
        if (personDiscountRate == null) {
            input.setPersonDiscountRate(95.0);
        }

        Double personPeakSeasonDiscountRate = input.getPersonPeakSeasonDiscountRate();
        if (personPeakSeasonDiscountRate == null) {
            input.setPersonPeakSeasonDiscountRate(95.0);
        }

        Integer beneficiaryNumber = input.getBeneficiaryNumber();
        if (beneficiaryNumber == null) {
            input.setBeneficiaryNumber(50000);
        }

        Date validStartTime = input.getValidStartTime();
        if (validStartTime == null) {
            input.setValidStartTime(date);
        }

        Date validEndTime = input.getValidEndTime();
        if (validEndTime == null) {
            input.setValidEndTime(nowAfter3Years);
        }

    }

    /**
     * 参数校验
     */
    private BaseResponse validateInput(LongRentMemberSyncInput input) {
        if (input == null) {
            return new BaseResponse(-1, "输入参数不能为空");
        }

        if (input.getType() != 1 && input.getType() != 0) {
            return new BaseResponse(-1, "类型不正确");
        }

        if (StringUtils.isEmpty(input.getAgencyName())) {
            return new BaseResponse(-1, "企业名称不能为空");
        }

        Double personDiscountRate = input.getPersonDiscountRate();
        Double personPeakSeasonDiscountRate = input.getPersonPeakSeasonDiscountRate();
        if (input.getType() == 1) {
            if (personDiscountRate == null || personPeakSeasonDiscountRate == null) {
                return new BaseResponse(-1, "折扣不能为空");
            }
            return new BaseResponse(0, "");
        }

        if (StringUtils.isEmpty(input.getContractsName())) {
            return new BaseResponse(-1, "联系人姓名不能为空");
        }

        if (StringUtils.isEmpty(input.getContractsMobile())) {
            return new BaseResponse(-1, "联系人手机号不能为空");
        }

        // 企业营业执照号非必填，不校验

        // 姓名长度过长时截取
        if (input.getContractsName() != null && input.getContractsName().length() > 20) {
            input.setContractsName(input.getContractsName().substring(0, 20));
        }

        // 设置默认值
        if (input.getOperatorId() == null) {
            input.setOperatorId(-1L);
        }
        if (StringUtils.isEmpty(input.getOperatorName())) {
            input.setOperatorName("长租会员同步自动操作");
        }

        return new BaseResponse(0, "参数校验通过");
    }

    @Override
    @Transactional
    public String createNewAgency(LongRentMemberSyncInput input) {
        log.info("开始创建新的企业会员，企业名称：{}", input.getAgencyName());

        try {
            // 1. 生成新的企业ID
            String maxAgencyId = agencyInfoMapper.getMaxAgencyId();
            String last2Digit = maxAgencyId.substring(maxAgencyId.length() - 2);
            String newAgencyId = maxAgencyId.substring(0, maxAgencyId.length() - 2) + AgencyIdUtils.getNextNodeId(last2Digit);

            // 2. 构建企业信息
            AgencyInfo agencyInfo = buildAgencyInfo(input, newAgencyId, true);

            // 3. 初始化折扣规则（支持输入参数，提供兜底默认值）
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(input.getOperatorId());
            operatorDTO.setOperatorName(input.getOperatorName());

            // 个人折扣率：支持输入参数，默认为95%
            Double discountRate = input.getPersonDiscountRate();
            // 创建默认的折扣规则
            Long discountId = mmpDiscountRuleService.saveInit(newAgencyId, null, operatorDTO, 100.0, 0);
            Long discountPersonId = mmpDiscountRuleService.saveInitForPerson(newAgencyId, null, operatorDTO, discountRate);
            mmpDiscountRuleService.saveInitForPackage(newAgencyId, null, operatorDTO);

            agencyInfo.setDiscountId(discountId);
            agencyInfo.setDiscountPersonalId(discountPersonId);


            // 4. 保存企业信息
            int result = longShortContractAdapter.addAgencyInfo(agencyInfo);
            if (result > 0) {
                log.info("创建企业会员成功，企业ID：{}", newAgencyId);

                /** 新建企业会员更新redis */
                String agencyRedisKey = "agencyFreeInfo_" + agencyInfo.getAgencyId();
                HashMap<String, String> agencyMap = new HashMap<>();
                agencyMap.put("pay_way", String.valueOf(agencyInfo.getPayWay().intValue()));
                JedisUtil.hmset(agencyRedisKey, agencyMap);
                OperationLogSaveDTO operationLogSaveDTO = new OperationLogSaveDTO();
                operationLogSaveDTO.setOperationRemark("新建企业会员");
                operationLogSaveDTO.setAgencyId(newAgencyId);
                operationLogSaveDTO.setOperationTime(new Date());
                operationLogService.save(operationLogSaveDTO, null, operatorDTO);

                /** 新增免押人数*/
                AgencyFreeDepositSaveDTO freeDepositSaveDTO = new AgencyFreeDepositSaveDTO();
                freeDepositSaveDTO.setAgencyId(newAgencyId);
                mmpFreeDepositNumberService.save(freeDepositSaveDTO, null,operatorDTO);

                // 初始化完成后，新增规则
                DiscountRuleSaveDTO personalDiscountRuleSaveDTO = new DiscountRuleSaveDTO();
                personalDiscountRuleSaveDTO.setDiscountRate(input.getPersonDiscountRate());
                personalDiscountRuleSaveDTO.setPeakSeasonDiscountRate(input.getPersonPeakSeasonDiscountRate());
                personalDiscountRuleSaveDTO.setAgencyId(agencyInfo.getAgencyId());
                personalDiscountRuleSaveDTO.setDiscountType(1);
                personalDiscountRuleSaveDTO.setValidEndTime(input.getExpireTime());
                personalDiscountRuleSaveDTO.setBeneficiaryNumber(input.getBeneficiaryNumber());
                DefaultServiceRespDTO defaultServiceRespDTO = mmpDiscountRuleService.save(personalDiscountRuleSaveDTO, null, operatorDTO);
                if (defaultServiceRespDTO.getCode() != 0) {
                    log.error("长租客户同步，更新企业会员个人折扣异常，req:{}，res:{}",JSON.toJSON(personalDiscountRuleSaveDTO), JSON.toJSON(defaultServiceRespDTO));
                    throw new BusinessException("更新企业会员个人折扣失败");
                }
                return newAgencyId;
            } else {
                log.error("保存企业会员失败，企业名称：{}", input.getAgencyName());
                return null;
            }

        } catch (Exception e) {
            log.error("创建企业会员异常，企业名称：{}", input.getAgencyName(), e);
            return null;
        }
    }

    @Override
    protected void updateAgencyInfo(AgencyInfo agencyInfo, LongRentMemberSyncInput input, boolean keepReferrer) {
        log.info("开始更新企业信息，企业ID：{}，保持推荐人信息：{}", agencyInfo.getAgencyId(), keepReferrer);

        try {
            // 保存原有的推荐人信息
            String originalReferrerName = agencyInfo.getReferrerName();
            String originalReferrerMobile = agencyInfo.getReferrerMobile();

            // 更新企业信息
            buildAgencyInfoFromInput(agencyInfo, input);

            // 如果需要保持推荐人信息不变，则恢复原有值
            if (keepReferrer) {
                agencyInfo.setReferrerName(originalReferrerName);
                agencyInfo.setReferrerMobile(originalReferrerMobile);
            }

            // 设置更新信息
            agencyInfo.setUpdateOperId(input.getOperatorId());
            agencyInfo.setUpdatedUser(input.getOperatorName());
            agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));

            // 保存更新
            int result = agencyInfoMapper.updateByPrimaryKey(agencyInfo);
            if (result > 0) {
                log.info("更新企业信息成功，企业ID：{}", agencyInfo.getAgencyId());
            } else {
                log.error("更新企业信息失败，企业ID：{}", agencyInfo.getAgencyId());
            }

        } catch (Exception e) {
            log.error("更新企业信息异常，企业ID：{}", agencyInfo.getAgencyId(), e);
            throw new RuntimeException("更新企业信息失败", e);
        }
    }

    @Override
    protected BaseResponse createDefaultVehicleRule(String agencyId, Long operatorId, String operatorName) {
        return vehicleRuleService.createDefaultVehicleRule(agencyId, operatorId, operatorName);
    }

    @Override
    protected BaseResponse updateToDefaultVehicleRule(String agencyId, String ruleId, Long operatorId, String operatorName) {
        return vehicleRuleService.updateToDefaultVehicleRule(agencyId, ruleId, operatorId, operatorName);
    }

    @Override
    protected String getDefaultVehicleRuleId(String agencyId) {
        return vehicleRuleService.getDefaultVehicleRuleId(agencyId);
    }

    /**
     * 构建企业信息对象
     */
    private AgencyInfo buildAgencyInfo(LongRentMemberSyncInput input, String agencyId, boolean isNew) {
        AgencyInfo agencyInfo = new AgencyInfo();
        agencyInfo.setAgencyId(agencyId);
        if (isNew) {
            agencyInfo.setCreateWay(input.getCreateWay());
            agencyInfo.setReferrerMobile(input.getReferrerMobile());
            agencyInfo.setReferrerName(input.getReferrerName());

            // 新建企业的基本设置
            agencyInfo.setAgencyId(agencyId);
            agencyInfo.setStatus(2); // 0-暂停中，1-合作中，2-未开始
            agencyInfo.setCooperateStatus(0); // 0-未开始；1-合作中；2-已暂停
            agencyInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            agencyInfo.setCreatedUser(input.getOperatorName());
            agencyInfo.setCreateOperId(input.getOperatorId());

            agencyInfo.setDiscountRule(1);
            agencyInfo.setAppKey("");
            agencyInfo.setOrgName("");
            agencyInfo.setMaxUnitPrice(new BigDecimal(1));
        }

        // 从输入参数构建企业信息
        buildAgencyInfoFromInput(agencyInfo, input);

        return agencyInfo;
    }

    /**
     * 从输入参数构建企业信息
     */
    private void buildAgencyInfoFromInput(AgencyInfo agencyInfo, LongRentMemberSyncInput input) {
        agencyInfo.setAgencyName(input.getAgencyName());
        agencyInfo.setOrgProperty(input.getOrgProperty());
        agencyInfo.setContact(input.getContractsName());
        agencyInfo.setMobilePhone(input.getContractsMobile());
        agencyInfo.setMail(input.getContractEmail());
        if (!StringUtils.isEmpty(input.getLicenseNo())) {
            agencyInfo.setLicenseNo(input.getLicenseNo());
        }
        agencyInfo.setPayWay(input.getPayWay());
        agencyInfo.setOrderPayer(input.getOrderPayer());
        agencyInfo.setDepositGuarantor(input.getDepositGuarantor());
        agencyInfo.setDefaultingParty(input.getDefaultingParty());
        agencyInfo.setBusinessSource(input.getBusinessSource());
        agencyInfo.setExpireTime(input.getExpireTime());
        agencyInfo.setLineLimitMonthly(input.getLineLimitMonthly());
        agencyInfo.setVehicleNo(input.getVehicleNo());
        agencyInfo.setVehicleThreshold(input.getMaxCarNumber());
        agencyInfo.setExemptDepositAmount(input.getExemptDepositAmount());
        agencyInfo.setRemark(input.getRemark());

        agencyInfo.setUpdateOperId(input.getOperatorId());
        agencyInfo.setUpdatedUser(input.getOperatorName());
        agencyInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
    }
}
