package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSONObject;
import com.csvreader.CsvWriter;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.*;
import com.extracme.evcard.mmp.dao.*;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.model.*;
import com.extracme.evcard.mmp.service.activity.ShopServ;
import com.extracme.evcard.mmp.service.activity.VehicleModelServ;
import com.extracme.evcard.rpc.coupon.dto.*;
import com.extracme.evcard.rpc.coupon.service.ICouponBatchServ;
import com.extracme.evcard.rpc.coupon.service.ICouponModelServ;
import com.extracme.evcard.rpc.coupon.service.ICouponServ;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.DateUtil;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目名称：evcard-mmp-service
 * 类名称：CreateRedeemCodeService
 * 类描述：生成兑换码活动业务实现类
 * 创建人：sunb-孙彬
 * 创建时间：2018年1月18日下午5:47:01
 * 修改备注
 *
 * @version1.0
 *
 *  TODO 1. 支持超过大量兑换码(10万-百万)生成和导出
 *          生成通过MQ异步完成，导出拆分多个文件上传到oss。
 * TODO 发券金额统计 也考虑两种新增的券
 */
@SuppressWarnings("ALL")
@Service
public class CreateRedeemCodeServiceImpl implements ICreateRedeemCodeService {
    private static final String EXPORT_TITLE = "export.CouponExchangeRecord.title";
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Resource
    private MmpPackNightActivityMapper mmpPackNightActivityMapper;
    @Resource
    ICouponModelServ couponModelServ;
    @Resource
    ICouponServ couponServ;
    @Resource
    private MmpThirdActivityMapper mmpThirdActivityMapper;
    @Resource
    private MmpThirdCouponMapper mmpThirdCouponMapper;
    @Resource
    private UserOperatorLogMapper userOperatorLogMapper;
    @Resource
    private ICouponBatchServ couponBatchServ;
    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Resource
    IOperateCityService operateCityService;
    @Resource
    IOrgInfoService orgInfoServiceImpl;
    @Resource
    private MmpActivityOfferDetailMapper mmpActivityOfferDetailMapper;
    @Resource
    IMarketActivityService marketActivityServiceImpl;
    @Resource
    MmpAgencyInfoServiceImpl mmpAgencyInfoService;
    @Resource
    MmpAgencyInfoMapper mmpAgencyInfoMapper;

    @Autowired
    private ShopServ shopServ;

    @Autowired
    private VehicleModelServ vehicleModelServ;

    private static final int ACTIVITY_TYPE = 6;
    private static final Integer TYPE_TRANS_BUY = 1;
    private static final Integer TYPE_TRANS_ANCENCY = 1;

    /**
     * 新增生成兑换码活动
     *
     * @param createRedeemCodeDTO
     * @param request
     * @return
     */
    @Override
    @Transactional
    public DefaultServiceRespDTO addCreateRedeemCodeActivity(CreateRedeemCodeDTO createRedeemCodeDTO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(createOperId);
        operatorDTO.setOperatorName(createOperName);

        //1. 校验活动配置信息
        vo = checkRedeemCodeActivity(createRedeemCodeDTO, comModel.getOrgId());
        if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
            return vo;
        }
        //2. 校验优惠券模板信息
        //如果有优惠券模板，模板匹配
        List<MmpThirdCoupon> thirdCoupons = new ArrayList<>();
        //优惠券模板总金额
        BigDecimal totalCouponValue = new BigDecimal(0);
        //导入折扣券的总面额
        BigDecimal discountTotalAmount = new BigDecimal(0);
        //导入抵用券的总面额
        BigDecimal vouchersTotalAmount = new BigDecimal(0);
        //导入非收入券的总面额
        BigDecimal uncountTotalAmount = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(createRedeemCodeDTO.getCouponModels())) {
            //add 2019.4.2 校验发券限制, 获取公司-活动优惠券发放限制
            MarketActivityDTO activityDto = new MarketActivityDTO();
            activityDto.setOrgId(createRedeemCodeDTO.getOrgId());
            activityDto.setType(ACTIVITY_TYPE);
            ActivityOfferConfigDTO couponOfferLimit = marketActivityServiceImpl.getCouponOfferLimit(activityDto);
            for (ThirdCouponModelDTO thirdCouponModel : createRedeemCodeDTO.getCouponModels()) {
                //检测优惠券模板
                DefaultServiceRespDTO checkResp = checkCouponModel(createRedeemCodeDTO, thirdCouponModel, request.getRemoteUser());
                if (checkResp.getCode() != 0) {
                    return checkResp;
                }
                //add 2019.4.2 校验发券限制
                if(couponOfferLimit != null) {
                    vo = marketActivityServiceImpl.checkCouponOfferLimit(thirdCouponModel, couponOfferLimit);
                    if (vo.getCode() != 0) {
                        vo.setCode(-1);
                        return vo;
                    }
                }
                if(thirdCouponModel.getTimeType() == null) {
                    thirdCouponModel.setTimeType(0);
                }
                CouponModelViewDto modelViewDto = new CouponModelViewDto();
                BeanUtils.copyProperties(thirdCouponModel, modelViewDto);
                modelViewDto.setCouponType(thirdCouponModel.getModelCouponType());

                //匹配模板不存优惠券名称
                modelViewDto.setCouponName(StringUtils.EMPTY);
                //设置validTimeType=0 匹配模板的时候不根据时间匹配
                //add 4.29 兑换码活动带时间类型匹配
                if(modelViewDto.getValidTimeType() != null && !modelViewDto.getValidTimeType().equals(2)) {
                    modelViewDto.setValidTimeType(0);
                }
//                else {
//                    modelViewDto.setValidTimeType(0);
//                }
                //modelViewDto.setValidTimeType(0);

                //匹配模板
                CouponModelViewDto couponModelViewDto = new CouponModelViewDto();
                try {
                  couponModelViewDto = marketActivityServiceImpl.getMatchedCouponModel(modelViewDto);
                    if (couponModelViewDto.getCode() != 0) {
                        vo.setCode(-1);
                        vo.setMessage(couponModelViewDto.getMessage());
                        return vo;
                    }
                }catch (Exception e){
                    vo.setCode(-1);
                    vo.setMessage("匹配模板失败");
                    log.error("匹配模板失败",e);
                    return vo;
                }
                MmpThirdCoupon thirdCoupon = new MmpThirdCoupon();
                BeanCopyUtils.copyProperties(thirdCouponModel, thirdCoupon);
                thirdCoupon.setCouponSeq(Long.valueOf(couponModelViewDto.getCouponSeq()));
                thirdCoupon.setOrgId(StringUtils.EMPTY);
                thirdCoupons.add(thirdCoupon);
                //计算总优惠券金额
                BigDecimal couponValue = thirdCouponModel.getCouponValue();
                Integer quantity = thirdCouponModel.getOfferQuantity();

                //一码多券时，需要 乘以 一码的数目
                if(createRedeemCodeDTO.getOneCodeDulCouponFlag() == 1) {
                    // 这里不往表里更新 ,只做总金额的计算
                    quantity = quantity * createRedeemCodeDTO.getCouponCodeNum();
                }

                //排除折扣券，因折扣券couponValue默认最高抵用金额
                Integer couponType = thirdCouponModel.getCouponType();
                if (1 == couponType ) {
                    vouchersTotalAmount = vouchersTotalAmount.add(couponValue.multiply(new BigDecimal(quantity)));
                } else if (2 == couponType ) {
                    discountTotalAmount = discountTotalAmount.add(couponValue.multiply(new BigDecimal(quantity)));
                }
                else if (3 == couponType ) {
                    uncountTotalAmount = uncountTotalAmount.add(couponValue.multiply(new BigDecimal(quantity)));
                }
            }
        } else {
            vo.setCode(-1);
            vo.setMessage("优惠券模板不能为空");
            return vo;
        }

        // 插入操作记录日志
        String operationContent = StringUtils.EMPTY;
        String symbol = "+";
        if (vouchersTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
            operationContent += vouchersTotalAmount + "元面额的抵用券";
        }
        if (vouchersTotalAmount.compareTo(new BigDecimal(0)) > 0 && discountTotalAmount.compareTo(new BigDecimal(0)) > 0) {
            operationContent += symbol;
        }
        if (discountTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
            operationContent += discountTotalAmount + "元面额的折扣券";
        }
        if (uncountTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
            operationContent += uncountTotalAmount + "元面额的非收入券";
        }

        // 插入操作记录日志
        operationContent = createOperName + "给" + createRedeemCodeDTO.getOrgId() + "发出" + operationContent;
        log.info(operationContent);
        ComUtil.insertOperatorLog(operationContent, "sendCoupon", createOperId.toString(), createOperName, userOperatorLogMapper);

        // 保存生成券码活动配置
        MmpThirdActivity mmpThirdActivity = new MmpThirdActivity();
        if(createRedeemCodeDTO.getTransType().equals(1)) {
            //券购买-企业
            if(createRedeemCodeDTO.getCouponOwner().equals(1)) {
                MmpAgencyInfoDTO mmpAgencyDTO = createRedeemCodeDTO.getMmpAgency();
                vo = mmpAgencyInfoService.saveAgencyInfo(mmpAgencyDTO, operatorDTO);
                if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
                    return vo;
                }
                mmpThirdActivity.setOwnerId(vo.getData());
                mmpThirdActivity.setTransType(createRedeemCodeDTO.getTransType());
                mmpThirdActivity.setCouponOwner(createRedeemCodeDTO.getCouponOwner());
            }
        }
        //活动预算额度=总抵用券金额
        totalCouponValue = vouchersTotalAmount;
        mmpThirdActivity.setPreTotalAmount(totalCouponValue);
       /* mmpThirdActivity.setVoucherTotalAmount(vouchersTotalAmount);
        mmpThirdActivity.setDiscountTotalAmount(discountTotalAmount);*/
        mmpThirdActivity.setCreateOperId(createOperId);
        mmpThirdActivity.setCreateOperName(createOperName);
        mmpThirdActivity.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mmpThirdActivity.setUpdateOperId(createOperId);
        mmpThirdActivity.setUpdateOperName(createOperName);
        mmpThirdActivity.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));

        // 一码多券设置
        if (StringUtils.isNotBlank(createRedeemCodeDTO.getCdkExpiresTime())) {
            mmpThirdActivity.setCdkExpiresTime(ComUtil.getDateFromStr(createRedeemCodeDTO.getCdkExpiresTime(), ComUtil.DATE_TYPE1));
            mmpThirdActivity.setCdkStartTime(new Date());
        }
        if (createRedeemCodeDTO.getOneCodeDulCouponFlag() == 1) {
            mmpThirdActivity.setCouponCodeNum(createRedeemCodeDTO.getCouponCodeNum());
            mmpThirdActivity.setOneCodeDulCouponFlag(createRedeemCodeDTO.getOneCodeDulCouponFlag());
        }
        mmpThirdActivityMapper.insertSelective(mmpThirdActivity);

        //保存关联表
        if (CollectionUtils.isNotEmpty(thirdCoupons)) {
            for (MmpThirdCoupon thirdCoupon : thirdCoupons) {
                thirdCoupon.setThirdActivityId(mmpThirdActivity.getId());
            }
            mmpThirdCouponMapper.batchSaveThirdFullCoupons(thirdCoupons);
        }
        // 保存活动
        MmpPackNightActivity mmpPackNightActivity = new MmpPackNightActivity();
        BeanCopyUtils.copyProperties(createRedeemCodeDTO, mmpPackNightActivity);
        mmpPackNightActivity.setCreateOperId(createOperId);
        mmpPackNightActivity.setCreateOperName(createOperName);
        mmpPackNightActivity.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mmpPackNightActivity.setUpdateOperId(createOperId);
        mmpPackNightActivity.setUpdateOperName(createOperName);
        // 新增时活动状态由“运行中”改为“待审核”
        mmpPackNightActivity.setActivityStatus(0L);
        mmpPackNightActivity.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mmpPackNightActivity.setThirdActivityId(mmpThirdActivity.getId());
        mmpPackNightActivity.setType(6);
        Date currentDate = new Date();
        mmpPackNightActivity.setActivityStartDate(currentDate);
        mmpPackNightActivity.setActivityEndDate(currentDate);
        mmpPackNightActivityMapper.save(mmpPackNightActivity);
        //保存抵用券折扣券总金额
        MmpActivityOfferDetail offerDetail = new MmpActivityOfferDetail();
        offerDetail.setActivityId(mmpPackNightActivity.getId());
        offerDetail.setOrgId(mmpPackNightActivity.getOrgId());
        offerDetail.setDiscountTotalAmount(discountTotalAmount);
        offerDetail.setVoucherTotalAmount(vouchersTotalAmount);
        offerDetail.setDiscountRemainAmount(discountTotalAmount);
        offerDetail.setVoucherRemainAmount(vouchersTotalAmount);
        offerDetail.setRemainOfferNumber(0);
        offerDetail.setCreateOperId(createOperId);
        offerDetail.setCreateOperName(createOperName);
        mmpActivityOfferDetailMapper.insertSelective(offerDetail);

        String operatorContent = "新增";
        ComUtil.insertOperatorLog(operatorContent, "HD" + mmpPackNightActivity.getId().toString(), createOperId.toString(), createOperName, userOperatorLogMapper);
        
      //生成券码的优惠券模板总金额
        totalCouponValue = totalCouponValue.add(vouchersTotalAmount).add(discountTotalAmount);
        // 扣减额度 环球为00
        if (!"00".equals(createRedeemCodeDTO.getOrgId()) &&
                vouchersTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
            //BaseResponse baseResponse = couponServ.subtractCurrentMonthQuota(createRedeemCodeDTO.getOrgId(), vouchersTotalAmount);
            BaseResponse baseResponse = couponServ.subtractCurrentMonthQuota(createRedeemCodeDTO.getOrgId(), vouchersTotalAmount,createOperId,createOperName,mmpPackNightActivity.getId()+"",2);
            if (baseResponse.getCode() == -1) {
                throw new RuntimeException(baseResponse.getMessage());
            }
        }
        vo.setMessage("提交成功");
        vo.setData(String.valueOf(mmpPackNightActivity.getId()));
        return vo;
    }

    @Override
    @Transactional
    public DefaultServiceRespDTO delete(Long id, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        int count;
        //1. 获取活动状态，是否可删除
        MmpPackNightActivity activity = mmpPackNightActivityMapper.selectById(id);
        if (activity == null) {
            vo.setCode(-1);
            vo.setMessage("活动不存在");
            return vo;
        }
        //仅未开始/暂停中的活动，可被删除
        if(activity.getActivityStatus() != 0 && activity.getActivityStatus() != 1) {
            vo.setCode(-1);
            vo.setMessage("活动状态已修改，请刷新重试");
            return vo;
        }
        //2.1 删除活动配置信息
        MmpThirdActivity mmpThirdActivity = mmpThirdActivityMapper.selectByPrimaryKey(activity.getThirdActivityId());
        count = mmpThirdActivityMapper.deleteByPrimaryKey(activity.getThirdActivityId());
        if (count < 1) {
            vo.setCode(-1);
            vo.setMessage("删除失败");
            return vo;
        }
        //2.2 删除活动相关模板配置
        mmpThirdCouponMapper.deleteByThirdActivityId(activity.getThirdActivityId());
        //2.3 删除活动主表记录
        count = mmpPackNightActivityMapper.deleteNightCarInfo(Long.valueOf(id));
        if (count < 1) {
            vo.setCode(-1);
            vo.setMessage("删除失败");
            return vo;
        }

        //3. 将扣除的子公司额度 还给下个月
        if (!"00".equals(activity.getOrgId())) {
            BigDecimal remainAmountVal = mmpThirdActivity.getPreTotalAmount();
            if (remainAmountVal.compareTo(BigDecimal.ZERO) > 0) {
                couponServ.addToNextMonthQuota(activity.getCreateTime(),
                        activity.getOrgId(), remainAmountVal, createOperId, createOperName, id+"", 5);
            }
        }
        //4. 日志记录
        String operatorContent = "删除";
        ComUtil.insertOperatorLog(operatorContent, "HD" + id.toString(), createOperId.toString(), createOperName,
                userOperatorLogMapper);

        vo.setMessage("活动已删除");
        return vo;
    }

    @Override
    @Transactional
    public DefaultServiceRespDTO publish(Long id, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        //查询当前活动
        MmpPackNightActivity eActivity = mmpPackNightActivityMapper.selectById(id);
        if (eActivity == null) {
            vo.setCode(-1);
            vo.setMessage("活动不存在");
            return vo;
        }
        Date activityEndDate = eActivity.getActivityEndDate();
        String now = ComUtil.getSystemDate(ComUtil.DATE_TYPE5);
        String endDate = DateFormatUtils.ISO_DATE_FORMAT.format(activityEndDate);
        if (now.compareTo(endDate) > 0) {
            vo.setCode(-1);
            vo.setMessage("活动时间已截止，无法再发布");
            return vo;
        }
        /**
         * 审核通过并开始
         */
        int count = mmpPackNightActivityMapper.updateActivityImmediateStartStatus(id, createOperId, createOperName);
        if (count < 1) {
            vo.setCode(-1);
            vo.setMessage("操作失败");
            return vo;
        }
        String operatorContent = "发布并开始";
        ComUtil.insertOperatorLog(operatorContent, "HD" + id.toString(), createOperId.toString(), createOperName, userOperatorLogMapper);
        vo.setMessage("活动已发布");
        return vo;
    }

    private DefaultServiceRespDTO checkCouponModel(CreateRedeemCodeDTO createRedeemCodeDTO,
                                                   ThirdCouponModelDTO thirdCouponModel, String remoteUser) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        //购买类，仅限发购买类券
        if(TYPE_TRANS_BUY.equals(createRedeemCodeDTO.getTransType())) {
            if(thirdCouponModel.getCouponType() != null && !thirdCouponModel.getCouponType().equals(4)) {
                vo.setCode(-1);
                vo.setMessage("仅支持发放购买类优惠券");
                return vo;
            }
        }else {
            if(thirdCouponModel.getCouponType() != null &&
                    (thirdCouponModel.getCouponType() < 1 || thirdCouponModel.getCouponType() > 3 )) {
                vo.setCode(-1);
                vo.setMessage("仅支持发放优惠券、折扣券、非收入优惠券");
                return vo;
            }
        }
        MarketActivityCouponModelDTO activityCouponModelDTO = new MarketActivityCouponModelDTO();
        activityCouponModelDTO.setType(ACTIVITY_TYPE);
        activityCouponModelDTO.setOrgId(createRedeemCodeDTO.getOrgId());
        activityCouponModelDTO.setCouponModel(thirdCouponModel);
        return marketActivityServiceImpl.checkCouponModel(activityCouponModelDTO, remoteUser);
    }

    private DefaultServiceRespDTO checkRedeemCodeActivity(CreateRedeemCodeDTO createRedeemCodeDTO, String orgId) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        // 活动必填，长度不能超过20个字符，活动名称不能重复
        if (StringUtils.isBlank(createRedeemCodeDTO.getActivityName())) {
            vo.setCode(-1);
            vo.setMessage("活动名称必填");
            return vo;
        }
        if (StringUtils.isNotBlank(createRedeemCodeDTO.getActivityName()) &&
                createRedeemCodeDTO.getActivityName().length() > 20) {
            vo.setCode(-1);
            vo.setMessage("活动名称长度不能超过20个字符");
            return vo;
        }
        // 默认账户所属公司
        if (StringUtils.isBlank(createRedeemCodeDTO.getOrgId())) {
            createRedeemCodeDTO.setOrgId(orgId);
        }
        // 备注长度不能超过512个字符
        if (StringUtils.isNotBlank(createRedeemCodeDTO.getRemark()) && createRedeemCodeDTO.getRemark().length() > 512) {
            vo.setCode(-1);
            vo.setMessage("备注长度不能超过512个字符");
            return vo;
        }

        // -----------    add 2019.4.22 添加 优惠券交易方式校验。
        // 交易方式 0赠送(默认)， 1购买
        if(createRedeemCodeDTO.getTransType() == null) {
            createRedeemCodeDTO.setTransType(0); //默认赠送
        }
        //优惠券购买，购买方信息校验
        if(TYPE_TRANS_BUY.equals(createRedeemCodeDTO.getTransType())) {
            if(TYPE_TRANS_ANCENCY.equals(createRedeemCodeDTO.getCouponOwner())) {
                //1. 企业购买，校验企业信息
                MmpAgencyInfoDTO mmpAgencyInfo = createRedeemCodeDTO.getMmpAgency();
                vo = mmpAgencyInfoService.checkMmpAgencyInfo(mmpAgencyInfo);
                if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
                    vo.setCode(-1);
                    vo.setMessage(vo.getMessage());
                    return vo;
                }
            }
        }

        // -----------    add 2024.12.18 添加 一码多券
        if(createRedeemCodeDTO.getOneCodeDulCouponFlag() == 1 && createRedeemCodeDTO.getCouponCodeNum() <= 0) {
            vo.setCode(-1);
            vo.setMessage("兑换码数量必须大于0");
            return vo;
        }

        // 判断时间是否过期
        String cdkExpiresTime = createRedeemCodeDTO.getCdkExpiresTime();
        if (StringUtils.isNotBlank(cdkExpiresTime)) {
            LocalDateTime cdkExpiresLocalDate = LocalDateTime.parse(cdkExpiresTime, DateUtil.DATE_TYPE1);
            if (LocalDateTime.now().compareTo(cdkExpiresLocalDate) > 0) {
                vo.setCode(-1);
                vo.setMessage("兑换码过期时间不能小于当前时间");
                return vo;
            }
        }

        return vo;
    }

    /**
     * 验证车牌
     *
     * @param address
     * @param vo
     * @return
     */
    public boolean checkAddress(String address, DefaultServiceRespDTO vo) {
        address = address.replace("，", ",");
        String c = "";
        c = address.substring(address.length() - 1, address.length());
        if (",".equals(c)) {
            vo.setMessage("地域限制不能以逗号结尾");
            return false;
        }
        String[] split = address.split(",");
        for (String str : split) {
            if (null == str || "".equals(str)) {
                vo.setMessage("地域限制逗号间隔之间的车牌号不能为空");
                return false;
            }
            if (str.length() > 2) {
                vo.setMessage("地域限制车牌长度填写错误--" + str);
                return false;
            }
            // 第一个字符必须在全国车牌简称中
            c = str.substring(0, 1);
            if (!Arrays.asList(Contants.CARLICENSE).contains(c)) {
                vo.setMessage("地域限制车牌简称填写错误--" + str);
                return false;
            }
            if (str.length() == 2) {
                // 第二个字符必须在26个子母中
                c = str.substring(1, 2);
                if (!Arrays.asList(Contants.LETTER).contains(c)) {
                    vo.setMessage("地域限制车牌号填写错误--" + str);
                    return false;
                }
            }
        }

        // 地区限制个数不能超过10个
        if (split.length > 10) {
            vo.setMessage("地区选择数量不能超过十个");
            return false;
        }
        return true;
    }

    /**
     * 验证车型
     *
     * @param vehicleModle
     * @param vo
     * @return
     */
    public String checkVehicleModel(String vehicleModle, DefaultServiceRespDTO vo) {
        vehicleModle = vehicleModle.replace("，", ",");
        String result = "";
        List<String> resultList = new ArrayList<String>();
        String c = "";
        c = vehicleModle.substring(vehicleModle.length() - 1, vehicleModle.length());
        if (",".equals(c)) {
            vo.setMessage("车型限制不能以逗号结尾");
            return "";
        }
        String[] split = vehicleModle.split(",");
        List<VehicleModel> vehicleModels = marketActivityServiceImpl.queryVehicleModelList();
        //List<VehicleModel> vehicleModels = mmpPackNightActivityMapper.queryVehicleModel();
        for (String str : split) {
            if (null == str || "".equals(str)) {
                vo.setMessage("车型限制逗号间隔之间的车型不能为空");
                return "";
            }
            String value = "";
            for (VehicleModel model : vehicleModels) {
                if (str.equals(model.getVehicleModelSeq())) {
                    value = model.getVehicleModelSeq();
                    resultList.add(model.getVehicleModelSeq());
                    break;
                }
            }
            if (StringUtils.isEmpty(value)) {
                vo.setMessage("未找到对应的车型--" + str);
                return "";
            }
        }
        result = StringUtils.join(resultList.toArray(), ",");
        return result;
    }

    /**
     * 生成兑换码活动详情
     *
     * @param id 活动id
     * @return
     */
    @Override
    public CouponBatchImportActivityDetailDTO getCreateRedeemCodeActivityDetail(Long id) {
        CouponBatchImportActivityDetailDTO couponBatchImportActivityDetailDTO =
                mmpPackNightActivityMapper.getCreateRedeemCodeActivityDetail(id);
        //add 4.22 添加购买方相关信息
        //交易方式若为购买，则许查询购买方信息
        if(TYPE_TRANS_BUY.equals(couponBatchImportActivityDetailDTO.getTransType())) {
            if(TYPE_TRANS_ANCENCY.equals(couponBatchImportActivityDetailDTO.getCouponOwner())) {
                String agencyId = couponBatchImportActivityDetailDTO.getOwnerId();
                MmpAgencyInfoDTO mmpAgencyInfo = mmpAgencyInfoMapper.selectByPrimaryKey(Long.valueOf(agencyId));
                couponBatchImportActivityDetailDTO.setMmpAgency(mmpAgencyInfo);
            }
        }

        List<MmpActivityOfferDetail> mmpActivityOfferDetails =
                mmpActivityOfferDetailMapper.selectOfferDetailsByActivityId(id);
        if(CollectionUtils.isNotEmpty(mmpActivityOfferDetails)){
            MmpActivityOfferDetail detail = mmpActivityOfferDetails.get(0);
            couponBatchImportActivityDetailDTO.setDiscountTotalAmount(
                    String.valueOf(detail.getDiscountTotalAmount().intValue()));
            couponBatchImportActivityDetailDTO.setVoucherTotalAmount(
                    String.valueOf(detail.getVoucherTotalAmount().intValue()));
        }

        // 兑换码结束日期
        Date cdkExpiresTime = couponBatchImportActivityDetailDTO.getCdkExpiresTimeDate();
        if (cdkExpiresTime != null) {
            couponBatchImportActivityDetailDTO.setCdkExpiresTime(com.extracme.evcard.mmp.common.DateUtils.getFormatDate(cdkExpiresTime, ComUtil.DATE_TYPE1));
        }else{
            couponBatchImportActivityDetailDTO.setCdkExpiresTime("长期有效");
        }

        // 优惠券数量
        int couponModleQuantity = 0;
        List<MmpThirdCoupon> mmpThirdCoupons = mmpThirdCouponMapper.selectAllCouponSeqByThirdId(couponBatchImportActivityDetailDTO.getThirdActivityId());
        if (CollectionUtils.isNotEmpty(mmpThirdCoupons)) {
            couponModleQuantity = mmpThirdCoupons.stream().mapToInt(MmpThirdCoupon::getOfferQuantity).sum();
        }
        Integer oneCodeDulCouponFlag = couponBatchImportActivityDetailDTO.getOneCodeDulCouponFlag();
        Integer couponCodeNum = couponBatchImportActivityDetailDTO.getCouponCodeNum();
        if (oneCodeDulCouponFlag == 1) {
            couponBatchImportActivityDetailDTO.setCouponCodeNum(couponCodeNum);
        }else{
            couponBatchImportActivityDetailDTO.setCouponCodeNum(couponModleQuantity);
        }
        couponBatchImportActivityDetailDTO.setOneCodeDulCouponFlag(oneCodeDulCouponFlag);

        return couponBatchImportActivityDetailDTO;
    }

    /**
     * 生成兑换码
     *
     * @param createRedeemCodeModelDTO
     * @param request
     * @return
     */
    @Override
    @Transactional
    public DefaultServiceRespDTO addCreateRedeemCode(CreateRedeemCodeModelDTO createRedeemCodeModelDTO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        // 活动ID不能为空
        String actionId = createRedeemCodeModelDTO.getActionId();
        int needQrFlag = createRedeemCodeModelDTO.getNeedQrFlag();

        if (StringUtils.isBlank(actionId)) {
            vo.setCode(-1);
            vo.setMessage("活动ID不能为空");
            return vo;
        }
        //通过活动id查询生成券码的活动
        MmpPackNightActivity createRedeemCodeActivity = mmpPackNightActivityMapper.selectById(Long.valueOf(actionId));
        if (null == createRedeemCodeActivity) {
            vo.setCode(-1);
            vo.setMessage("获取活动信息失败");
            return vo;
        }
        if (Long.valueOf(3L).equals(createRedeemCodeActivity.getActivityStatus())) {
            vo.setCode(-1);
            vo.setMessage("活动已停止");
            return vo;
        }

        Long thirdActivityId = createRedeemCodeActivity.getThirdActivityId();
        MmpThirdActivity thirdActivity = mmpThirdActivityMapper.selectByPrimaryKey(thirdActivityId);
        if (null == thirdActivity) {
            vo.setCode(-1);
            vo.setMessage("获取活动信息失败");
            return vo;
        }
        String orgId = createRedeemCodeActivity.getOrgId();
        List<OfferCouponDto> offerCouponDtoList = getCreateRedeemCodeOfferCouponDto(actionId, orgId, createOperName, thirdActivity);
        // 更新活动状态为已停止
        MmpPackNightActivity mmpPackNightActivity = new MmpPackNightActivity();
        mmpPackNightActivity.setActivityStatus(3L);
        mmpPackNightActivity.setUpdateOperId(createOperId);
        mmpPackNightActivity.setUpdateOperName(createOperName);
        mmpPackNightActivity.setId(new Long(createRedeemCodeModelDTO.getActionId()));
        mmpPackNightActivityMapper.updateByIdSelective(mmpPackNightActivity);
        String operatorContent = "生成兑换码，更新活动状态为已停止";
        ComUtil.insertOperatorLog(operatorContent, "HD" + createRedeemCodeModelDTO.getActionId().toString(), createOperId.toString(), createOperName, userOperatorLogMapper);

        //调用服务批量生成券码
        BaseResponse baseResponse = null;
        if (thirdActivity.getOneCodeDulCouponFlag() == 1) {
            OneCodeMulCouponDto oneCodeMulCouponDto = new OneCodeMulCouponDto();
            oneCodeMulCouponDto.setCouponCodeNum(thirdActivity.getCouponCodeNum());
            oneCodeMulCouponDto.setActionId(createRedeemCodeModelDTO.getActionId());
            oneCodeMulCouponDto.setOptUser(createOperName);
            oneCodeMulCouponDto.setCouponCodeStartTime(thirdActivity.getCdkStartTime());
            oneCodeMulCouponDto.setCouponCodeExpiresTime(thirdActivity.getCdkExpiresTime());
            // 一码多券的 发券
            baseResponse = couponBatchServ.batchCreateOneCodeMulCoupon(offerCouponDtoList,oneCodeMulCouponDto);
        }else{
            baseResponse = couponBatchServ.batchCreateCouponBatch(offerCouponDtoList);
        }


        if (needQrFlag == 1) {
            try {
                BatchCreateQrCodeInput batchCreateQrCodeInput = new BatchCreateQrCodeInput();
                batchCreateQrCodeInput.setActivityId(Long.valueOf(actionId));
                if (thirdActivity.getOneCodeDulCouponFlag() == 1) {
                    batchCreateQrCodeInput.setType(1);
                }else{
                    batchCreateQrCodeInput.setType(0);
                }
                batchCreateQrCodeInput.setOperatorName(comModel.getCreateOperName());
                batchCreateQrCodeInput.setOperatorId(comModel.getCreateOperId());
                BatchCreateQrCodeResponse batchCreateQrCodeResponse = couponBatchServ.batchCreateQrCode(batchCreateQrCodeInput);
                if (batchCreateQrCodeResponse != null) {
                    vo.setCode(batchCreateQrCodeResponse.getCode());
                    vo.setMessage(batchCreateQrCodeResponse.getMessage());
                }
            } catch (Exception e) {
                log.error("生成兑换码二维码失败，活动id：{}", actionId, e);
                vo.setCode(-1);
                vo.setMessage("生成兑换码失败");
            }
            return vo;
        }

        if (baseResponse.getCode() < 0) {
            log.info("批量生成兑换码失败，活动id：" + actionId + "失败原因：" + baseResponse.getMessage());
            throw new RuntimeException(baseResponse.getMessage());
        }
        vo.setMessage("生成兑换码成功");
        return vo;
    }

    //TODO 暂定兑换最长支持2年内兑换。
    private static final int DEF_MAX_VALIDDAYS = 365 * 2;
    private List<OfferCouponDto> getCreateRedeemCodeOfferCouponDto(String actionId, String orgId, String createOperName,
                                                                   MmpThirdActivity thirdActivity) {
        List<OfferCouponDto> offerCouponDtos = new ArrayList<>();
        List<CouponDetailDTO> couponDetailDTOs = mmpThirdCouponMapper.getCouponDetail(Long.valueOf(actionId));
        for (CouponDetailDTO couponDetailDTO : couponDetailDTOs) {
            OfferCouponDto offerCouponDto = new OfferCouponDto();
            offerCouponDto.setActionId(actionId);
            // 券模版ID
            offerCouponDto.setCouponSeq(Integer.valueOf(couponDetailDTO.getCouponSeq()));
            // 优惠券所属机构id
            offerCouponDto.setOrgId(orgId);
            //设置不需要扣减额度标识 为false
            offerCouponDto.setDeductions(false);
            //add 4.22 设置交易类型及购买信息
            offerCouponDto.setTransactionType(couponDetailDTO.getCouponType());
            if(TYPE_TRANS_ANCENCY.equals(thirdActivity.getCouponOwner())) {
                offerCouponDto.setAgencyId(thirdActivity.getOwnerId());
            }

            Integer validTimeTypeVal = couponDetailDTO.getValidTimeType();
            String startDate = StringUtils.EMPTY;
            String expiresDate = StringUtils.EMPTY;
            Date now = new Date();
            if (validTimeTypeVal == 1 || validTimeTypeVal == 0) {
                startDate = couponDetailDTO.getStartDate();
                expiresDate = couponDetailDTO.getExpiresDate();
            } else if (validTimeTypeVal == 2) {
                //指定有效时长的有坏全
                //到账几天有效
                Integer effectiveDays = couponDetailDTO.getEffectiveDays();
                Integer validDays = couponDetailDTO.getValidDays();
                //开始时间，优惠券的有效期为发券时间 + 到账时长
                startDate = DateFormatUtils.ISO_DATE_FORMAT.format(DateUtils.addDays(now, effectiveDays));
                //有效时长，优惠券的有效期为发券时间 + 到账时长 + 有效时长.
                //modify 4.29, 兑换码使用有效时长时，给与默认的最大兑换时间区间。
                //expiresDate = DateFormatUtils.ISO_DATE_FORMAT.format(DateUtils.addDays(now, effectiveDays + validDays));
                expiresDate = DateFormatUtils.ISO_DATE_FORMAT.format(DateUtils.addDays(now, effectiveDays + DEF_MAX_VALIDDAYS - 1));
            }
            // 有效期开始时间
            offerCouponDto.setStartDate(startDate);
            // 有效期结束时间
            offerCouponDto.setExpiresDate(expiresDate);
            // 优惠券数量
            offerCouponDto.setNum(couponDetailDTO.getNum());
            // 兑换码有效期
            Date cdkExpiresTime = thirdActivity.getCdkExpiresTime();
            if (cdkExpiresTime != null) {
                offerCouponDto.setCouponCodeExpiresTime(cdkExpiresTime);
            }
            Date cdkStartTime = thirdActivity.getCdkStartTime();
            if (cdkStartTime != null) {
                offerCouponDto.setCouponCodeStartTime(cdkStartTime);
            }

            // 优惠券名称
            offerCouponDto.setCouponName(couponDetailDTO.getCouponName());
            offerCouponDto.setOptOrgId(orgId);
            offerCouponDto.setOptUser(createOperName);
            offerCouponDtos.add(offerCouponDto);
        }
        return offerCouponDtos;
    }

    /**
     * 优惠券详情
     *
     * @param id 活动送券配置表id
     * @return
     */
    @Override
    public DefaultServiceRespDTO getCouponDetail(Long id) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        List<MmpThirdCoupon> mmpThirdCouponList = mmpThirdCouponMapper.selectAllCouponSeqByThirdId(id);
        MmpThirdCoupon mmpThirdCoupon = new MmpThirdCoupon();
        if (CollectionUtils.isNotEmpty(mmpThirdCouponList)) {
            mmpThirdCoupon = mmpThirdCouponList.get(0);
            // 调用dubbo服务(优惠券匹配模板信息)
            if (mmpThirdCoupon.getCouponSeq() == null) {
                vo.setCode(-1);
                vo.setMessage("优惠券id不能为空");
                return vo;
            }
            if (mmpThirdCoupon.getCouponName() == null) {
                vo.setCode(-1);
                vo.setMessage("优惠券名称不能为空");
                return vo;
            }
            CouponModelViewDto couponModelViewDto = new CouponModelViewDto();
            try {
                couponModelViewDto = couponModelServ.getCouponModelByCouponSeq(mmpThirdCoupon.getCouponSeq().intValue());
            } catch (Exception e) {
                vo.setCode(-1);
                vo.setMessage("调用优惠券详情dubbo服务异常：" + e.toString());
                return vo;
            }
            RedeemCodeCouponDetailDTO redeemCodeCouponDetailDTO = new RedeemCodeCouponDetailDTO();
            BeanCopyUtils.copyProperties(couponModelViewDto, redeemCodeCouponDetailDTO);
            redeemCodeCouponDetailDTO.setCouponName(mmpThirdCoupon.getCouponName());
            redeemCodeCouponDetailDTO.setOfferQuantity(mmpThirdCoupon.getOfferQuantity());
            redeemCodeCouponDetailDTO.setStartDate(mmpThirdCoupon.getStartDate());
            redeemCodeCouponDetailDTO.setExpiresDate(mmpThirdCoupon.getExpiresDate());
            redeemCodeCouponDetailDTO.setValidDays(mmpThirdCoupon.getValidDays());
            redeemCodeCouponDetailDTO.setEffectiveDays(mmpThirdCoupon.getEffectiveDays());
            redeemCodeCouponDetailDTO.setValidTimeType(mmpThirdCoupon.getValidTimeType());
            //TODO redeemCodeCouponDetailDTO.setCouponType();
            vo.setData(JSONObject.toJSONString(redeemCodeCouponDetailDTO));
            return vo;
        } else {
            vo.setCode(-1);
            vo.setMessage("此活动没有配置优惠券信息");
            return vo;
        }
    }

    /**
     * 导出兑换码
     *
     * @param id       活动id
     * @param request
     * @param response
     * @return
     */
    private static final int SINGLE_FILE_SIZE = 100000; //单个文件10万
    private static final int QUERY_LIMIT_SIZE = 10000; //单个文件10万


    /**
     * 优惠券兑换码 导出功能
     * 入参 needQrFlag 为1，导出二维码图片时，把excel和二维码图片打包的zip文件 分别返回
     * 入参 needQrFlag 为0，只导出excel
     *
     * @param dto
     * @param request
     * @return
     */
    @Override
    public DefaultServiceRespDTO2 handleExportCreateRedeemCode(ExportRedeemCodeModelDTO dto, HttpServletRequest request) {
        DefaultServiceRespDTO2 vo = new DefaultServiceRespDTO2();
        ExportCreateRedeemCodeDTO exportCreateRedeemCodeDTO = new ExportCreateRedeemCodeDTO();
        vo.setData(exportCreateRedeemCodeDTO);

        ComModel comModel = ComUtil.getUserInfo(request);
        Long id = dto.getActionId();
        int needQrFlag = dto.getNeedQrFlag();
        // 根据活动id查询活动详情
        CouponBatchImportActivityDetailDTO activityDetailDto = mmpPackNightActivityMapper.getCreateRedeemCodeActivityDetail(id);
        if (activityDetailDto == null) {
            //活动信息不存在
            vo.setCode(-1);
            vo.setMessage("导出失败，活动信息不存在。");
            return vo;
        }
        MmpThirdActivity thirdActivity = mmpThirdActivityMapper.selectByPrimaryKey(activityDetailDto.getThirdActivityId());
        if (null == thirdActivity) {
            vo.setCode(-1);
            vo.setMessage("获取活动信息失败");
            return vo;
        }

        String couponCodeExcelFullFileName = null;
        try {
            String qrCodeZipUrl = activityDetailDto.getQrCodeZipUrl();
            // excel 文件全路径
            couponCodeExcelFullFileName = getCouponCodeExcel(dto);
            String fileName = couponCodeExcelFullFileName.substring(couponCodeExcelFullFileName.lastIndexOf("/") + 1);
            //上传至oss, 返回文件地址
            String resultFileUrl = UploadImgUtil.uploadSynByFilePathGetHttpsPath(couponCodeExcelFullFileName, "/qrCode/wechat/zip/coupon/" + fileName);
            exportCreateRedeemCodeDTO.setExcelFilePath(resultFileUrl);
            if (needQrFlag == 0) {
                // 不需要导出二维码，只导出excle
                return vo;
            } else {
                List<String> localFileFUllPathList = new ArrayList<>();
                localFileFUllPathList.add(couponCodeExcelFullFileName);

                List<String> ossFileNameList = new ArrayList<>();
                //需要导出二维码
                if (StringUtils.isBlank(qrCodeZipUrl)) {
                    try {
                        BatchCreateQrCodeInput batchCreateQrCodeInput = new BatchCreateQrCodeInput();
                        batchCreateQrCodeInput.setActivityId(id);
                        if (thirdActivity.getOneCodeDulCouponFlag() == 1) {
                            batchCreateQrCodeInput.setType(1);
                        }else{
                            batchCreateQrCodeInput.setType(0);
                        }
                        batchCreateQrCodeInput.setOperatorName(comModel.getCreateOperName());
                        batchCreateQrCodeInput.setOperatorId(comModel.getCreateOperId());
                        BatchCreateQrCodeResponse batchCreateQrCodeResponse = couponBatchServ.batchCreateQrCode(batchCreateQrCodeInput);
                        if (batchCreateQrCodeResponse != null) {
                            vo.setCode(batchCreateQrCodeResponse.getCode());
                            vo.setMessage(batchCreateQrCodeResponse.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("生成兑换码二维码失败，活动id：{}", id, e);
                        vo.setCode(-1);
                        vo.setMessage("导出文件失败");
                    }
                } else {
                    //生成过二维码,后续导出zip
                    exportCreateRedeemCodeDTO.setZipFilePath(qrCodeZipUrl);
                }
                return vo;
            }
        } catch (BusinessException e) {
            vo.setCode(e.getCode());
            vo.setMessage(e.getMessage());
        } catch (Exception e) {
            vo.setCode(-1);
            vo.setMessage("导出失败。");
        }finally {
            if (StringUtils.isNotBlank(couponCodeExcelFullFileName)) {
                File file = new File(couponCodeExcelFullFileName);
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
        return vo;
    }

    /**
     * 获取优惠券兑换码excel文件
     *
     * @param dto
     * @return  返回服务器上的文件
     */
    public String getCouponCodeExcel(ExportRedeemCodeModelDTO dto) throws BusinessException {
        String result = StringUtils.EMPTY;
        Long id = dto.getActionId();
        ComUtil.checkAndMkDir("/data");

        //0. TODO 增加逻辑 查询文件记录，若已生成则直接下载

        //1. 获取活动详情
        // 根据活动id查询活动详情
        CouponBatchImportActivityDetailDTO activityDetailDto = mmpPackNightActivityMapper.getCreateRedeemCodeActivityDetail(id);
        //活动信息不存在
        if(activityDetailDto == null) {
            throw new BusinessException(-1,"导出失败，活动信息不存在。");
        }

        //2. 获取活动优惠券所使用的优惠券模板
        List<CouponDetailDTO> mmpThirdCouponList = mmpThirdCouponMapper.getCouponDetail(id);
        //活动信息不存在
        if(CollectionUtils.isEmpty(mmpThirdCouponList)) {
            throw new BusinessException(-1,"导出失败，无优惠券信息。");
        }
        // 调用coupon rpc 服务查询优惠券模板
        Set<Long> couponSeqs = new HashSet<>();
        for(CouponDetailDTO thirdCoupon : mmpThirdCouponList) {
            couponSeqs.add(Long.valueOf(thirdCoupon.getCouponSeq()));
        }
        List<Long> couponSeqList = new ArrayList<>(couponSeqs);
        List<CouponModelListDto> couponModels = couponModelServ.getCouponModelListByCouponSeq(couponSeqList);
        /**
         * 转换优惠券ids为优惠券信息列表
         */
        List<CouponModelViewListDto> viewListDtos = marketActivityServiceImpl.getCouponViewPage(couponModels);

        Map<Integer, List<CouponModelViewListDto>> couponModelMap = viewListDtos.stream()
                .collect(Collectors.groupingBy(CouponModelViewListDto::getCouponSeq));

        for(CouponDetailDTO thirdCoupon : mmpThirdCouponList) {
            long couponSeq = Long.valueOf(thirdCoupon.getCouponSeq());
            List<CouponModelViewListDto> list = couponModelMap.get(couponSeq);
            if(CollectionUtils.isNotEmpty(list) && list.size() > 0) {
                list.get(0).setCouponType(thirdCoupon.getCouponType());
            }
            couponSeqs.add(Long.valueOf(thirdCoupon.getCouponSeq()));
        }

        //4. 分批次获取券数据(每次1万)， 10万分批次写文件(计入文件导出日志)
        Long couponIdCursor = null;
        String dateStr = new SimpleDateFormat(ComUtil.DATE_TYPE3).format(System.currentTimeMillis());
        String execelName = "createRedeemCode_" + id + "_" + dateStr + ".CSV";
        String fullPath = "/data/" + execelName;
        Integer oneCodeDulCouponFlag = activityDetailDto.getOneCodeDulCouponFlag();
        for(int index = 0 ; ; index ++) {
            List<SelectUserCouponDto> userCouponList = new ArrayList<>();
            if (oneCodeDulCouponFlag == 1) {
                userCouponList = couponBatchServ.selectPageOneCodeMulCouponByActionId(id, couponIdCursor, QUERY_LIMIT_SIZE);
            }else{
                userCouponList = couponBatchServ.selectPageCouponByActionId(id, couponIdCursor, QUERY_LIMIT_SIZE);
            }

            if(CollectionUtils.isEmpty(userCouponList)) {
                break;
            }
            //3. 根据authId查询会员名称和手机号
            Map<String, MembershipInfo> memberMap = new HashMap<String, MembershipInfo>();
            Set<String> authIds = new HashSet<>();
            for(SelectUserCouponDto userCoupon : userCouponList) {
                if(StringUtils.isNotBlank(userCoupon.getAuthId())) {
                    authIds.add(userCoupon.getAuthId());
                }
            }
            if(CollectionUtils.isNotEmpty(authIds)){
                List<MembershipInfo> memberList = membershipInfoMapper.getNameAndTelByAuthIds(authIds);
                memberList.forEach(member -> {
                    if(!memberMap.containsKey(member.getAuthId())){
                        memberMap.put(member.getAuthId(), member);
                    }
                });
            }
            //4. 写文件
            writeCSVFile(fullPath, index, userCouponList, activityDetailDto, couponModelMap, memberMap);
            //下一个文件游标
            couponIdCursor = userCouponList.get(userCouponList.size() -1).getUserCouponSeq();
        }

        return fullPath;
    }


    /**
     * 文件流导出
     * @param actionId
     * @param couponCodeExcelFullFileName
     * @param request
     * @param response
     * @return
     */
    public DefaultServiceRespDTO exportCreateRedeemCode2(long actionId,String couponCodeExcelFullFileName, HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        try {
            if(StringUtils.isBlank(couponCodeExcelFullFileName)) {
                log.warn("导出兑换码|导出失败，未生成文件，活动id={}。", actionId);
                vo.setCode(-1);
                vo.setMessage("导出兑换码失败！");
                return vo;
            }

            String fileName = couponCodeExcelFullFileName.substring(couponCodeExcelFullFileName.lastIndexOf("/") + 1);
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + fileName);
            OutputStream out = response.getOutputStream();
            BufferedInputStream input = null;
            try {
                File file = new File(couponCodeExcelFullFileName);
                input = new BufferedInputStream(new FileInputStream(file));
                out = response.getOutputStream();
                // 创建一个Buffer字符串
                byte[] buffer = new byte[1024];
                // 每次读取的字符串长度，如果为-1，代表全部读取完毕
                int len = 0;
                // 使用一个输入流从buffer里把数据读取出来
                while ((len = input.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
                out.flush();// 缓存清空输出

            } catch (FileNotFoundException e1) {
                log.error(ComUtil.getExceptionMsg(e1));
                vo.setCode(-1);
                vo.setMessage("导出兑换码失败！");
                return vo;
            } catch (IOException e) {
                log.error(ComUtil.getExceptionMsg(e));
                vo.setCode(-1);
                vo.setMessage("导出兑换码失败！");
                return vo;
            }
            finally {
                IOUtils.closeQuietly(input);
                IOUtils.closeQuietly(out);
                //移除临时文件
                try{
                    File file = new File(couponCodeExcelFullFileName);
                    file.delete();
                }
                catch (Exception ex) {
                    log.error("导出兑换码|：移除文件失败， file={}", couponCodeExcelFullFileName);
                }
            }
            ComModel comModel = ComUtil.getUserInfo(request);
            ComUtil.insertOperatorLog("导出兑换码", "HD" + actionId, comModel.getCreateOperId().toString(), comModel.getCreateOperName(), userOperatorLogMapper);
            vo.setMessage("导出兑换码成功！");
            return vo;
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(-1);
            vo.setMessage("导出兑换码失败！");
            return vo;
        }

    }

    //TODO 后续拆分导出和下载接口
    //导出接口仅做文件生成，文件异步下载。
    @Override
    @Transactional
    public DefaultServiceRespDTO exportCreateRedeemCode(ExportRedeemCodeModelDTO dto, HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        Set<String> files = new HashSet<>();
        Long id = dto.getActionId();
        try {
            ComUtil.checkAndMkDir("/data");

            //0. TODO 增加逻辑 查询文件记录，若已生成则直接下载

            //1. 获取活动详情
            // 根据活动id查询活动详情
            CouponBatchImportActivityDetailDTO activityDetailDto = mmpPackNightActivityMapper.getCreateRedeemCodeActivityDetail(id);
            if(activityDetailDto == null) {
                //活动信息不存在
                vo.setCode(-1);
                vo.setMessage("导出失败，活动信息不存在。");
                return vo;
            }
            //2. 获取活动优惠券所使用的优惠券模板
            List<CouponDetailDTO> mmpThirdCouponList = mmpThirdCouponMapper.getCouponDetail(id);
            if(CollectionUtils.isEmpty(mmpThirdCouponList)) {
                //活动信息不存在
                vo.setCode(-1);
                vo.setMessage("导出失败，无优惠券信息。");
                return vo;
            }
            // 调用coupon rpc 服务查询优惠券模板
            Set<Long> couponSeqs = new HashSet<>();
            for(CouponDetailDTO thirdCoupon : mmpThirdCouponList) {
                couponSeqs.add(Long.valueOf(thirdCoupon.getCouponSeq()));
            }
            List<Long> couponSeqList = new ArrayList<>(couponSeqs);
            List<CouponModelListDto> couponModels = couponModelServ.getCouponModelListByCouponSeq(couponSeqList);
            /**
             * 转换优惠券ids为优惠券信息列表
             */
            List<CouponModelViewListDto> viewListDtos = marketActivityServiceImpl.getCouponViewPage(couponModels);

            Map<Integer, List<CouponModelViewListDto>> couponModelMap = viewListDtos.stream()
                    .collect(Collectors.groupingBy(CouponModelViewListDto::getCouponSeq));

            // TODO 处理couponType(兼容新增的两种券类型)
            for(CouponDetailDTO thirdCoupon : mmpThirdCouponList) {
                long couponSeq = Long.valueOf(thirdCoupon.getCouponSeq());
                List<CouponModelViewListDto> list = couponModelMap.get(couponSeq);
                if(CollectionUtils.isNotEmpty(list) && list.size() > 0) {
                    list.get(0).setCouponType(thirdCoupon.getCouponType());
                }
                couponSeqs.add(Long.valueOf(thirdCoupon.getCouponSeq()));
            }

            //4. 分批次获取券数据(每次1万)， 10万分批次写文件(计入文件导出日志)
            Long couponIdCursor = null;
            String dateStr = new SimpleDateFormat(ComUtil.DATE_TYPE3).format(System.currentTimeMillis());
            for(int index = 0 ; ; index ++) {
                List<SelectUserCouponDto> userCouponList = couponBatchServ.selectPageCouponByActionId(id,
                        couponIdCursor, QUERY_LIMIT_SIZE);
                if(CollectionUtils.isEmpty(userCouponList)) {
                    break;
                }
                //3. 根据authId查询会员名称和手机号
                Map<String, MembershipInfo> memberMap = new HashMap<String, MembershipInfo>();
                Set<String> authIds = new HashSet<>();
                for(SelectUserCouponDto userCoupon : userCouponList) {
                    if(StringUtils.isNotBlank(userCoupon.getAuthId())) {
                        authIds.add(userCoupon.getAuthId());
                    }
                }
                if(CollectionUtils.isNotEmpty(authIds)){
                    List<MembershipInfo> memberList = membershipInfoMapper.getNameAndTelByAuthIds(authIds);
                    memberList.forEach(member -> {
                        if(!memberMap.containsKey(member.getAuthId())){
                            memberMap.put(member.getAuthId(), member);
                        }
                    });
                }
                //4. 写文件
                // TODO 增加Buffer， 每2w写一次文件。
                //String execelName = "createRedeemCode_" + id + "_" + index + "_" + dateStr + ".CSV";
                String execelName = "createRedeemCode_" + id + "_" + dateStr + ".CSV";
                String fullPath = "/data/" + execelName;
//                String fullPath = "D:/" + execelName;
                writeCSVFile(fullPath, index, userCouponList, activityDetailDto, couponModelMap, memberMap);
                files.add(fullPath);

                //下一个文件游标
                couponIdCursor = userCouponList.get(userCouponList.size() -1).getUserCouponSeq();
            }

            //TODO 文件上传至oss
            //TODO 文件记录写入会员业务导出表


            //开始下载
            // String exportDirector =
            // SystemPropertyUtils.getString(Contants.EXPROT_DIRECTORY);

            //TODO 临时逻辑，当前只下载第一个文件。
            if(files.isEmpty()) {
                log.warn("导出兑换码|导出失败，未生成文件，活动id={}。", activityDetailDto.getId());
                vo.setCode(-1);
                vo.setMessage("导出兑换码失败！");
                return vo;
            }

            String destFile = new ArrayList<String>(files).get(0);
            String fileName = "createRedeemCode_" + id + "_" + dateStr + ".CSV";
            // File file = new File("d:\\" + execelName);
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + fileName);
            OutputStream out = response.getOutputStream();
            BufferedInputStream input = null;
            try {
                //TODO 改为 从oss上读取文件
                File file = new File(destFile);
                input = new BufferedInputStream(new FileInputStream(file));
                out = response.getOutputStream();
                // 创建一个Buffer字符串
                byte[] buffer = new byte[1024];
                // 每次读取的字符串长度，如果为-1，代表全部读取完毕
                int len = 0;
                // 使用一个输入流从buffer里把数据读取出来
                while ((len = input.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
                out.flush();// 缓存清空输出

            } catch (FileNotFoundException e1) {
                log.error(ComUtil.getExceptionMsg(e1));
                vo.setCode(-1);
                vo.setMessage("导出兑换码失败！");
                return vo;
            } catch (IOException e) {
                log.error(ComUtil.getExceptionMsg(e));
                vo.setCode(-1);
                vo.setMessage("导出兑换码失败！");
                return vo;
            }
            finally {
                IOUtils.closeQuietly(input);
                IOUtils.closeQuietly(out);
                files.forEach(f -> {
                    //移除临时文件
                    try{
                        File file = new File(f);
                        file.delete();
                    }
                    catch (Exception ex) {
                        log.error("导出兑换码|：移除文件失败， file={}", f);
                    }
                });
            }
            ComModel comModel = ComUtil.getUserInfo(request);
            ComUtil.insertOperatorLog("导出兑换码", "HD" + id.toString(), comModel.getCreateOperId().toString(), comModel.getCreateOperName(), userOperatorLogMapper);
            vo.setMessage("导出兑换码成功！");
            return vo;
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(-1);
            vo.setMessage("导出兑换码失败！");
            return vo;
        }

    }


    private String[] buildFullCoupon(SelectUserCouponDto userCoupon,
                                     CouponBatchImportActivityDetailDTO activityDetailDto,
                                     Map<Integer, List<CouponModelViewListDto>> couponModelMap,
                                     Map<String, MembershipInfo> memberMap){
        MembershipInfo member = null;
        if(StringUtils.isNotBlank(userCoupon.getAuthId())) {
            member = memberMap.get(userCoupon.getAuthId());
        }
        CouponModelViewListDto couponModel = null;
        List<CouponModelViewListDto> couponModels = couponModelMap.get(userCoupon.getCouponSeq().intValue());
        if(CollectionUtils.isEmpty(couponModels)) {
            return null;
        }
        //优惠券类别
        couponModel = couponModels.get(0);
        String couponTypeStr = "抵用券";
        Integer couponType = couponModel.getCouponType();
        if(userCoupon.getTransactionType() != null) {
            couponType = userCoupon.getTransactionType();
        }
        if(couponType != null) {
            //TODO 后续改为根据userCoupon的transactionType来确定优惠券类型
            if(couponType == 2) {
                couponTypeStr = "折扣券";
            }
            //TODO 后续改为根据userCoupon的transactionType来确定优惠券类型
            else if(couponType == 3) {
                couponTypeStr = "非收入优惠券";
            }
            else if(couponType == 4) {
                couponTypeStr = "购买类优惠券";
            }
        }

        //使用限制
        String serviceTypeStr = "不限";
        if(couponModel != null) {
            if (couponModel.getServiceType() == 1) {
                serviceTypeStr = "分时";
            }
            else if (couponModel.getServiceType() == 2) {
                serviceTypeStr = "短租";
            }
            else if (couponModel.getServiceType() == 3) {
                serviceTypeStr = "长租";
            }
            else if (couponModel.getServiceType() == 4) {
                serviceTypeStr = "充电";
            }
        }
        //限制
        String minAmountStr = StringUtils.EMPTY;
        if (couponModel.getMinAmount() != null) {
            minAmountStr = couponModel.getMinAmount() + "元";
        } else if(couponModel.getDurationLimit() != null){
            minAmountStr = couponModel.getDurationLimit() + "小时";
        }

        String availableDaysOfWeek = couponModel.getAvailableDaysOfWeek();
        if (StringUtils.isNotBlank(availableDaysOfWeek) && !"1,2,3,4,5,6,7".equals(availableDaysOfWeek)) {
            availableDaysOfWeek = availableDaysOfWeek.replace("1", "周一");
            availableDaysOfWeek = availableDaysOfWeek.replace("2", "周二");
            availableDaysOfWeek = availableDaysOfWeek.replace("3", "周三");
            availableDaysOfWeek = availableDaysOfWeek.replace("4", "周四");
            availableDaysOfWeek = availableDaysOfWeek.replace("5", "周五");
            availableDaysOfWeek = availableDaysOfWeek.replace("6", "周六");
            availableDaysOfWeek = availableDaysOfWeek.replace("7", "周日");
        }
        String availableDayStr = "法定节假日通用";
        if(couponModel.getHolidaysAvailable() != null && 0 == couponModel.getHolidaysAvailable()) {
            availableDayStr = "法定节假日不可用";
        }
        String timeStr = StringUtils.EMPTY;
        try {
            if (StringUtils.isNotBlank(couponModel.getStartTime()) && StringUtils.isNotBlank(couponModel.getEndTime())) {
                timeStr = ComUtil.getTime(couponModel.getStartTime(), "HHmm", "HH:mm", 4) + "-"
                        + ComUtil.getTime(couponModel.getEndTime(), "HHmm", "HH:mm", 4);
            }
        }catch (Exception ex) {}

        //时间限制类型
        String timeTypeStr = StringUtils.EMPTY;
        if (couponModel.getTimeType() != null) {
            if (0 == couponModel.getTimeType()) {
                timeTypeStr = "不限";
            } else if (1 == couponModel.getTimeType()) {
                timeTypeStr = "取车时间";
            } else if (2 == couponModel.getTimeType()) {
                timeTypeStr = "还车时间";
            } else if (3 == couponModel.getTimeType()) {
                timeTypeStr = "取/还车时间";
            }
        }

        String overlapStr = "可叠加包日包夜租";
        if(couponModel.getActivityOverlap() != null && 1 == couponModel.getActivityOverlap()) {
            overlapStr = "不可叠加包日包夜租";
        }

        StringBuffer packageIdStr = new StringBuffer();
        if(CollectionUtils.isNotEmpty(couponModel.getPackages())) {
            for (PackageViewDTO packageDto : couponModel.getPackages()) {
                packageIdStr.append(packageDto.getRentMethodDesc() + packageDto.getId() + packageDto.getName()).append("；");
            }
        }

        //10.22 add. 券码导出：添加优惠券使用状态、过期状态查询
        String stateStr = StringUtils.EMPTY;
        String expireStatusStr = StringUtils.EMPTY;
        //String stateStr = (userCoupon.getStatus() == null ||  userCoupon.getStatus() == 0) ? "未使用" : "已使用";
        boolean isExpired = DateUtils.addDays(ComUtil.getDateFromStr(userCoupon.getExpiresDate(), ComUtil.DATE_TYPE5), 1).compareTo(new Date()) < 0;
        if(userCoupon.getStatus() == null ||  (userCoupon.getStatus() == 0 && !isExpired)) {
            stateStr = "未使用";
            expireStatusStr = "未过期";
        }else if(userCoupon.getStatus() == 1) {
            stateStr = "已使用";
        }else if(userCoupon.getStatus() == 2) {
            stateStr = "已作废";
        }else {
            if (isExpired){
                expireStatusStr = "已过期";
            }
        }

        //12.27 add. 券码导出：添加优惠券使用业务线产品限制，使用场景限制查询
        String rentMethodVal = "不限";
        String userMethodVal = StringUtils.EMPTY;
        if(StringUtils.isNotBlank(couponModel.getRentMethodGroup())){
            rentMethodVal = StringUtils.EMPTY;
            // 0 即时分时 1 预约分时 2即时日租 3预约日租, 空表示不限
            if(couponModel.getRentMethod().contains("1")){
                rentMethodVal+="时租，";
            }
            if(couponModel.getRentMethod().contains("2")){
                rentMethodVal+="日租";
                if(StringUtils.contains(couponModel.getUseMethod(),"0")){
                    userMethodVal = "上门送取车";
                }
            }
        }
//        if(StringUtils.isNotBlank(couponModel.getRentMethod())){
//            rentMethodVal = StringUtils.EMPTY;
//            // 0 即时分时 1 预约分时 2即时日租 3预约日租, 空表示不限
//            if(couponModel.getRentMethod().contains("0")){
//                rentMethodVal+="即时分时，";
//            }
//            if(couponModel.getRentMethod().contains("1")){
//                rentMethodVal+="预约分时，";
//            }
//            if(couponModel.getRentMethod().contains("2")){
//                rentMethodVal+="即时日租，";
//            }
//            if(couponModel.getRentMethod().contains("3")){
//                rentMethodVal+="预约日租";
//                if(StringUtils.contains(couponModel.getUseMethod(),"0")){
//                    userMethodVal = "上门送取车";
//                }
//            }
//        }
        //end.

        String createDateStr = StringUtils.EMPTY;
        String useDateStr = StringUtils.EMPTY;
        try {
            if (userCoupon.getStatus() != null && userCoupon.getStatus() == 1) {
                useDateStr = ComUtil.getTime(userCoupon.getUpdatedTime(), ComUtil.DATE_TYPE4, ComUtil.DATE_TYPE1, 14);
            }
            createDateStr = ComUtil.getTime(userCoupon.getCreatedTime(), ComUtil.DATE_TYPE4, ComUtil.DATE_TYPE1, 14);
        }catch (Exception ex) {}

        String startDate = ComUtil.ObjectToString(userCoupon.getStartDate()); // 第二十七列：有效期开始时间
        String endDate = ComUtil.ObjectToString(userCoupon.getExpiresDate()); // 第二十八列：有效期结束时间
        //未兑换且为有效时长的券，显示模板有效时间
        if(StringUtils.isBlank(userCoupon.getAuthId()) && couponModel.getValidTimeType() != null
                && couponModel.getValidTimeType() == 2) {
            startDate = "到账" + couponModel.getEffectiveDays() + "天有效";
            endDate = "有效" + couponModel.getValidDays() + "天";
        }

        String vehicleModleName = couponModel.getGoodsModelName();
//        if(StringUtils.isBlank(vehicleModleName)) {
//            vehicleModleName = couponModel.getVehicleModelName();
//        }

        // 兑换码有效期
        String couponCodeExpireDesc = "长期有效";
        Date couponCodeExpiresTime = userCoupon.getCouponCodeExpiresTime();
        if(couponCodeExpiresTime != null) {
            couponCodeExpireDesc = com.extracme.evcard.mmp.common.DateUtils.getFormatDate(couponCodeExpiresTime, ComUtil.DATE_TYPE13);
        }

        // 兑换码状态
        String couponCodeStatus = "未兑换";
        if(userCoupon.getCouponCodeStatus() != null){
            // 一码多券
            if (userCoupon.getCouponCodeStatus() == 1 || userCoupon.getCouponCodeStatus() == 2) {
                couponCodeStatus = "已兑换";
            }else{
                if (couponCodeExpiresTime != null && couponCodeExpiresTime.before(new Date())) {
                    couponCodeStatus = "已过期";
                }
            }
        }else{
            // 非一码多券
            if (StringUtils.isNotBlank(userCoupon.getAuthId())) {
                couponCodeStatus = "已兑换";
            }else{
                if (couponCodeExpiresTime != null && couponCodeExpiresTime.before(new Date())) {
                    couponCodeStatus = "已过期";
                }
            }
        }

        String[] content = {
                (member == null) ? StringUtils.EMPTY : member.getName(), // 第一列：姓名
                (member == null) ? StringUtils.EMPTY : member.getMobilePhone(),// 第二列：手机号
                ComUtil.ObjectToString(activityDetailDto.getId()), // 第三列：活动编号
                activityDetailDto.getActivityName(), // 第四列：活动名称
                activityDetailDto.getActivityType(), // 第五列：活动类型
                activityDetailDto.getOrgName(), // 第六列：活动所属公司
                ComUtil.ObjectToString(userCoupon.getUserCouponSeq()), // 第七列：优惠券ID
                userCoupon.getCouponOrigin(), // 第八列：优惠券名称
                couponTypeStr, // 第九列：优惠券类型
                ComUtil.ObjectToString(couponModel.getDiscountRate()), // 第十列：折扣（%）
                ComUtil.ObjectToString(couponModel.getCouponValue()), // 第十一列：最高可抵扣金额
                serviceTypeStr, // 第十二列：业务限制
                rentMethodVal, //第十三列：产品线限制
                userMethodVal, //第十四列： 使用场景限制
                minAmountStr, // 第十五列：使用门槛
                couponModel.getVehicleNo(), // 第十六列：车牌限制
                vehicleModleName, // 第十七列：车型限制
                (availableDaysOfWeek == null) ? StringUtils.EMPTY : availableDaysOfWeek, // 第十八列：可用天限制
                availableDayStr, // 第十九列：法定节假日限制
                timeStr, // 第二十列：时间限制
                timeTypeStr, // 第十一列：时间限制类型
                couponModel.getPickshopCityName(),
                couponModel.getReturnshopCityName(),
                couponModel.getPickshopName(),
                couponModel.getReturnshopName(),
//                ComUtil.getShopCityName(couponModel.getPickshopCity(), operateCityService), // 第二十二列：取车城市限制
//                ComUtil.getShopCityName(couponModel.getReturnshopCity(), operateCityService), // 第二十三列：还车城市限制
//                shopServ.getShopNames(couponModel.getPickshopSeq()),  // 第二十四列：取车网点限制
//                shopServ.getShopNames(couponModel.getReturnshopSeq()), // 第二十五列：还车网点限制
                overlapStr,// 第二十六列：活动限制
                packageIdStr.toString(), //第二十七列 可用套餐
                stateStr, // 第二十八列：优惠券使用状态
                //compareCurrentDate(userCoupon.getStatus(), userCoupon.getExpiresDate()), // 第二十九列列：优惠券过期状态
                expireStatusStr,
                ComUtil.ObjectToString(startDate), // 第三十列：有效期开始时间
                ComUtil.ObjectToString(endDate), // 第三十一列：有效期结束时间
                useDateStr, // 第三十二列：优惠券使用时间
                activityDetailDto.getIssuer(), // 第三十三列：发放人
                createDateStr, // 第三十四列：发放时间
                userCoupon.getCouponCode(), // 第三十五列：兑换码
                couponCodeExpireDesc, // 第三十六列：兑换码有效期
                couponCodeStatus // 第三十七列：兑换码状态
        };
        return content;
    }


    private boolean writeCSVFile(String filePath, int index, List<SelectUserCouponDto> userCouponList,
                                 CouponBatchImportActivityDetailDTO activityDetailDto,
                                 Map<Integer, List<CouponModelViewListDto>> couponModelMap,
                                 Map<String, MembershipInfo> memberMap) {
        CsvWriter csvWriter = null;
        OutputStream out = null;
        boolean isAppend = true;
        try {
            //1. 文件创建(新建或追加)
            log.warn("导出兑换码|写文件，活动id={}, index={}, buffer=1w, filePath={}", activityDetailDto.getId(), index, filePath);
            //创建文件
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
                isAppend = false;
                log.warn("导出兑换码|创建临时文件完成：filePath={}", filePath);
            }
            //空列表，不写文件
            if(CollectionUtils.isEmpty(userCouponList)) {return true;}
            //创建CSV写对象
            if(isAppend) {
                out = new FileOutputStream(filePath,true);
                BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(out,"GBK"),1024);
                csvWriter = new CsvWriter(bw, ',');
            }
            else{
                csvWriter = new CsvWriter(filePath,',', Charset.forName("GBK"));
                //产生标题行
                String[] headers = PropertyUtils.getProperty(EXPORT_TITLE).split(Contants.COMMA_SIGN_SPLIT_NAME);
                csvWriter.writeRecord(headers);
            }
            for(SelectUserCouponDto couponDto : userCouponList) {
                //StringBuffer sb = new StringBuffer();
                String[] content = buildFullCoupon(couponDto, activityDetailDto, couponModelMap, memberMap);
                csvWriter.writeRecord(content);
            }
            log.warn("导出兑换码|写文件完成，活动id={}, index={}, buffer=1w, filePath={}", activityDetailDto.getId(), index, filePath);
            return true;
        }catch (Exception ex) {
            log.error("个人E币报表导出：写入文件失败fileName=" + filePath, ex);
            log.warn("导出兑换码|写文件失败，活动id={}, index={}, buffer=1w, filePath={}", activityDetailDto.getId(), index, filePath);
            log.warn("导出兑换码|写文件失败", ex);
            return false;
        }
        finally{
            if(csvWriter != null) {
                csvWriter.flush(); // flush
                csvWriter.close();
            }
            IOUtils.closeQuietly(out);
        }
    }

    @Override
    public CreateRedeemCodeCouponModelDTO getCreateRedeemCodeCouponModelPage(ThirdCouponModelParamsBO paramsBO) {
        CreateRedeemCodeCouponModelDTO activityCouponModelDto = new CreateRedeemCodeCouponModelDTO();
        BigDecimal totalCouponValue = new BigDecimal(0);
        PageBeanBO<ThirdCouponModelViewDTO> pageBeanBO = new PageBeanBO<>();
        List<ThirdCouponModelViewDTO> thirdCouponLists = new ArrayList<>();
        //查询相关券模板
        List<MmpThirdCoupon> mmpThirdCouponList =
                mmpThirdCouponMapper.selectAllCouponSeqByThirdId(paramsBO.getThirdActivityId());
        if (CollectionUtils.isNotEmpty(mmpThirdCouponList)) {
            //调用coupon服务查询券模板
            List<Long> couponSeqList = new ArrayList<>();
            for (MmpThirdCoupon coupon : mmpThirdCouponList) {
                couponSeqList.add(coupon.getCouponSeq());
            }
            Page page = new Page();
            if (1 == paramsBO.getIsAll()) {
                page.setCountFlag(true);
            }
            page.setPageNo(paramsBO.getPageNum());
            page.setPageSize(paramsBO.getPageSize());
            PageBeanDto<CouponModelListDto> couponModelPage =
                    couponModelServ.getAllCouponModelsByCouponSeq(couponSeqList, page);
            PageBO pageBO = new PageBO();
            pageBO.setPageNum(couponModelPage.getPage().getPageNo());
            pageBO.setPageSize(couponModelPage.getPage().getPageSize());
            pageBO.setTotal(null == couponModelPage.getPage().getCount() ? 0L : couponModelPage.getPage().getCount());
            List<CouponModelListDto> couponModelListDtos = couponModelPage.getList();

            for (MmpThirdCoupon thirdCoupon : mmpThirdCouponList) {
                for (CouponModelListDto modelDto : couponModelListDtos) {
                    if (thirdCoupon.getCouponSeq().intValue() == modelDto.getCouponSeq().intValue()) {
                        ThirdCouponModelViewDTO thirdCouponModel = new ThirdCouponModelViewDTO();
                        BeanUtils.copyProperties(modelDto, thirdCouponModel);
                        //vehicleModel服务返回名称不同
                        thirdCouponModel.setVehicleModle(modelDto.getVehicleModle());
                        thirdCouponModel.setVehicleModleName(modelDto.getVehicleModelName());
                        thirdCouponModel.setOfferQuantity(thirdCoupon.getOfferQuantity());
                        thirdCouponModel.setCouponName(thirdCoupon.getCouponName());
                        thirdCouponModel.setStartDate(thirdCoupon.getStartDate());
                        thirdCouponModel.setExpiresDate(thirdCoupon.getExpiresDate());
                        thirdCouponModel.setValidDays(thirdCoupon.getValidDays());
                        thirdCouponModel.setEffectiveDays(thirdCoupon.getEffectiveDays());
                        thirdCouponModel.setValidTimeType(thirdCoupon.getValidTimeType());
                        thirdCouponModel.setCouponLimit(thirdCoupon.getCouponLimit());
                        thirdCouponModel.setId(thirdCoupon.getId());
                        //add 4.22 处理couponType
                        thirdCouponModel.setCouponType(thirdCoupon.getCouponType());
                        if(StringUtils.isNotBlank(modelDto.getPackageIds())) {
                            List<PackageViewDTO> packages = marketActivityServiceImpl.getPackageListByIds(modelDto.getServiceType(),
                                    modelDto.getPackageIds());
                            thirdCouponModel.setPackages(packages);
                        }
                        thirdCouponLists.add(thirdCouponModel);
                        //计算优惠券模板金额 只有抵用券金额，折扣券不记录
                        if (1 == thirdCouponModel.getCouponType()) {
                            BigDecimal couponValue = thirdCouponModel.getCouponValue();
                            Integer offerQuantity = thirdCouponModel.getOfferQuantity();
                            totalCouponValue = totalCouponValue.add(couponValue.multiply(new BigDecimal(offerQuantity)));
                        }
                        break;
                    }
                }

            }
            pageBeanBO = new PageBeanBO<ThirdCouponModelViewDTO>(pageBO, thirdCouponLists);
        }
        activityCouponModelDto.setCouponModelPage(pageBeanBO);
        activityCouponModelDto.setTotalCouponValue(totalCouponValue);
        return activityCouponModelDto;
    }

    // 判断优惠券是否过期
    public String compareCurrentDate(Short status, String expiresDate) {
        String flag = "";
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = sdf.format(now);
        if (status == 0) {
            if (expiresDate.compareTo(currentDate) == -1) {
                flag = "已过期";
            } else {
                flag = "未过期";
            }
        }
        return flag;
    }
}
