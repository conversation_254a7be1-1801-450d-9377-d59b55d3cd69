package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.CommonConstant;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IChannelManagementService;
import com.extracme.evcard.mmp.service.ISecondChannelManagementService;
import com.extracme.evcard.mmp.vo.ChannelVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：ChannelManagementController
 * 类描述：渠道管理
 * 创建人：沈建华
 * 创建时间：2018年1月16日上午10:53:30
 * 修改备注
 *
 * @version0.9
 */
@RestController
@RequestMapping("api")
public class ChannelManagementController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IChannelManagementService channelManagementService;

    @Resource
    private ISecondChannelManagementService secondChannelManagementService;
    /**
     * 渠道信息一览
     *
     * @param pageNum     页码
     * @param pageSize    每页显示条数
     * @param platName    渠道名称
     * @param appKey      渠道Key
     * @param createdUser 创建人
     * @param orgId       所属公司
     * @param startTime   创建日期开始时间
     * @param endTime     创建日期结束时间
     * @param isAll       是否显示全件数
     * @param channelType   渠道类型 0：一级渠道  1：二级渠道
     * @return
     */
    @RequestMapping(value = "channelManagement", method = RequestMethod.GET)
    public DefaultWebRespVO channelManagement(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(value = "platName", required = false) String platName,
                                                    @RequestParam(value = "appKey", required = false) String appKey, @RequestParam(value = "createdUser", required = false) String createdUser,
                                                    @RequestParam(value = "orgId", required = false) String orgId, @RequestParam(value = "startTime", required = false) String startTime,
                                                    @RequestParam(value = "endTime", required = false) String endTime, @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, 
                                                    @RequestParam(value = "platformId", required = false) Long platformId,@RequestParam(value = "status", required = false) Integer status,
                                              @RequestParam(value = "firstAppKey", required = false) String firstAppKey,@RequestParam(value = "channelType", defaultValue = "0") Integer channelType,HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO pageBeanBO = null;
            if (channelType == 0) {
                // 一级渠道查询
                pageBeanBO = channelManagementService.getChannelManagement(pageNum, pageSize, platName, appKey, createdUser, orgId, startTime, endTime, isAll, platformId, status, false, request);
            }else{
                // 2级渠道查询
                pageBeanBO = secondChannelManagementService.getChannelManagement(pageNum, pageSize, platName, appKey, firstAppKey,createdUser, orgId, startTime, endTime, isAll, platformId, status, false, request);
            }
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("渠道信息一览...");
        return vo;
    }

    /**
     * 新增渠道
     *
     * @param channelVO
     * @param request
     * @return
     */
    @RequestMapping(value = "channelManagement", method = RequestMethod.POST)
    public DefaultWebRespVO addChannelManagement(@RequestBody ChannelVO channelVO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            ChannelDTO channelDTO = new ChannelDTO();
            BeanCopyUtils.copyProperties(channelVO, channelDTO);
            DefaultServiceRespDTO respDTO = null;
            if (channelVO.getChannelType() == 0) {
                //  新增一级渠道
                respDTO = channelManagementService.addChannelManagement(channelDTO, request);
            }else{
                //  新增2级渠道
                respDTO = secondChannelManagementService.addChannelManagement(channelDTO, request);
            }
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增渠道信息...");
        return vo;
    }

    /**
     * 批量手动生成渠道
     * @param request
     * @return
     */
    @RequestMapping(value = "batchAddChannelManagement", method = RequestMethod.POST)
    public DefaultWebRespVO batchAddChannelManagement(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = channelManagementService.batchAddChannelManagement(request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增渠道信息...");
        return vo;
    }

    /**
     * 修改渠道
     *
     * @param channelVO
     * @param request
     * @return
     */
    @RequestMapping(value = "channelManagement", method = RequestMethod.PUT)
    public DefaultWebRespVO updateChannelManagement(@RequestBody ChannelVO channelVO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            ChannelDTO channelDTO = new ChannelDTO();
            BeanCopyUtils.copyProperties(channelVO, channelDTO);
            DefaultServiceRespDTO respDTO = null;
            if (channelVO.getChannelType() == 0) {
                respDTO = channelManagementService.updateChannelManagement(channelDTO, request);
            }else {
                respDTO = secondChannelManagementService.updateChannelManagement(channelDTO, request);
            }
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改渠道信息...");
        return vo;
    }

    /**
     * 渠道详情
     *
     * @param appKey
     * @param request
     * @return
     */
    @RequestMapping(value = "channelDetail/{appKey}", method = RequestMethod.GET)
    public DefaultWebRespVO getChannelDetail(@PathVariable String appKey, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            ChannelDetailInfoDTO detailInfoDTO = null;
            if (StringUtils.isBlank(appKey)) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("appkey 不能为空");
            }else{
                if (appKey.startsWith(CommonConstant.SECOND_CHANNEL_PRE)) {
                    detailInfoDTO = secondChannelManagementService.getChannelDetail(appKey);
                }else{
                    detailInfoDTO =channelManagementService.getChannelDetail(appKey);
                }
                return DefaultWebRespVO.getSuccessVO(detailInfoDTO);
            }
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("获取渠道详情...");
        return vo;
    }

    /**
     * 查看渠道密钥
     *
     * @param appKey
     * @param request
     * @return
     */
    @RequestMapping(value = "showSecretKey/{appKey}", method = RequestMethod.PUT)
    public DefaultWebRespVO showSecretKey(@PathVariable String appKey, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = null;
        if(StringUtils.isNotBlank(appKey) && appKey.startsWith(CommonConstant.SECOND_CHANNEL_PRE)){
            respDTO = secondChannelManagementService.showSecretKey(appKey, request);
        }else{
            respDTO = channelManagementService.showSecretKey(appKey, request);
        }
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("查看渠道密钥...");
        return DefaultWebRespVO.getSuccessVO(respDTO.getData());
    }

    /**
     * 日志一览
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @param isAll    是否统计
     * @param appKey
     * @return vo 返回值
     */
    @RequestMapping(value = "channelOperatorLog/{appKey}", method = RequestMethod.GET)
    public DefaultWebRespVO queryChannelOperatorLogList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                        @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                        @PathVariable(value = "appKey") String appKey) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO<UserOperatorLogDTO> pageBeanBO =
                    channelManagementService.queryChannelOperatorLogList(pageNum, pageSize, isAll, appKey);
            if (CollectionUtils.isEmpty(pageBeanBO.getList())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("日志记录不存在");
                return vo;
            }
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("日志操作情况一览...");
        return vo;
    }
    
    /**
     * 禁用appKey
     * @param appKey
     * @param request
     * @return
     */
    @RequestMapping(value = "disableAppKey/{appKey}", method = RequestMethod.PUT)
    public DefaultWebRespVO disableAppKey(@PathVariable String appKey, HttpServletRequest request) {
    	DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = null;
            if(StringUtils.isNotBlank(appKey) && appKey.startsWith(CommonConstant.SECOND_CHANNEL_PRE)){
                respDTO = secondChannelManagementService.disableAppKey(appKey, request);
            }else{
                respDTO = channelManagementService.disableAppKey(appKey, request);
            }
			if (respDTO.getCode() == -1) {
			    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("禁用appKey异常",e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
		}
        log.debug("禁用appKey...");
        vo.setMessage("禁用成功");
        return vo;
    }
    
    /**
     * 启用appKey
     * @param appKey
     * @param request
     * @return
     */
    @RequestMapping(value = "enableAppKey/{appKey}", method = RequestMethod.PUT)
    public DefaultWebRespVO enableAppKey(@PathVariable String appKey, HttpServletRequest request) {
    	DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
            DefaultServiceRespDTO respDTO = null;
            if(StringUtils.isNotBlank(appKey) && appKey.startsWith(CommonConstant.SECOND_CHANNEL_PRE)){
                respDTO = secondChannelManagementService.enableAppKey(appKey, request);
            }else{
                respDTO = channelManagementService.enableAppKey(appKey, request);
            }
			if (respDTO.getCode() == -1) {
			    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("启用appKey异常",e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("启用失败");
            return vo;
		}
        log.debug("启用appKey...");
        vo.setMessage("启用成功");
        return vo;
    }
    
    /**
     * 渠道用途
     * @return
     */
    @RequestMapping(value = "getChannelPurpose", method = RequestMethod.GET)
    public DefaultWebRespVO getChannelPurpose() {
        List<ChannelPurposeDTO> list = channelManagementService.getChannelPurpose();
        return DefaultWebRespVO.getSuccessVO(list);
    }

    /**
     * 渠道查询列表
     *
     * @param pageNum     页码
     * @param pageSize    每页显示条数
     * @param platName    渠道名称
     * @param appKey      渠道Key
     * @param isAll       是否显示全件数
     * @param channelType      渠道类型 0：一级渠道  1：二级渠道
     * @param firstAppKey      channelType 为1时， 才有值
     * @return
     * @remark 用于活动渠道选择
     */
    @RequestMapping(value = "channelManagement/list", method = RequestMethod.GET)
    public DefaultWebRespVO channelManagementList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                    @RequestParam(value = "appKeyName", required = false) String platName,
                                                    @RequestParam(value = "appKey", required = false) String appKey,
                                                    @RequestParam(value = "platformId", required = false) Long platformId,
                                                    @RequestParam(value = "channelType", defaultValue = "0") Integer channelType,
                                                    @RequestParam(value = "firstAppKey", required = false) String firstAppKey,
                                                    HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO pageBeanBO = null;
            if (channelType == 0) {
                pageBeanBO = channelManagementService.getChannelManagement(pageNum, pageSize, platName,
                        appKey, null, null, null, null, isAll, platformId, 0, true, request);
            }else{
                pageBeanBO = secondChannelManagementService.getChannelManagement(pageNum, pageSize, platName,
                        appKey,firstAppKey, null, null, null, null, isAll, platformId, 0, true, request);
            }
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("渠道信息一览...");
        return vo;
    }

    /**
     * 获取二级渠道来源下拉框
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "secondAppKey/list", method = RequestMethod.POST)
    public DefaultWebRespVO queryAppAllList(@RequestBody SecondAppKeyDTO appDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<AppDTO> secondAppList = secondChannelManagementService.getSecondAppKeys(appDTO);
            return DefaultWebRespVO.getSuccessVO(secondAppList);
        } catch (Exception e) {
            log.error("获取二级渠道来源下拉框,异常",e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        return vo;
    }
}
