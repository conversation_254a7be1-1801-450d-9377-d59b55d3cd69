package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.MakeCardBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MakeCardInfoDTO;
import com.extracme.evcard.mmp.dto.MakeCardInfoDetailDTO;
import com.extracme.evcard.mmp.dto.MakeCardInfoPageDTO;
import com.extracme.evcard.mmp.service.IMakeCardService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * MarkCardController class
 *
 * <AUTHOR>
 * @date 2018/2/26 16:27
 * Description 制卡账户权限分配
 */
@RestController
@RequestMapping("api/markCard")
public class MakeCardController {

    @Resource
    private IMakeCardService makeCardServiceImpl;

    /**
     * 账户权限分配新增
     * @param makeCardInfoDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "addMakeCardInfo", method = RequestMethod.POST)
    public DefaultWebRespVO addMakeCardInfo(@RequestBody MakeCardInfoDTO makeCardInfoDTO,
                                            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = makeCardServiceImpl.addMakeCardInfo(makeCardInfoDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setMessage("添加失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 获取账户权限信息
     * @param userId 账户名称
     * @return
     */
    @RequestMapping(value = "makeCardInfoDetail/{userId}", method = RequestMethod.GET)
    public DefaultWebRespVO makeCardInfoDetail(@PathVariable("userId") String userId) {
        MakeCardInfoDetailDTO cardInfoDetailDTO = makeCardServiceImpl.makeCardInfoDetail(userId);
        return DefaultWebRespVO.getSuccessVO(cardInfoDetailDTO);
    }

    /**
     * 账户权限列表
     * @param makeCardBO
     * @return
     */
    @RequestMapping(value = "getMakeCardListPage", method = RequestMethod.POST)
    public DefaultWebRespVO getMakeCardListPage(@RequestBody MakeCardBO makeCardBO){
        PageBeanBO<MakeCardInfoPageDTO> pageBeanBO = makeCardServiceImpl.getMakeCardListPage(makeCardBO);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 模糊查询，获取所有账户
     * @return
     */
    @RequestMapping(value = "getAllUserIds", method = RequestMethod.GET)
    public DefaultWebRespVO getAllUserIds() {
        List<String> list = makeCardServiceImpl.getAllUserIds();
        return DefaultWebRespVO.getSuccessVO(list);
    }
}