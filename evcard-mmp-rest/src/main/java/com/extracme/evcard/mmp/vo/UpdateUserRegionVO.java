package com.extracme.evcard.mmp.vo;

import java.util.List;

/**
 * 
 * 项目名称：evcard-mmp-rest
 * 类名称：QueryShopInfoListVO
 * 类描述：查询网点列表 接口的传入参数
 * 创建人：chennian-谌年
 * 创建时间：2017年3月3日 上午10:40:38
 * 修改备注：
 * @version1.0
 *
 */
public class UpdateUserRegionVO {
	
	/** 用户id **/
    private Long userId;
	
	/** 运营区域 **/
	private List<String> regionidList;
	
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public List<String> getRegionidList() {
		return regionidList;
	}

	public void setRegionidList(List<String> regionidList) {
		this.regionidList = regionidList;
	}
	
}
