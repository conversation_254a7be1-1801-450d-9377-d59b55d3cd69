package com.extracme.evcard.mmp.tcs.rest;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.service.tcs.ITcsTaskCenterService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.tcs.provider.api.dto.AdConfigDTO;
import com.extracme.evcard.tcs.provider.api.dto.TaskOperateLogDTO;
import com.extracme.evcard.tcs.provider.api.dto.model.PackageFile;
import com.extracme.evcard.tcs.provider.api.dto.model.PageBeanDto;
import com.extracme.evcard.tcs.provider.api.dto.model.UserMessgae;
import com.extracme.evcard.tcs.provider.api.service.IAdConfigServiceProvider;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/06
 * 广告位配置
 */
@RestController
@RequestMapping("api/adConfig")
public class TaskAdConfigController {
    private static final Logger logger = LoggerFactory.getLogger(TaskAdConfigController.class);

    @Resource
    IAdConfigServiceProvider adConfigServiceProvider;

    @Resource
    ITcsTaskCenterService tcsTaskCenterServiceImpl;

    /**
     * 配置页信息 and 查询
     * @param
     * @return
     */
    @RequestMapping(value = "initLoad", method = RequestMethod.GET)
    public DefaultWebRespVO initLoad(@RequestParam(value = "pageNum",defaultValue = "1")Integer pageNum,
                                     @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                     @RequestParam(value = "status", required = false)Integer status, @RequestParam(value = "location", required = false)Integer location,
                                     @RequestParam(value = "startTime", required = false)String startTime, @RequestParam(value = "endTime", required = false)String endTime){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        PageBeanDto<AdConfigDTO> pageBeanDto = adConfigServiceProvider.initLoad(pageNum, pageSize, status, location, startTime, endTime);
        if (null != pageBeanDto){
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setData(pageBeanDto);
            defaultWebRespVO.setMessage("成功！");
        }else{
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    @RequestMapping(value = "getDetail", method = RequestMethod.GET)
    public DefaultWebRespVO getDetail(@RequestParam("id") Long id){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            AdConfigDTO adConfigDTO = adConfigServiceProvider.getDetail(id);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setData(adConfigDTO);
            defaultWebRespVO.setMessage("成功");
        }catch (Exception e){
            logger.error("获取详情失败，id=" + id, e);
            defaultWebRespVO.setData("-1");
            defaultWebRespVO.setMessage("失败");
        }
        return defaultWebRespVO;
    }

    /**
     * 新增
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public DefaultWebRespVO save(@RequestParam("location")Integer location, @RequestParam("startTime")String startTime,
                                 @RequestParam("endTime")String endTime, @RequestParam(value = "warterMark", defaultValue = "0")Integer warterMark,
                                 HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getCreateOperId());
        userMessgae.setUpdateOperName(comModel.getCreateOperName());
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        CommonsMultipartFile multipartFile = null;
        String fileName = null;
        PackageFile file = new PackageFile();
        try {
            multipartFile = (CommonsMultipartFile)multipartRequest.getFile("adImageFile");
            fileName = multipartFile.getOriginalFilename();
            file.setName(fileName);
            file.setBytes(multipartFile.getBytes());
        }catch (Exception e){
            e.printStackTrace();
        }
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            BaseResponse result = adConfigServiceProvider.save(location,startTime, endTime, warterMark, file, userMessgae);
            if (result.getCode() != 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("新增失败，location=" + location, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("新增失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 修改
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public DefaultWebRespVO update(@RequestParam(value = "id")Long id, @RequestParam(value = "location") Integer location, @RequestParam(value = "startTime")String startTime,@RequestParam(value = "endTime")String endTime,
                                   @RequestParam(value = "adImageFile", required = false) CommonsMultipartFile multipartFile, HttpServletRequest request){
        AdConfigDTO updateAdConfig = new AdConfigDTO();
        updateAdConfig.setId(id);
        updateAdConfig.setLocation(location);
        updateAdConfig.setStartTime(startTime);
        updateAdConfig.setEndTime(endTime);
        System.out.println(JSON.toJSONString(updateAdConfig));

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        PackageFile file = null;
        if (multipartFile != null) {
            file = new PackageFile();
            file.setBytes(multipartFile.getBytes());
            file.setName(multipartFile.getOriginalFilename());
        }
        updateAdConfig.setFile(file);
        try {
            BaseResponse baseResponse = adConfigServiceProvider.update(updateAdConfig, userMessgae);
            if (baseResponse.getCode() != 0) {
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(baseResponse.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("修改成功！");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("修改失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 发布
     * @param id
     * @return
     */
    @RequestMapping(value = "release/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO release(@PathVariable Long id, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        AdConfigDTO updateAdConfig = new AdConfigDTO();
        updateAdConfig.setId(id);
        try {
            BaseResponse result = adConfigServiceProvider.release(updateAdConfig, userMessgae);
            if (result.getCode() != 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("发布成功！");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setMessage("发布失败！");
            defaultWebRespVO.setCode("-1");
        }
        return defaultWebRespVO;
    }

    public void setMessage(Long id,UserMessgae userMessgae, AdConfigDTO updateAdConfig, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());
        updateAdConfig.setId(id);
    }

    /**
     * 上线
     * @return
     */
    @RequestMapping(value = "online/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO online(@PathVariable Long id, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        AdConfigDTO updateAdConfig = new AdConfigDTO();
        updateAdConfig.setId(id);
        try {
            BaseResponse baseResponse = adConfigServiceProvider.online(updateAdConfig, userMessgae);
            if (baseResponse.getCode() != 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(baseResponse.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("上线成功！");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setMessage("上线失败！");
            defaultWebRespVO.setCode("-1");
        }
        return defaultWebRespVO;
    }

    /**
     * 停止
     * @param id
     * @return
     */
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO stop(@PathVariable Long id, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        AdConfigDTO updateAdConfig = new AdConfigDTO();
        updateAdConfig.setId(id);
        try {
            adConfigServiceProvider.stop(updateAdConfig, userMessgae);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("停止成功！");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setMessage("停止失败！");
            defaultWebRespVO.setCode("-1");
        }
        return defaultWebRespVO;
    }

    /**
     * 暂停
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "timeOut/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO timeout(@PathVariable Long id, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        AdConfigDTO updateAdConfig = new AdConfigDTO();
        updateAdConfig.setId(id);
        try {
            adConfigServiceProvider.timeOut(updateAdConfig, userMessgae);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("暂停成功！");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setMessage("暂停失败！");
            defaultWebRespVO.setCode("-1");
        }
        return defaultWebRespVO;
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO delete(@PathVariable Long id, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        AdConfigDTO updateAdConfig = new AdConfigDTO();
        updateAdConfig.setId(id);
        try {
            adConfigServiceProvider.delete(updateAdConfig, userMessgae);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("删除成功！");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setMessage("删除失败！");
            defaultWebRespVO.setCode("-1");
        }
        return defaultWebRespVO;
    }

    /**
     * 日志展示
     * @param id
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "showLog", method = RequestMethod.GET)
    public DefaultWebRespVO showLog(@RequestParam("id") Long id, @RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                    @RequestParam(value = "isAll", defaultValue = "1")Integer isAll){
        List<Long> configIds = Arrays.asList(id);
        PageBeanBO<TaskOperateLogDTO> pageBean = tcsTaskCenterServiceImpl.queryLogs(configIds, 1,
                pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }
}
