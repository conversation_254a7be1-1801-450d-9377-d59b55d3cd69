package com.extracme.evcard.mmp.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020/2/19 16:39
 */
@Data
public class SearchDailyRentDefaultPriceConfigListVo extends PageInfoVO{

    /**
     * 运营机构id
     */
    private String orgId;

    /**
     * 车型编号
     */
    private Long vehicleModelSeq;

    /**
     * 配置状态
     * 默认价格：1待生效 2生效中 3已失效 5待失效
     */
    private Integer configStatus;

    private Long id;

    /**
     * 是否有默认价格
     * 0否 1是
     */
    private Integer defaultPriceFlag;

    /**
     * 是否有油价
     * 0否 1是
     */
    private Integer oilPriceFlag;

    /**
     *  车牌类型 -1不区分车牌 0车牌 1非配置车牌
     */
    private Integer matchType;
}