package com.extracme.evcard.mmp.tcs.rest;

import com.extracme.evcard.tcs.provider.api.bo.TaskStatisticsQueryBO;
import com.extracme.evcard.tcs.provider.api.dto.TaskDailyStatisticsDTO;
import com.extracme.evcard.tcs.provider.api.dto.TaskStatisticsDTO;
import com.extracme.evcard.tcs.provider.api.service.task.ITaskCenterServiceProvider;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tools.ant.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/09/06
 * 任务中心统计
 */
@RestController
@RequestMapping("api/taskStatistics")
public class TaskStatisticsController {
    private static final Logger logger = LoggerFactory.getLogger(TaskStatisticsController.class);

    @Resource
    ITaskCenterServiceProvider iTaskCenterServiceProvider;


    @RequestMapping(value = "initLoad", method = RequestMethod.GET)
    public DefaultWebRespVO initLoad(@RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                     @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                     @RequestParam(value = "startTime",required = false)String startTime,
                                     @RequestParam(value = "endTime", required = false)String endTime){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        TaskStatisticsQueryBO taskStatisticsQueryBO = new TaskStatisticsQueryBO();
        taskStatisticsQueryBO.setPageSize(pageSize);
        taskStatisticsQueryBO.setPageNum(pageNum);
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.DAY_OF_MONTH, -7);
            startTime = DateUtils.format(c.getTime(), DateUtils.ISO8601_DATE_PATTERN);
            c.add(Calendar.DAY_OF_MONTH, 6);
            endTime = DateUtils.format(c.getTime(), DateUtils.ISO8601_DATE_PATTERN);
//            endTime = startTime;
        }
        taskStatisticsQueryBO.setStartDate(startTime);
        taskStatisticsQueryBO.setEndDate(endTime);
        taskStatisticsQueryBO.setIsAll(1);
        try {
            PageBeanBO<TaskDailyStatisticsDTO> pageBeanBO = iTaskCenterServiceProvider.queryStatistics(taskStatisticsQueryBO);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功");
            defaultWebRespVO.setData(pageBeanBO);
        }catch (Exception e){
            logger.error("查询失败", e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败");
        }
        return defaultWebRespVO;
    }

    @RequestMapping(value = "export", method = RequestMethod.GET)
    public void export(@RequestParam("startTime")String startTime,
                       @RequestParam("endTime")String endTime,
                       HttpServletRequest request, HttpServletResponse response){
        TaskStatisticsQueryBO taskStatisticsQueryBO = new TaskStatisticsQueryBO();
        taskStatisticsQueryBO.setPageNum(1);
        SimpleDateFormat dataFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        Long time1 = null;
        Long time2 = null;
        try {
            cal.setTime(dataFormat.parse(startTime));
            time1 = cal.getTimeInMillis();
            cal.setTime(dataFormat.parse(endTime));
            time2 = cal.getTimeInMillis();
        }catch (Exception e){
            logger.error("日期转换失败", e);
        }
//        if (Integer.parseInt(String.valueOf(time2 - time1)) >= 1) {
//            taskStatisticsQueryBO.setPageSize(Integer.parseInt(String.valueOf((time2 - time1) / (1000 * 3600 * 24))) * 4);
//        }else{
//            taskStatisticsQueryBO.setPageSize(Integer.parseInt(String.valueOf(1 * 4)));
//        }
        //不分页不计数
        taskStatisticsQueryBO.setIsAll(0);
        taskStatisticsQueryBO.setPageSize(50000);
        taskStatisticsQueryBO.setStartDate(startTime);
        taskStatisticsQueryBO.setEndDate(endTime);
        PageBeanBO<TaskDailyStatisticsDTO> pageBeanBO = iTaskCenterServiceProvider.queryStatistics(taskStatisticsQueryBO);
        //创建工作薄
        XSSFWorkbook wb = new XSSFWorkbook();
        //创建一个sheet
        XSSFSheet sheet = wb.createSheet();
        //第一行数据
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("日期");
        row.createCell(1).setCellValue("任务类型");
        row.createCell(2).setCellValue("任务参与人数");
        row.createCell(3).setCellValue("任务发券张数");
        row.createCell(4).setCellValue("券使用张数");
        row.createCell(5).setCellValue("订单内抵扣金额");
        row.createCell(6).setCellValue("任务总参与人数");
        List<TaskDailyStatisticsDTO> taskDailyStatisticsDTOList = pageBeanBO.getList();
        int indexRow = 1;
        int oldIndexRow = indexRow;
        for (int i = 0; i< taskDailyStatisticsDTOList.size(); i++){
            for (TaskStatisticsDTO taskStatisticsDTO : taskDailyStatisticsDTOList.get(i).getTasks()){
                Row row1 = sheet.createRow(indexRow);
                row1.createCell(0).setCellValue(taskDailyStatisticsDTOList.get(i).getDate());
                row1.createCell(1).setCellValue(taskStatisticsDTO.getTaskName());
                row1.createCell(2).setCellValue(taskStatisticsDTO.getTotalUser());
                row1.createCell(3).setCellValue(taskStatisticsDTO.getTotalCoupon());
                row1.createCell(4).setCellValue(taskStatisticsDTO.getTotalCouponUsed());
                row1.createCell(5).setCellValue(taskStatisticsDTO.getOrderDiscountAmount().toString());
                row1.createCell(6).setCellValue(taskDailyStatisticsDTOList.get(i).getTotal());
                indexRow ++;
            }
            sheet.addMergedRegion(new CellRangeAddress(oldIndexRow, indexRow-1, 6, 6));
            oldIndexRow = indexRow;
        }

        OutputStream os = null;
        try {
            // 设置文件后缀
            String fileName = "TaskDailyStatistics_";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fn = fileName + sdf.format(new Date()) + ".xlsx";
            // 读取字符编码
            String utf = "UTF-8";

            // 设置响应
            response.setContentType("application/xlsx");
            response.setCharacterEncoding(utf);
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "max-age=30");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fn, utf));
            os = response.getOutputStream();
            wb.write(os);
            os.flush();
        }catch (Exception e){
            logger.error("文件下载失败", e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }

}
