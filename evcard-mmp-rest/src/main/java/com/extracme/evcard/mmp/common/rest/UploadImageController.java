package com.extracme.evcard.mmp.common.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.UploadImageService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.*;

/**
 * 项目名称：evcard-fas-rest
 * 类名称：UploadImageController
 * 类描述：上传图片
 * 创建人：shenjh-沈建华
 * 创建时间：2017年4月13日 上午9:48:14
 * 修改备注：添加了service层面，方便调用。
 * <AUTHOR>
 * @version1.0
 */
@Api(value="uploadFile", tags = "U4-文件上传")
@RestController
@RequestMapping("api")
public class UploadImageController {

    @Resource
    UploadImageService uploadImageService;


    /**
     * 上传图片
     * @param file
     * @param type
     * @param id
     * @param resType
     * @return
     */
    @ApiOperation(value="4.0 图片上传", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "file",value = "文件对象",dataType = "file", required = true, paramType = "form-data"),
            @ApiImplicitParam(name = "type",value = "文件类别，1活动图片, 2信用事件, 3会员条款, 4企业会员, 5客服任务",dataType = "int",required = false, paramType = "form-data",
                    allowableValues= "{@code 1活动图片, 2信用事件, 3会员条款, 4企业会员, 5客服任务}"),
            @ApiImplicitParam(name = "id",value = "id，会员pk_id/活动id/任务id等，新增时无可不传",dataType = "Long", required = false, paramType = "form-data"),
            @ApiImplicitParam(name = "resType",value = "会员资源类别 1驾照正页, 2驾照副页, 3证件照, 4手持证件照, 5人脸照片，仅会员图片使用，将上传至加密bucket;",
                    dataType = "Long",required = false, paramType = "form-data",
                    allowableValues = "{@code 1驾照正页, 2驾照副页, 3证件照, 4手持证件照, 5人脸照片"),
    })
    @ApiResponses(value={@ApiResponse(code=0, message="SUCCESS，成功；此时data为文件全路径"),
            @ApiResponse(code=-1, message="0以外其他错误码，失败"),
    })
    @RequestMapping(value = "uploadImage", method = RequestMethod.POST)
    public BaseResultVo<String> uploadImage(@RequestParam("file") CommonsMultipartFile file,
                                            @RequestParam(value = "type", required = false) String type,
                                            @RequestParam(value = "id", defaultValue = "") String id,
                                            @RequestParam(value = "resType", required = false) Integer resType) {
        BaseResultVo<String> vo = new BaseResultVo();
        DefaultServiceRespDTO defaultServiceRespDTO = uploadImageService.uploadImage(file, type, id, resType);
        if (defaultServiceRespDTO.getCode() == -1){
            vo.setMessage(defaultServiceRespDTO.getMessage());
            vo.setCode(Contants.RETURN_ERROR_CODE);
            return vo;
        }else {
            return BaseResultVo.getSuccessVO(defaultServiceRespDTO.getData());
        }
    }

    /**
     * 上传敏感图片
     * @param file
     * @param id
     * @param resType
     * @return
     */
    @ApiOperation(value="4.1 敏感图片上传(会员图片)", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "file",value = "文件对象",dataType = "file", required = true, paramType = "form-data"),
            @ApiImplicitParam(name = "id",value = "会员id",dataType = "Long", required = false, paramType = "form-data",
                    allowableValues = "会员pk_id，新增时无可不传"),
            @ApiImplicitParam(name = "resType",value = "会员id",dataType = "Long",required = false, paramType = "form-data",
                    allowableValues = "{@code 1驾照正页, 2驾照副页, 3证件照, 4手持证件照, 5人脸照片"),
    })
    @ApiResponses(value={@ApiResponse(code=0, message="SUCCESS，成功, data为文件全路径"),
            @ApiResponse(code=-1, message="0以外其他错误码，失败"),
    })
    @RequestMapping(value = "uploadSecretImage", method = RequestMethod.POST)
    public DefaultWebRespVO uploadSecretImage(@RequestParam("file") CommonsMultipartFile file,
                                        @RequestParam(value = "id", defaultValue = "") String id,
                                        @RequestParam(value = "resType", required = false) Integer resType) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        DefaultServiceRespDTO defaultServiceRespDTO = uploadImageService.uploadImage(file, "", id, resType);
        if (defaultServiceRespDTO.getCode() == -1){
            vo.setMessage(defaultServiceRespDTO.getMessage());
            vo.setCode(Contants.RETURN_ERROR_CODE);
            return vo;
        }else {
            return DefaultWebRespVO.getSuccessVO(defaultServiceRespDTO.getData());
        }
    }


    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return
     * @throws IOException
     */
    @ApiOperation(value="4.2 文件上传（非敏感）", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "file",value = "文件对象", dataType = "file", required = true, paramType = "form-data"),
            @ApiImplicitParam(name = "type",value = "文件类别", dataType = "int",required = true, paramType = "form-data",
                    allowableValues= "{@code 1活动图片, 2信用事件, 3会员条款, 4企业会员, 5客服任务}"),
            @ApiImplicitParam(name = "id",value = "id",dataType = "Long", required = false, paramType = "form-data",
                    allowableValues = "id，新增时无可不传"),
    })
    @ApiResponses(value={@ApiResponse(code=0, message="SUCCESS，成功, data为文件全路径"),
            @ApiResponse(code=-1, message="0以外其他错误码，失败"),
    })
    @RequestMapping(value = "uploadFile", method = RequestMethod.POST)
    public DefaultWebRespVO uploadFile(@RequestParam("file") CommonsMultipartFile file,
                                       @RequestParam("type") String type,
                                       @RequestParam(value = "id", defaultValue = "") String id) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        DefaultServiceRespDTO defaultServiceRespDTO = uploadImageService.uploadFile(file, type, id);
        if (defaultServiceRespDTO.getCode() == -1){
            vo.setMessage("上传失败："+defaultServiceRespDTO.getMessage());
            vo.setCode(Contants.RETURN_ERROR_CODE);
            return vo;
        }else{
            return DefaultWebRespVO.getSuccessVO(defaultServiceRespDTO.getData());
        }
    }
}
