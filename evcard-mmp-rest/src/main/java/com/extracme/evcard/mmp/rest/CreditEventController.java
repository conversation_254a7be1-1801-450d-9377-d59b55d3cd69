package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.membership.credit.service.IMemberShipTagServ;
import com.extracme.evcard.mmp.bo.*;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.common.PropertyUtils;
import com.extracme.evcard.mmp.service.UserServiceImpl;
import com.extracme.evcard.mmp.vo.*;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.UserDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Elin on 2017/11/24.
 * 信用事件
 */
@RestController
@RequestMapping("api/credit")
public class CreditEventController {

    /**
     * Excel的标题
     */
    private static final String EVENT_TYPE_REPORT_TITLE = "export.EventTypeReport.title";
    
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IMemberShipTagServ memberShipTagServ;
    @Resource
    UserServiceImpl userServiceImpl;

    /**
     * 获取信用事件类型
     *
     * @return
     */
    @RequestMapping(value = "getCreditEventTypes", method = RequestMethod.GET)
    public DefaultWebRespVO getCreditEventTypes() {
        List<CreditEventTypeDto> creditEventTypeDtos = memberShipTagServ.getCreditEventTypes();
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        defaultWebRespVO.setData(creditEventTypeDtos);
        return defaultWebRespVO;
    }

    /**
     * 新增事件类型
     *
     * @param typeFullDto
     * @return
     */
    @RequestMapping(value = "addCreditEventType", method = RequestMethod.POST)
    public DefaultWebRespVO addCreditEventType(@RequestBody CreditEventTypeFullDto typeFullDto,
                                               HttpServletRequest request) {
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        CommonAddRespDto commonAddRespDto = null;
        try {
            UserDTO userDTO = userServiceImpl.selectByUsername(request.getRemoteUser());
            typeFullDto.setCreateOperId(userDTO.getId());
            typeFullDto.setCreateOperName(userDTO.getName());
            commonAddRespDto = memberShipTagServ.addCreditEventType(typeFullDto);
            if (commonAddRespDto.getCode() < 0) {
                defaultWebRespVO.setCode(String.valueOf(commonAddRespDto.getCode()));
                defaultWebRespVO.setMessage(commonAddRespDto.getMessage());
                return defaultWebRespVO;
            }
        } catch (Exception e) {
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("操作失败");
            return defaultWebRespVO;
        }
        return DefaultWebRespVO.getSuccessVO(commonAddRespDto.getId());
    }

    /**
     * 修改事件类型
     *
     * @param typeFullDto
     * @return
     */
    @RequestMapping(value = "updateCreditEventType", method = RequestMethod.PUT)
    public DefaultWebRespVO updateCreditEventType(@RequestBody CreditEventTypeFullDto typeFullDto,
                                                  HttpServletRequest request) {
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        CommonAddRespDto commonAddRespDto = null;
        try {
            UserDTO userDTO = userServiceImpl.selectByUsername(request.getRemoteUser());
            typeFullDto.setUpdateOperId(userDTO.getId());
            typeFullDto.setUpdateOperName(userDTO.getName());
            commonAddRespDto = memberShipTagServ.updateCreditEventType(typeFullDto);
            if (commonAddRespDto.getCode() < 0) {
                defaultWebRespVO.setCode(String.valueOf(commonAddRespDto.getCode()));
                defaultWebRespVO.setMessage(commonAddRespDto.getMessage());
                return defaultWebRespVO;
            }
        } catch (Exception e) {
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("操作失败");
            return defaultWebRespVO;
        }
        return DefaultWebRespVO.getSuccessVO(commonAddRespDto.getId());
    }


    /**
     * 获取信用事件类型维护列表
     *
     * @param paramsBO
     * @return
     */
    @RequestMapping(value = "getCreditEventTypeList", method = RequestMethod.POST)
    public DefaultWebRespVO getCreditEventTypeList(@RequestBody CreditEventTypeParamsBO paramsBO) {
        CreditEventTypeParamsDto paramsDto = new CreditEventTypeParamsDto();
        BeanUtils.copyProperties(paramsBO, paramsDto);
        Page page = new Page();
        if (1 == paramsBO.getIsAll()) {
            page.setCountFlag(true);
        }
        page.setPageNo(paramsBO.getPageNum());
        page.setPageSize(paramsBO.getPageSize());
        PageBeanDto<CreditEventTypePageDto> typeListPage =
                memberShipTagServ.getCreditEventTypeListPage(paramsDto, page);
        PageBO pageBO = new PageBO();
        pageBO.setPageNum(typeListPage.getPage().getPageNo());
        pageBO.setPageSize(typeListPage.getPage().getPageSize());
        pageBO.setTotal(null == typeListPage.getPage().getCount() ? 0L : typeListPage.getPage().getCount());
        PageBeanBO<CreditEventTypePageDto> pageBeanBO = new PageBeanBO<CreditEventTypePageDto>(pageBO, typeListPage.getList());
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }


    /**
     * 新增信用事件记录
     *
     * @param fullDto
     * @param request
     * @return
     */
    @RequestMapping(value = "addCreditEvent", method = RequestMethod.POST)
    public DefaultWebRespVO addCreditEvent(@RequestBody CreditEventRecordFullDto fullDto,
                                           HttpServletRequest request) {
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        CommonAddRespDto commonAddRespDto = null;
        try {
            UserDTO userDTO = userServiceImpl.selectByUsername(request.getRemoteUser());
            fullDto.setCreateOperId(userDTO.getId());
            fullDto.setCreateOperName(userDTO.getName());
            String imagePath = ComUtil.getFileSplitPath(fullDto.getEventImagePath());
            fullDto.setEventImagePath(imagePath);
            String filePath = ComUtil.getFileSplitPath(fullDto.getEventFilePath());
            fullDto.setEventFilePath(filePath);
            commonAddRespDto = memberShipTagServ.saveCreditEventRecord(fullDto);
            if (commonAddRespDto.getCode() < 0) {
                defaultWebRespVO.setCode(String.valueOf(commonAddRespDto.getCode()));
                defaultWebRespVO.setMessage(commonAddRespDto.getMessage());
                return defaultWebRespVO;
            }
        } catch (Exception e) {
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("操作失败");
            return defaultWebRespVO;
        }
        return DefaultWebRespVO.getSuccessVO(commonAddRespDto.getId());
    }

    /**
     * 获取会员负面记录
     *
     * @param paramsBO
     * @return
     */
    @RequestMapping(value = "getAuthCreditEventList", method = RequestMethod.POST)
    public DefaultWebRespVO getAuthCreditEventList(@RequestBody AuthCreditEventBO paramsBO) {
        Page page = new Page();
        if (1 == paramsBO.getIsAll()) {
            page.setCountFlag(true);
        }
        page.setPageNo(paramsBO.getPageNum());
        page.setPageSize(paramsBO.getPageSize());
        PageBeanDto<AuthCreditEventRecordPageDto> authCreditEventRecordListPage =
                memberShipTagServ.getAuthCreditEventRecordListPage(paramsBO.getAuthId(), page);
        PageBO pageBO = new PageBO();
        pageBO.setPageNum(authCreditEventRecordListPage.getPage().getPageNo());
        pageBO.setPageSize(authCreditEventRecordListPage.getPage().getPageSize());
        pageBO.setTotal(null == authCreditEventRecordListPage.getPage().getCount() ?
                0L : authCreditEventRecordListPage.getPage().getCount());
        List<AuthCreditEventRecordPageVO> arrayList = new ArrayList<>();
        for (AuthCreditEventRecordPageDto record : authCreditEventRecordListPage.getList()) {
            AuthCreditEventRecordPageVO recordVo = new AuthCreditEventRecordPageVO();
            BeanUtils.copyProperties(record, recordVo);
            arrayList.add(recordVo);
        }
        PageBeanBO<AuthCreditEventRecordPageVO> pageBeanBO = new PageBeanBO<AuthCreditEventRecordPageVO>(
                pageBO, arrayList);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }


    /**
     * 获取信用事件详情
     *
     * @param authId
     * @param eventId
     * @return
     */
    @RequestMapping(value = "getCreditEventDetail", method = RequestMethod.GET)
    public DefaultWebRespVO getCreditEventDetail(@RequestParam("authId") String authId,
                                                 @RequestParam("eventId") Long eventId) {
        CreditEventRecordDetailDto eventRecordDetailDto = memberShipTagServ.getCreditEventRecordDetail(authId, eventId);
        String mobile = eventRecordDetailDto.getMobilePhone(); 
        if(StringUtils.isNotBlank(mobile)) {
    		mobile = mobile.substring(0, 3).concat("****")
					.concat(mobile.substring(mobile.length() - 4, mobile.length()));
    	}
      //会员姓名脱敏
		String memberName = eventRecordDetailDto.getAuthName();
		if (StringUtils.isNotBlank(memberName)) {
			String suffix = StringUtils.EMPTY;
			for (int i = 0; i < memberName.substring(1).length(); i++) {
				suffix += "*";
			}
			memberName = memberName.substring(0, 1).concat(suffix);
		}
        CreditEventRecordDetailVO detailVO = new CreditEventRecordDetailVO();
        String imagePath = ComUtil.getFileFullPath(eventRecordDetailDto.getEventImagePath());
        eventRecordDetailDto.setEventImagePath(imagePath);
        String filePath = ComUtil.getFileFullPath(eventRecordDetailDto.getEventFilePath());
        eventRecordDetailDto.setEventFilePath(filePath);
        BeanUtils.copyProperties(eventRecordDetailDto, detailVO);
        detailVO.setMobilePhone(mobile);
        detailVO.setAuthName(memberName);
        return DefaultWebRespVO.getSuccessVO(detailVO);
    }

    /**
     * 获取申诉事件列表
     *
     * @param appealRecordParamsBO
     * @return
     */
    @RequestMapping(value = "getAppealEventList", method = RequestMethod.POST)
    public DefaultWebRespVO getAppealEventList(@RequestBody CreditEventAppealRecordParamsBO appealRecordParamsBO) {
        CreditEventAppealRecordParamsDto appealRecordParamsDto = new CreditEventAppealRecordParamsDto();
        BeanUtils.copyProperties(appealRecordParamsBO, appealRecordParamsDto);
        Page page = new Page();
        if (1 == appealRecordParamsBO.getIsAll()) {
            page.setCountFlag(true);
        }
        page.setPageNo(appealRecordParamsBO.getPageNum());
        page.setPageSize(appealRecordParamsBO.getPageSize());
        PageBeanDto<CreditEventAppealRecordPageDto> appealRecordPageDtoPageBeanDto =
                memberShipTagServ.getCreditEventAppealRecordPage(appealRecordParamsDto, page);
        PageBO pageBO = new PageBO();
        pageBO.setPageNum(appealRecordPageDtoPageBeanDto.getPage().getPageNo());
        pageBO.setPageSize(appealRecordPageDtoPageBeanDto.getPage().getPageSize());
        pageBO.setTotal(null == appealRecordPageDtoPageBeanDto.getPage().getCount() ?
                0L : appealRecordPageDtoPageBeanDto.getPage().getCount());

        List<CreditEventAppealRecordPageVO> arrayList = new ArrayList<>();
        for (CreditEventAppealRecordPageDto record : appealRecordPageDtoPageBeanDto.getList()) {
        	String mobile = record.getMobilePhone(); 
        	if(StringUtils.isNotBlank(mobile)) {
        		mobile = mobile.substring(0, 3).concat("****")
    					.concat(mobile.substring(mobile.length() - 4, mobile.length()));
        	}
        	//会员姓名脱敏
			String memberName = record.getAuthName();
			if (StringUtils.isNotBlank(memberName)) {
				String suffix = StringUtils.EMPTY;
				for (int i = 0; i < memberName.substring(1).length(); i++) {
					suffix += "*";
				}
				memberName = memberName.substring(0, 1).concat(suffix);
			}
            CreditEventAppealRecordPageVO recordVo = new CreditEventAppealRecordPageVO();
            BeanUtils.copyProperties(record, recordVo);
            recordVo.setMobilePhone(mobile);
            recordVo.setAuthName(memberName);
            arrayList.add(recordVo);
        }
        PageBeanBO<CreditEventAppealRecordPageVO> pageBeanBO = new PageBeanBO<CreditEventAppealRecordPageVO>(
                pageBO, arrayList);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 获取事件记录列表
     *
     * @param paramsBO
     * @return
     */
    @RequestMapping(value = "getCreditEventList", method = RequestMethod.POST)
    public DefaultWebRespVO getCreditEventList(@RequestBody CreditEventRecordParamsBO paramsBO) {
    	if(paramsBO == null || StringUtils.isBlank(paramsBO.getMobilePhone())) {
    		//手机号为空限制查询七天的数据
        	if(paramsBO == null || StringUtils.isBlank(paramsBO.getEventStartTime()) || StringUtils.isBlank(paramsBO.getEventEndTime())) {
        		return new DefaultWebRespVO("-1", "发生时间不能为空");
        	}
        	Date startDate = ComUtil.getDateFromStr(paramsBO.getEventStartTime(), ComUtil.DATE_TYPE5);
    		Date endDate = ComUtil.getDateFromStr(paramsBO.getEventEndTime(), ComUtil.DATE_TYPE5);
    		try {
    			int day = ComUtil.daysBetween(startDate, endDate);
    			if(day>7) {
    				return new DefaultWebRespVO("-1", "发生时间不能超过7天");
    			}
    		} catch (ParseException e) {
    			log.warn("获取事件记录列表异常",e);
    		}
    	}
        CreditEventRecordParamsDto creditEventRecordParamsDto = new CreditEventRecordParamsDto();
        BeanUtils.copyProperties(paramsBO, creditEventRecordParamsDto);
        Page page = new Page();
        if (1 == paramsBO.getIsAll()) {
            page.setCountFlag(true);
        }
        page.setPageNo(paramsBO.getPageNum());
        page.setPageSize(paramsBO.getPageSize());
        PageBeanDto<CreditEventRecordPageDto> creditEventRecordPage =
                memberShipTagServ.getCreditEventRecordPage(creditEventRecordParamsDto, page);
        PageBO pageBO = new PageBO();
        pageBO.setPageNum(creditEventRecordPage.getPage().getPageNo());
        pageBO.setPageSize(creditEventRecordPage.getPage().getPageSize());
        pageBO.setTotal(null == creditEventRecordPage.getPage().getCount() ?
                0L : creditEventRecordPage.getPage().getCount());
        List<CreditEventRecordPageVO> arrayList = new ArrayList<>();
        for (CreditEventRecordPageDto record : creditEventRecordPage.getList()) {
        	String mobile = record.getMobilePhone();
        	if(StringUtils.isNotBlank(mobile)) {
        		mobile = mobile.substring(0, 3).concat("****")
    					.concat(mobile.substring(mobile.length() - 4, mobile.length()));
        	}
        	//会员姓名脱敏
			String memberName = record.getAuthName();
			if (StringUtils.isNotBlank(memberName)) {
				String suffix = StringUtils.EMPTY;
				for (int i = 0; i < memberName.substring(1).length(); i++) {
					suffix += "*";
				}
				memberName = memberName.substring(0, 1).concat(suffix);
			}
            CreditEventRecordPageVO recordVo = new CreditEventRecordPageVO();
            BeanUtils.copyProperties(record, recordVo);
            recordVo.setMobilePhone(mobile);
            recordVo.setAuthName(memberName);
            arrayList.add(recordVo);
        }
        PageBeanBO<CreditEventRecordPageVO> pageBeanBO = new PageBeanBO<CreditEventRecordPageVO>(
                pageBO, arrayList);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 处理申诉事件 同意 拒绝
     *
     * @param handleDto
     * @param request
     * @return
     */
    @RequestMapping(value = "handleAppealEventStatus", method = RequestMethod.POST)
    public DefaultWebRespVO handleAppealEventStatus(@RequestBody CreditEventAppealHandleDto handleDto,
                                                    HttpServletRequest request) {
        UserDTO userDTO = userServiceImpl.selectByUsername(request.getRemoteUser());
        handleDto.setUpdateOperId(userDTO.getId());
        handleDto.setUpdateOperName(userDTO.getName());
        handleDto.setHandleUserId(userDTO.getId() + "");
        handleDto.setHandleUser(userDTO.getName());
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        BaseResponse baseResponse = memberShipTagServ.handleAppealEventStatus(handleDto);
        defaultWebRespVO.setCode(baseResponse.getCode() + "");
        defaultWebRespVO.setMessage(baseResponse.getMessage());
        return DefaultWebRespVO.getSuccessVO(defaultWebRespVO);
    }

    /**
     * 进行事件申诉
     *
     * @param addCreditEventAppealDto
     * @param request
     * @return
     */
    @RequestMapping(value = "saveAppealEvent", method = RequestMethod.POST)
    public DefaultWebRespVO saveAppealEvent(@RequestBody AddCreditEventAppealDto addCreditEventAppealDto,
                                            HttpServletRequest request) {
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        CommonAddRespDto commonAddRespDto = null;
        try {
            UserDTO userDTO = userServiceImpl.selectByUsername(request.getRemoteUser());
            addCreditEventAppealDto.setCreateOperId(userDTO.getId() + "");
            addCreditEventAppealDto.setCreateOperName(userDTO.getName());
            commonAddRespDto = memberShipTagServ.saveAppealEvent(addCreditEventAppealDto);
            if (commonAddRespDto.getCode() < 0) {
                defaultWebRespVO.setCode(String.valueOf(commonAddRespDto.getCode()));
                defaultWebRespVO.setMessage(commonAddRespDto.getMessage());
                return defaultWebRespVO;
            }
        } catch (Exception e) {
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("操作失败");
            return defaultWebRespVO;
        }
        return DefaultWebRespVO.getSuccessVO(commonAddRespDto.getId());
    }

    /**
     * 获取事件类型分析列表
     *
     * @param paramsBO
     * @return
     */
    @RequestMapping(value = "getCreditEventTypeReportList", method = RequestMethod.POST)
    public DefaultWebRespVO getCreditEventTypeReportList(@RequestBody CreditEventTypeReportParamsBO paramsBO) {
        CreditEventTypeReportParamsDto creditEventTypeReportParamsDto = new CreditEventTypeReportParamsDto();
        BeanUtils.copyProperties(paramsBO, creditEventTypeReportParamsDto);
        Page page = new Page();
        if (1 == paramsBO.getIsAll()) {
            page.setCountFlag(true);
        }
        page.setPageNo(paramsBO.getPageNum());
        page.setPageSize(paramsBO.getPageSize());
        PageBeanDto<CreditEventTypeReportPageDto> creditEventRecordPage =
                memberShipTagServ.getCreditEventTypeReportPage(creditEventTypeReportParamsDto, page);
        PageBO pageBO = new PageBO();
        pageBO.setPageNum(creditEventRecordPage.getPage().getPageNo());
        pageBO.setPageSize(creditEventRecordPage.getPage().getPageSize());
        pageBO.setTotal(null == creditEventRecordPage.getPage().getCount() ?
                0L : creditEventRecordPage.getPage().getCount());

        Map<String, Object> map = new HashedMap();
        Set<String> set = new HashSet<>();
        List<CreditEventTypeReportPageVO> arrayList = new ArrayList<>();
        for (CreditEventTypeReportPageDto reportDto : creditEventRecordPage.getList()) {
            set.add(reportDto.getEventName());
            map.put(reportDto.getEventName() + "_" + reportDto.getMonth(), reportDto.getTotal());
        }
        for (String name : set) {
            CreditEventTypeReportPageVO vo = new CreditEventTypeReportPageVO();
            vo.setEventName(name);
            vo.setJanTotal((Integer) map.get(name + "_" + 1) == null ? 0 : (Integer) map.get(name + "_" + 1));
            vo.setFebTotal((Integer) map.get(name + "_" + 2) == null ? 0 : (Integer) map.get(name + "_" + 2));
            vo.setMarTotal((Integer) map.get(name + "_" + 3) == null ? 0 : (Integer) map.get(name + "_" + 3));
            vo.setAprTotal((Integer) map.get(name + "_" + 4) == null ? 0 : (Integer) map.get(name + "_" + 4));
            vo.setMayTotal((Integer) map.get(name + "_" + 5) == null ? 0 : (Integer) map.get(name + "_" + 5));
            vo.setJuneTotal((Integer) map.get(name + "_" + 6) == null ? 0 : (Integer) map.get(name + "_" + 6));
            vo.setJulyTotal((Integer) map.get(name + "_" + 7) == null ? 0 : (Integer) map.get(name + "_" + 7));
            vo.setAugTotal((Integer) map.get(name + "_" + 8) == null ? 0 : (Integer) map.get(name + "_" + 8));
            vo.setSeptTotal((Integer) map.get(name + "_" + 9) == null ? 0 : (Integer) map.get(name + "_" + 9));
            vo.setOctTotal((Integer) map.get(name + "_" + 10) == null ? 0 : (Integer) map.get(name + "_" + 10));
            vo.setNovTotal((Integer) map.get(name + "_" + 11) == null ? 0 : (Integer) map.get(name + "_" + 11));
            vo.setDecTotal((Integer) map.get(name + "_" + 12) == null ? 0 : (Integer) map.get(name + "_" + 12));
            arrayList.add(vo);
        }

        PageBeanBO<CreditEventTypeReportPageVO> pageBeanBO = new PageBeanBO<CreditEventTypeReportPageVO>(
                pageBO, arrayList);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }


    /**
     * 导出事件类型分析数据报表
     *
     * @param yearNum 年份
     * @param orgId   所属公司id
     * @param type    查询类型 0-各事件类型次数 1-各事件类型会员数
     * @return
     */
    @RequestMapping(value = "exportEventTypeReportData", method = RequestMethod.GET)
    public DefaultWebRespVO exportEventTypeReportData(@RequestParam(value = "yearNum", required = true) String yearNum,
                                                      @RequestParam(value = "orgId", required = true) String orgId,
                                                      @RequestParam(value = "type", required = true) Integer type,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) {
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            CreditEventTypeReportParamsDto creditEventTypeReportParamsDto = new CreditEventTypeReportParamsDto();
            creditEventTypeReportParamsDto.setYearNum(yearNum);
            creditEventTypeReportParamsDto.setOrgId(orgId);
            creditEventTypeReportParamsDto.setType(type);
            PageBeanDto<CreditEventTypeReportPageDto> creditEventRecordPage =
                    memberShipTagServ.getCreditEventTypeReportExport(creditEventTypeReportParamsDto);
            Map<String, Object> map = new HashedMap();
            Set<String> set = new HashSet<>();
            List<CreditEventTypeReportPageVO> arrayList = new ArrayList<>();
            for (CreditEventTypeReportPageDto reportDto : creditEventRecordPage.getList()) {
                set.add(reportDto.getEventName());
                map.put(reportDto.getEventName() + "_" + reportDto.getMonth(), reportDto.getTotal());
            }
            for (String name : set) {
                CreditEventTypeReportPageVO vo = new CreditEventTypeReportPageVO();
                vo.setEventName(name);
                vo.setJanTotal((Integer) map.get(name + "_" + 1) == null ? 0 : (Integer) map.get(name + "_" + 1));
                vo.setFebTotal((Integer) map.get(name + "_" + 2) == null ? 0 : (Integer) map.get(name + "_" + 2));
                vo.setMarTotal((Integer) map.get(name + "_" + 3) == null ? 0 : (Integer) map.get(name + "_" + 3));
                vo.setAprTotal((Integer) map.get(name + "_" + 4) == null ? 0 : (Integer) map.get(name + "_" + 4));
                vo.setMayTotal((Integer) map.get(name + "_" + 5) == null ? 0 : (Integer) map.get(name + "_" + 5));
                vo.setJuneTotal((Integer) map.get(name + "_" + 6) == null ? 0 : (Integer) map.get(name + "_" + 6));
                vo.setJulyTotal((Integer) map.get(name + "_" + 7) == null ? 0 : (Integer) map.get(name + "_" + 7));
                vo.setAugTotal((Integer) map.get(name + "_" + 8) == null ? 0 : (Integer) map.get(name + "_" + 8));
                vo.setSeptTotal((Integer) map.get(name + "_" + 9) == null ? 0 : (Integer) map.get(name + "_" + 9));
                vo.setOctTotal((Integer) map.get(name + "_" + 10) == null ? 0 : (Integer) map.get(name + "_" + 10));
                vo.setNovTotal((Integer) map.get(name + "_" + 11) == null ? 0 : (Integer) map.get(name + "_" + 11));
                vo.setDecTotal((Integer) map.get(name + "_" + 12) == null ? 0 : (Integer) map.get(name + "_" + 12));
                arrayList.add(vo);
            }
            // 声明一个工作薄
            Workbook workbook = new SXSSFWorkbook(100);
            // 生成一个表格
            Sheet sheet = workbook.createSheet();
            sheet.setColumnWidth(0, 4000);
            sheet.setColumnWidth(1, 3000);
            sheet.setColumnWidth(2, 3000);
            sheet.setColumnWidth(3, 3000);
            sheet.setColumnWidth(4, 3000);
            sheet.setColumnWidth(5, 3000);
            sheet.setColumnWidth(6, 3000);
            sheet.setColumnWidth(7, 3000);
            sheet.setColumnWidth(8, 3000);
            sheet.setColumnWidth(9, 3000);
            sheet.setColumnWidth(10, 3000);
            sheet.setColumnWidth(11, 3000);
            sheet.setColumnWidth(12, 3000);

            String[] headers = PropertyUtils.getProperty(EVENT_TYPE_REPORT_TITLE).split(Contants.COMMA_SIGN_SPLIT_NAME);

            int columnNum = headers.length;
            // 产生表格标题行
            Row row = sheet.createRow(0);
            for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                Cell cell = row.createCell(columnIndex);
                cell.setCellValue(headers[columnIndex]);
            }
            for (int rowIndex = 0; rowIndex < arrayList.size(); rowIndex++) {
                CreditEventTypeReportPageVO reportPageVO = arrayList.get(rowIndex);
                row = sheet.createRow(rowIndex + 1);
                // 第一列：事件类型
                Cell cell0 = row.createCell(0);
                // 第二列：1月
                Cell cell1 = row.createCell(1);
                // 第三列：2月
                Cell cell2 = row.createCell(2);
                // 第四列：3月
                Cell cell3 = row.createCell(3);
                // 第五列：4月
                Cell cell4 = row.createCell(4);
                // 第六列：5月
                Cell cell5 = row.createCell(5);
                // 第七列：6月
                Cell cell6 = row.createCell(6);
                // 第八列：7月
                Cell cell7 = row.createCell(7);
                // 第九列：8月
                Cell cell8 = row.createCell(8);
                // 第十列：9月
                Cell cell9 = row.createCell(9);
                // 第十一列：10月
                Cell cell10 = row.createCell(10);
                // 第十二列：11月
                Cell cell11 = row.createCell(11);
                // 第十三列：12月
                Cell cell12 = row.createCell(12);

                cell0.setCellValue(reportPageVO.getEventName());
                cell1.setCellValue(reportPageVO.getJanTotal());
                cell2.setCellValue(reportPageVO.getFebTotal());
                cell3.setCellValue(reportPageVO.getMarTotal());
                cell4.setCellValue(reportPageVO.getAprTotal());
                cell5.setCellValue(reportPageVO.getMayTotal());
                cell6.setCellValue(reportPageVO.getJuneTotal());
                cell7.setCellValue(reportPageVO.getJulyTotal());
                cell8.setCellValue(reportPageVO.getAugTotal());
                cell9.setCellValue(reportPageVO.getSeptTotal());
                cell10.setCellValue(reportPageVO.getOctTotal());
                cell11.setCellValue(reportPageVO.getNovTotal());
                cell12.setCellValue(reportPageVO.getDecTotal());
            }

            SimpleDateFormat dateformat = new SimpleDateFormat(ComUtil.DATE_TYPE4);
            String dateStr = dateformat.format(System.currentTimeMillis());
            String excelName = "EventTypeReport" + dateStr + Contants.XLSX;
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + excelName);
            OutputStream out = response.getOutputStream();
            try {
                workbook.write(out);
                out.flush();// 缓存清空输出
            } catch (FileNotFoundException e1) {
                defaultWebRespVO.setCode(Contants.RETURN_ERROR_CODE);
                defaultWebRespVO.setMessage("事件类型分析报表导出失败！");
                return defaultWebRespVO;
            } catch (IOException e) {
                defaultWebRespVO.setCode(Contants.RETURN_ERROR_CODE);
                defaultWebRespVO.setMessage("事件类型分析报表导出失败！");
                return defaultWebRespVO;
            } finally {
                if (workbook != null) {
                    try {
                        workbook.close();
                    } catch (IOException e) {
                        defaultWebRespVO.setCode(Contants.RETURN_ERROR_CODE);
                        defaultWebRespVO.setMessage("事件类型分析报表导出失败！");
                        return defaultWebRespVO;
                    }
                    workbook = null;
                }
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        defaultWebRespVO.setCode(Contants.RETURN_ERROR_CODE);
                        defaultWebRespVO.setMessage("事件类型分析报表导出失败！");
                        return defaultWebRespVO;
                    }
                    out = null;
                }
            }
            defaultWebRespVO.setMessage("事件类型分析报表导出成功");
            return defaultWebRespVO;
        } catch (IOException e) {
            defaultWebRespVO.setCode(Contants.RETURN_ERROR_CODE);
            defaultWebRespVO.setMessage("事件类型分析报表导出失败！");
            return defaultWebRespVO;
        }

    }
}
