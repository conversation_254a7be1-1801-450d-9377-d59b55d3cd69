package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.BrandActivityCouponModelDTO;
import com.extracme.evcard.mmp.dto.CouponBatchImportActivityDetailDTO;
import com.extracme.evcard.mmp.dto.CouponBatchImportDTO;
import com.extracme.evcard.mmp.dto.ThirdCouponModelDTO;
import com.extracme.evcard.mmp.service.ICouponBatchImportService;
import com.extracme.evcard.rpc.coupon.dto.MmpCouponStatisticsDTO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.constants.Constants;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.exception.BusinessRuntimeException;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.File;
import java.io.IOException;

/**
 * 项目名称：evcard-mmp-rest 类名称：CouponBatchImportController 类描述：优惠券批量导入活动控制层
 * 创建人：sunb-孙彬 创建时间：2018年1月17日上午10:50:10 修改备注
 *
 * @version1.0
 */
@RestController
@RequestMapping("api")
public class CouponBatchImportController {
	private final Logger log = LoggerFactory.getLogger(this.getClass());

	@Resource
	private ICouponBatchImportService couponBatchImportServiceImpl;

	/**
	 * 新增优惠券批量导入活动
	 *
	 * @param
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "couponBatchImportActivity", method = RequestMethod.POST)
	public DefaultWebRespVO addCouponBatchImportActivity(
			@RequestParam(value = "file", required = true) CommonsMultipartFile file,
			@RequestParam(value = "activityName", required = true) String activityName,
			@RequestParam(value = "orgId", required = false) String orgId,
			@RequestParam(value = "remark", required = false) String remark, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			CouponBatchImportDTO couponBatchImportDTO = new CouponBatchImportDTO();
			// FileItem fileItem = file.getFileItem();
			DiskFileItem fi = (DiskFileItem) file.getFileItem();
			File file1 = fi.getStoreLocation();
			String fileName = file.getOriginalFilename();
			couponBatchImportDTO.setFile(file1);
			couponBatchImportDTO.setActivityName(activityName);
			couponBatchImportDTO.setOrgId(orgId);
			couponBatchImportDTO.setRemark(remark);
			couponBatchImportDTO.setFileName(fileName);
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.batchImportActivityCoupon(couponBatchImportDTO,
					request);
			if (respDTO.getCode() == -1) {
				log.info("新增优惠券批量导入活动失败：" + respDTO.getMessage());
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			} else {
				log.info("新增优惠券批量导入活动成功...");
				vo.setData(respDTO.getData());
				vo.setMessage("提交成功");
				return vo;
			}
		} catch (BusinessRuntimeException e) {
			e.printStackTrace();
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getErrorMsg());
		} catch (Exception e) {
			e.printStackTrace();
			log.info("新增优惠券批量导入活动失败...", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("导入失败，请检查Excel模板格式");
			return vo;
		}
	}

	/**
	 * 优惠券批量导入活动详情
	 *
	 * @param id
	 *            活动id
	 * @return
	 */
	@RequestMapping(value = "couponBatchImportActivity/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getCouponBatchImportActivityDetail(@PathVariable("id") Long id) {
		CouponBatchImportActivityDetailDTO detailDTO = couponBatchImportServiceImpl
				.getCouponBatchImportActivityDetail(id);
		log.debug("优惠券批量导入活动详情...");
		return DefaultWebRespVO.getSuccessVO(detailDTO);
	}

	/**
	 * 下载优惠券导入模板文件
	 *
	 * @return
	 */
	@RequestMapping(value = "downloadCouponTemplate", method = RequestMethod.GET)
	public DefaultWebRespVO downloadTemplate() {
		String filePath = couponBatchImportServiceImpl.downloadTemplate();
		log.debug("下载优惠券导入模板文件...");
		return DefaultWebRespVO.getSuccessVO(filePath);
	}

	/**
	 * 下载导入失败错误报告
	 * @return
	 */
	@RequestMapping(value = "downloadErrorCouponActivityTemplateLong", method = RequestMethod.GET)
	public DefaultWebRespVO downloadErrorCouponActivityTemplateLong(
			@RequestParam(value = "activityId", required = true) Long activityId,
			HttpServletRequest request, HttpServletResponse response){
		DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
		try {
			couponBatchImportServiceImpl.exportBatchImportErrorReport(activityId, request, response);
		}catch (Exception e){
			log.debug("下载错误报告出错");
			e.printStackTrace();
			defaultWebRespVO.setCode("-1");
			defaultWebRespVO.setMessage("error");
			return defaultWebRespVO;
		}

		log.debug("下载导入失败错误报告...");
		return defaultWebRespVO;
	}


	/**
	 * 新增优惠券批量导入活动 - 短模板
	 * 
	 * @param couponBatchImportDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "addBatchImportCouponActivity", method = RequestMethod.POST)
	public DefaultWebRespVO addBatchImportCouponActivity(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl
					.addBatchImportCouponActivity(couponBatchImportDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.info("新增优惠券批量导入活动失败...", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增优惠券批量导入活动失败");
			return vo;
		}
		vo.setMessage("提交成功");
		return vo;
	}

	/**
	 * 修改优惠券批量导入活动 - 短模板
	 * 
	 * @param couponBatchImportDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updateBatchImportCouponActivity", method = RequestMethod.POST)
	public DefaultWebRespVO updateBatchImportCouponActivity(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl
					.updateBatchImportCouponActivity(couponBatchImportDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.info("修改优惠券批量导入活动失败...", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("修改优惠券批量导入活动失败");
			return vo;
		}
		vo.setMessage("提交成功");
		return vo;
	}

	/**
	 * 删除优惠券批量导入活动 - 短模板
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deleteBatchImportCouponActivity/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO deleteBatchImportCouponActivity(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.deleteBatchImportCouponActivity(id, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("删除优惠券批量导入活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券批量导入活动已删除");
	}

	/**
	 * 开始优惠券批量导入活动 - 短模板
	 * 
	 * @param id
	 * @param file
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "startBatchImportCouponActivityOld", method = RequestMethod.POST)
	public DefaultWebRespVO startBatchImportCouponActivity(
			@RequestParam(value = "file1", required = false) CommonsMultipartFile file,
			@RequestParam(value = "id", required = false) Long id, HttpServletRequest request,
			HttpServletResponse response) {
		try {
			DiskFileItem fi = (DiskFileItem) file.getFileItem();
			File file1 = fi.getStoreLocation();
			String fileName = file.getOriginalFilename();
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.startBatchImportCouponActivity(id, file1, fileName,
					request, response);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
			if (respDTO.getCode() == -10) {
				return new DefaultWebRespVO("-10", respDTO.getMessage());
			}
		} catch (Exception e) {
			log.warn("发券异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
		}
		log.debug("开始优惠券批量导入活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动结束");
	}

	/**
	 * 获取优惠券批量导入活动优惠券模板列表
	 * 
	 * @param paramsBO
	 * @return
	 */
	@RequestMapping(value = "getBatchImportCouponModelPage", method = RequestMethod.POST)
	public DefaultWebRespVO getBatchImportCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
		PageBeanBO<ThirdCouponModelDTO> couponModelPage = couponBatchImportServiceImpl
				.getBatchImportCouponModelPage(paramsBO);
		log.debug("获取优惠券批量导入活动优惠券模板列表...");
		return DefaultWebRespVO.getSuccessVO(couponModelPage);
	}

	/**
	 * 优惠券批量导入失败记录导出
	 * 
	 * @param activityId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "exportBatchImportFailedReport", method = RequestMethod.GET)
	public DefaultWebRespVO exportBatchImportFailedReport(
			@RequestParam(value = "activityId", required = true) Long activityId, HttpServletRequest request,
			HttpServletResponse response) {
		DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.exportBatchImportFailedReport(activityId, request,
				response);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("优惠券批量导入错误记录导出");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券批量导入错误记录导出成功");
	}

	/**
	 * 新增优惠券批量导入活动 - 长模板
	 * 
	 * @param couponBatchImportDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "addBatchImportCouponLong", method = RequestMethod.POST)
	public DefaultWebRespVO addBatchImportCouponLong(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.addBatchImportCouponLong(couponBatchImportDTO,
					request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.info("新增优惠券批量导入活动失败...", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增优惠券批量导入活动失败");
			return vo;
		}
		vo.setMessage("提交成功");
		return vo;
	}

	/**
	 * 修改优惠券批量导入活动 - 长模板
	 * 
	 * @param couponBatchImportDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updateBatchImportCouponLong", method = RequestMethod.POST)
	public DefaultWebRespVO updateBatchImportCouponLong(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl
					.updateBatchImportCouponLong(couponBatchImportDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.info("修改优惠券批量导入活动失败...", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("修改优惠券批量导入活动失败");
			return vo;
		}
		vo.setMessage("提交成功");
		return vo;
	}

	/**
	 * 删除优惠券批量导入活动 - 长模板
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deleteBatchImportCouponLong/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO deleteBatchImportCouponLong(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.deleteBatchImportCouponLong(id, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("删除优惠券批量导入活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券批量导入活动已删除");
	}

	@RequestMapping(value = "downloadErrorCouponActivityTemplate", method = RequestMethod.GET)
	public DefaultWebRespVO downloadErrorCouponActivityTemplate(@RequestParam(value = "id" , required = true) Long id,HttpServletRequest request, HttpServletResponse response){
		DefaultServiceRespDTO defaultServiceRespDTO = couponBatchImportServiceImpl.exportBatchImportErrorReport(id, request, response);
		if (null != defaultServiceRespDTO){
			DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
			defaultWebRespVO.setMessage(defaultServiceRespDTO.getMessage());
			defaultWebRespVO.setCode(Contants.RETURN_ERROR_CODE);
			return defaultWebRespVO;
		}
		return null;
	}

	/**
	 * 开始优惠券批量导入活动 - 长模板 --- new 1.26
	 * 
	 * @param file
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "startBatchImportCouponLongOld", method = RequestMethod.POST)
	public DefaultWebRespVO startBatchImportCouponLong(
			@RequestParam(value = "file1", required = true) CommonsMultipartFile file,
			@RequestParam(value = "id", required = true) Long id, 
			@RequestParam(value = "authority", defaultValue = "000" ) String authority,HttpServletRequest request,
			HttpServletResponse response) {
		DiskFileItem fi = (DiskFileItem) file.getFileItem();
		File file1 = fi.getStoreLocation();
		String fileName = file.getOriginalFilename();
		try {
			DefaultServiceRespDTO respDTO = couponBatchImportServiceImpl.startBatchImportCouponLong(id, file1, fileName,authority,
					request, response);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}else if (respDTO.getCode() == 1){
				return new DefaultWebRespVO("1", respDTO.getMessage());
			}
		} catch (Exception e) {
			log.warn("发券异常" , e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
		}
		log.debug("开始优惠券批量导入活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动结束");
	}
	
	/**
	 * 获取活动发券统计数据
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "getCouponStatisticsByActivityId/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getCouponStatisticsByActivityId(@PathVariable("id") Long id) {
		MmpCouponStatisticsDTO couponStatisticsDTO;
		try {
			couponStatisticsDTO = couponBatchImportServiceImpl
					.getCouponStatisticsByActivityId(id);
		} catch (Exception e) {
			log.error("获取优惠券批量导入发券详情失败", e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询发券情况失败");
		}
		log.debug("优惠券批量导入发券详情...");
		return DefaultWebRespVO.getSuccessVO(couponStatisticsDTO);
	}
}
