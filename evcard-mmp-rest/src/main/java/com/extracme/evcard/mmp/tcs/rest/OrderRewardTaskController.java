package com.extracme.evcard.mmp.tcs.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.tcs.IOrderRewardTaskConfigService;
import com.extracme.evcard.mmp.service.tcs.TaskCouponViewPageDTO;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.evcard.tcs.provider.api.bo.TaskCouponModelQueryBO;
import com.extracme.evcard.tcs.provider.api.dto.task.OrderRewardTaskConfigDTO;
import com.extracme.evcard.tcs.provider.api.dto.task.TaskDetailDTO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Discription
 * @date 2019/12/31
 */
@Api(value="orderAchievementTask", tags = "成就任务")
@RestController
@RequestMapping("api/orderAchievementTask")
public class OrderRewardTaskController {
    private static final Logger logger = LoggerFactory.getLogger(DailyOrderTaskController.class);

    @Resource
    IOrderRewardTaskConfigService orderRewardTaskConfigService;

    /**
     * 活动详情获取
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public BaseResultVo queryDetail(@PathVariable("id") Long id, HttpServletRequest request){
        TaskDetailDTO taskDetailDTO = orderRewardTaskConfigService.queryDetail(id);
        if (null == taskDetailDTO) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return BaseResultVo.getSuccessVO(taskDetailDTO);
    }

    /**
     * 券模板列表
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public BaseResultVo getCouponModelPage(@RequestBody TaskCouponModelQueryBO paramsBO,
                                               HttpServletRequest request) {
        TaskCouponViewPageDTO couponModelPage = orderRewardTaskConfigService.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        return BaseResultVo.getSuccessVO(couponModelPage);
    }

    /**
     * 活动新增
     * @param taskConfigDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public BaseResultVo add(@RequestBody OrderRewardTaskConfigDTO taskConfigDTO, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardTaskConfigService.add(taskConfigDTO, request);
            if (respDTO.getCode() != 0) {
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("新增活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 活动更新
     * @param taskConfigDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    public BaseResultVo update(@RequestBody OrderRewardTaskConfigDTO taskConfigDTO, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardTaskConfigService.update(taskConfigDTO, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("更新活动异常，id=" + taskConfigDTO.getId(), e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public BaseResultVo delete(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardTaskConfigService.delete(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动发布
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public BaseResultVo publish(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardTaskConfigService.publish(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "发布活动失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动启动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    public BaseResultVo start(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardTaskConfigService.start(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("启动活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }

    /**
     * 活动停止
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    public BaseResultVo stop(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardTaskConfigService.stop(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("停止活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }
}
