package com.extracme.evcard.mmp.tcs.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.dao.MmpUserMapper;
import com.extracme.evcard.mmp.model.MmpUser;
import com.extracme.evcard.mmp.vo.UserLogVo;
import com.extracme.evcard.tcs.provider.api.dto.UserOperateLogDTO;
import com.extracme.evcard.tcs.provider.api.dto.model.PageBeanDto;
import com.extracme.evcard.tcs.provider.api.dto.model.UserMessgae;
import com.extracme.evcard.tcs.provider.api.dto.model.resultOrUpdateModel.UserQueryPojo;
import com.extracme.evcard.tcs.provider.api.service.IUserQueryServiceProvider;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("api/userQuery")
public class TaskUserQueryController {
    private static final Logger logger = LoggerFactory.getLogger(TaskUserQueryController.class);

    @Resource
    IUserQueryServiceProvider iUserQueryServiceProvider;

    @Resource
    MmpUserMapper mmpUserMapper;

    /**
     * 查询 or 初始化
     * @param pageNum
     * @param pageSize
     * @param orgId
     * @param name
     * @param
     * @return
     */
    @RequestMapping(value = "initLoad", method = RequestMethod.GET)
    public DefaultWebRespVO initLoad(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                     @RequestParam(value = "orgId") String orgId,@RequestParam("name") String name,@RequestParam("mobilePhone") String mobilePhone){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            PageBeanDto<UserQueryPojo> userQueryPojos = iUserQueryServiceProvider.initLoad(pageNum, pageSize, orgId, mobilePhone, name);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功");
            defaultWebRespVO.setData(userQueryPojos);
        }catch (Exception e){
            logger.error("查询用户列表失败, ", e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("查询失败");
        }
        return defaultWebRespVO;
    }


    /**
     * 微信解绑
     * @param infoId
     * @param detailId
     * @return
     */
    @RequestMapping(value = "deleteWeChat/{infoId}/{detailId}", method = RequestMethod.PUT)
    public DefaultWebRespVO unbindWx(@PathVariable Long infoId, @PathVariable Long detailId, HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());

        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            iUserQueryServiceProvider.unbindWx(infoId, detailId, userMessgae);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("解绑成功");
        }catch (Exception e){
            logger.error("解绑微信号失败， userId=" + infoId, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("解绑失败");
        }
        return defaultWebRespVO;
    }

    /**
     * 展示日志
     * @param pageNum
     * @param pageSize
     * @param
     * @return
     */
    @RequestMapping(value = "showLog", method = RequestMethod.GET)
    public DefaultWebRespVO showLog(@RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                    @RequestParam(value = "infoId", required = true)Long infoId){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            PageBeanDto<UserOperateLogDTO> pageBeanDto = iUserQueryServiceProvider.showLog(infoId, pageNum, pageSize);
            System.out.println(JSON.toJSONString(pageBeanDto));
            if (pageBeanDto != null && CollectionUtils.isNotEmpty(pageBeanDto.getList())) {
                List<UserOperateLogDTO> userOperationLogs = pageBeanDto.getList();
                List<UserLogVo> userLogVos = new ArrayList<>();
                Long[] ids = new Long[userOperationLogs.size()];
                int i = 0;
                for (UserOperateLogDTO userOperationLog : userOperationLogs) {
                    ids[i++] = userOperationLog.getCreateUserId();
                    UserLogVo userLogVo = new UserLogVo();
                    userLogVo.setCreateTime(userOperationLog.getCreateTime());
                    userLogVo.setTaskType(userOperationLog.getTaskType().toString());
                    userLogVo.setTaskName(userOperationLog.getTaskName());
                    userLogVo.setOperationContent(userOperationLog.getOperateContent());
//                userLogVo.setCreateOperName(mmpUser.getName()+":"+mmpUser.getUsername());
                    userLogVo.setCreateOperName(userOperationLog.getCreateUser());
                    userLogVos.add(userLogVo);
                }

                List<MmpUser> mmpUsers = mmpUserMapper.selectByIds(ids);
                for (MmpUser mmpUser : mmpUsers) {
                    for (UserLogVo userLogVo : userLogVos) {
                        if (StringUtils.equals(userLogVo.getCreateOperName(),mmpUser.getId().toString())) {
                            userLogVo.setCreateOperName(mmpUser.getName() + ":" + mmpUser.getUsername());
                        }else{
                            userLogVo.setCreateOperName("用户");
                        }
                    }
                }
                if (mmpUsers.size() == 0) {
                    for (UserLogVo userLogVo : userLogVos) {
                        if (StringUtils.equals(userLogVo.getCreateOperName(), "-1") ){
                            userLogVo.setCreateOperName("用户");
                        }
                    }
                }
                PageBeanDto<UserLogVo> pageBeanDto1 = new PageBeanDto<>();
                pageBeanDto1.setTotal(pageBeanDto.getTotal());
                pageBeanDto1.setCount(pageBeanDto.getCount());
                pageBeanDto1.setList(userLogVos);
                defaultWebRespVO.setCode("0");
                defaultWebRespVO.setMessage("成功");
                defaultWebRespVO.setData(pageBeanDto1);
            }else{
                defaultWebRespVO.setCode("0");
                defaultWebRespVO.setMessage("成功");
                defaultWebRespVO.setData(pageBeanDto);
            }
        }catch (Exception e){
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败");
            logger.error("查询操作日志失败， userId=" + infoId, e);
        }
        return defaultWebRespVO;
    }

}
