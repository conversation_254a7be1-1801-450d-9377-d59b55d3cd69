package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.ActivityCenterFullDTO;
import com.extracme.evcard.mmp.dto.ActivityCenterFullDetailDTO;
import com.extracme.evcard.mmp.dto.ActivityCenterPageDTO;
import com.extracme.evcard.mmp.service.IActivityCenterService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/22
 * \* Time: 21:35
 * \* To change this template use File | Settings | File Templates.
 * \* Description:活动中心管理
 * \
 */
@RestController
@RequestMapping("api/activityCenter")
public class ActivityCenterController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IActivityCenterService activityCenterServiceImpl;

    /**
     * 新增活动中心配置活动信息
     *
     * @param activityCenterFullDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "addActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addActivity(
            @RequestBody ActivityCenterFullDTO activityCenterFullDTO,
            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO =
                    activityCenterServiceImpl.addActivity(activityCenterFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "提交失败");
        }
        logger.error("新增活动中心配置活动信息...");
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 修改活动中心配置活动信息
     *
     * @param activityCenterFullDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "updateActivity", method = RequestMethod.PUT)
    public DefaultWebRespVO updateActivity(
            @RequestBody ActivityCenterFullDTO activityCenterFullDTO,
            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO =
                    activityCenterServiceImpl.updateActivity(activityCenterFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "提交失败");
        }
        logger.error("修改活动中心配置活动信息...");
        vo.setMessage("提交成功");
        return vo;
    }


    /**
     * 获取活动中心配置活动信息详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "activityCenterDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO activityCenterDetail(@PathVariable("id") Long id) {
        ActivityCenterFullDetailDTO detailDTO = new ActivityCenterFullDetailDTO();
        try {
            detailDTO = activityCenterServiceImpl.activityCenterDetail(id);
            if (null == detailDTO) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "获取活动详情失败");
        }
        logger.error("获取活动中心配置活动信息详情...");
        return DefaultWebRespVO.getSuccessVO(detailDTO);
    }

    /**
     * 发布活动中心活动
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "publishActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publishActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = activityCenterServiceImpl.publishActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.error("发布活动中心活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "已发布");
    }


    /**
     * 立即开始活动中心活动
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "immediateStartActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = activityCenterServiceImpl.immediateStartActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.error("立即开始活动中心活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 停止活动中心活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "suspendActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = activityCenterServiceImpl.suspendActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.error("停止活动中心活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }

    /**
     * 删除活动中心活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "deleteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = activityCenterServiceImpl.deleteActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.error("删除活动中心活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 暂停活动中心活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "pauseActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO pauseActivity(@PathVariable("id") Long id,
                                          HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = activityCenterServiceImpl.pauseActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.error("暂停活动中心活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已暂停");
    }

    /**
     * 恢复活动中心活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "resumeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO resumeActivity(@PathVariable("id") Long id,
                                           HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = activityCenterServiceImpl.resumeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.error("恢复活动中心活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已恢复");
    }


    /**
     * 活动中心活动列表
     *
     * @param pageNum        页码
     * @param pageSize       每页显示条数
     * @param isAll          1-统计总数
     * @param name           活动名称
     * @param activitySeq    活动ID
     * @param activityStatus 活动状态
     * @param orgId          所属公司
     * @param startDate      活动开始时间
     * @param endDate        活动结束时间
     * @param serviceType    业务类型（0-分时 1-短租）
     * @param optUser        操作人
     * @param city           城市
     * @return vo  返回值
     */
    @RequestMapping(value = "queryActivityList", method = RequestMethod.GET)
    public DefaultWebRespVO queryActivityList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                              @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                              @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                              @RequestParam(value = "name", required = false) String name,
                                              @RequestParam(value = "activitySeq", required = false) String activitySeq,
                                              @RequestParam(value = "activityStatus", required = false) String activityStatus,
                                              @RequestParam(value = "orgId", required = false) String orgId,
                                              @RequestParam(value = "startDate", required = false) String startDate,
                                              @RequestParam(value = "endDate", required = false) String endDate,
                                              @RequestParam(value = "serviceType", required = false) String serviceType,
                                              @RequestParam(value = "optUser", required = false) String optUser,
                                              @RequestParam(value = "city", required = false) String city) {

        PageBeanBO<ActivityCenterPageDTO> pageBeanBO = activityCenterServiceImpl.queryActivityList(
                pageNum, pageSize, isAll, name, activitySeq, activityStatus, orgId, startDate, endDate, serviceType, optUser, city);
        logger.error("活动中心活动列表");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 根据所属公司查询城市
     *
     * @param orgId 所属公司
     * @return
     */
    @RequestMapping(value = "queryOrgCityByOrgId", method = RequestMethod.GET)
    public DefaultWebRespVO queryOrgCityByOrgId(String orgId) {
        List<Map<String, String>> list = activityCenterServiceImpl.queryOrgCityByOrgId(orgId);
        return DefaultWebRespVO.getSuccessVO(list);
    }

    /**
     * 查询所有的活动名称，模糊搜索
     *
     * @return
     */
    @RequestMapping(value = "queryAllActivityName", method = RequestMethod.GET)
    public DefaultWebRespVO queryAllActivityName() {
        List<String> list = activityCenterServiceImpl.queryAllActivityName();
        return DefaultWebRespVO.getSuccessVO(list);
    }


    /** 将活动置顶
     * @param activitySeq
     * @param request
     * @return
     */
    @RequestMapping(value = "updateActivityToTop/{activitySeq}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateActivityToTop(@PathVariable("activitySeq") Long activitySeq,
                                                HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        if (null == activitySeq) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请选择一个需要置顶的活动");
        }
        try {
            DefaultServiceRespDTO respDTO = activityCenterServiceImpl.updateActivityToTop(activitySeq, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("置顶失败");
            return vo;
        }
        vo.setMessage("活动已置顶");
        return vo;
    }

}