package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.CouponGrantRecord;
import com.extracme.evcard.mmp.dto.CouponGrantRecordDetailDTO;
import com.extracme.evcard.mmp.dto.UserCouponInvalidateDTO;
import com.extracme.evcard.mmp.dto.UserCouponOperatorLogDTO;
import com.extracme.evcard.mmp.service.CouponGrantRecordService;
import com.extracme.evcard.mmp.vo.UserCouponInvalidateVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 项目名称：evcard-mmp-rest 类名称：CouponGrantRecord 类描述：优惠券发放记录控制类 创建：qianhao
 * 创建时间：2018年1月19日上午9:44:29 修改备注
 *
 * @version 1.0
 */
@RestController
@RequestMapping("api")
public class CouponGrantRecordController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    CouponGrantRecordService couponGrantRecordServiceImpl;

    /**
     * 优惠券发放记录一览和查询
     *
     * @param name            姓名
     * @param mobilePhone     手机号
     * @param actionId        活动编号
     * @param activityName    活动名称
     * @param type            活动类型
     * @param activityOrgId   活动所属公司
     * @param status          使用状态
     * @param passStatus      过期状态
     * @param key             兑换码
     * @param createdUser     发放人
     * @param startCreateTime 发放开始时间
     * @param endCreateTime   发放结束时间
     * @param pageNum         页码
     * @param pageSize        条数
     * @return
     */
    @RequestMapping(value = "couponGrantRecordQuery", method = RequestMethod.GET)
    public DefaultWebRespVO couponGrantRecordQuery(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "mobilePhone", required = false) String mobilePhone,
            @RequestParam(value = "actionId", required = false) String actionId,
            @RequestParam(value = "activityName", required = false) String activityName,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "activityOrgId", required = false) String activityOrgId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "passStatus", required = false) String passStatus,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "createdUser", required = false) String createdUser,
            @RequestParam(value = "startCreateTime", required = false) String startCreateTime,
            @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
            @RequestParam(value = "usedStartTime", required = false) String usedStartTime,
            @RequestParam(value = "usedEndTime", required = false) String usedEndTime,
            @RequestParam(value = "queryType", defaultValue = "0") String queryType,
            @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {

        //手机号、活动编号、兑换码、发放时间、使用时间 不允许都不设置。
        if(StringUtils.isBlank(mobilePhone) && StringUtils.isBlank(actionId) && StringUtils.isBlank(key)
            && StringUtils.isBlank(startCreateTime) && StringUtils.isBlank(endCreateTime)
            && StringUtils.isBlank(usedStartTime) && StringUtils.isBlank(usedEndTime)) {
            return new DefaultWebRespVO("-1", "至少填写：手机号、活动编号、兑换码、发放时间、使用时间，其中的一项才可查询");
        }

        //邀请好友活动、订单分享活动， 且手机号不提供 --> 时间限制不超过一周
        boolean timeLimit = false;
        if(("2".equals(type) || "9".equals(type)) && StringUtils.isBlank(mobilePhone)) {
            timeLimit = true;
            if(StringUtils.isBlank(startCreateTime) && StringUtils.isBlank(endCreateTime)
                && StringUtils.isBlank(usedStartTime) && StringUtils.isBlank(usedEndTime)) {
                return new DefaultWebRespVO("-1", "请指定发放时间或使用时间，且不超过7天");
            }
        }
        //不指定手机号、活动id、兑换码则查询区间限制为1周
        else if(StringUtils.isBlank(mobilePhone) && StringUtils.isBlank(actionId) && StringUtils.isBlank(key)) {
            timeLimit = true;
        }

        if(timeLimit && StringUtils.isNotBlank(startCreateTime) && StringUtils.isNotBlank(endCreateTime)) {
            try {
                Date startDate = ComUtil.getDateFromStr(startCreateTime, ComUtil.DATE_TYPE5);
                Date endDate = ComUtil.getDateFromStr(endCreateTime, ComUtil.DATE_TYPE5);
                int day = ComUtil.daysBetween(startDate, endDate);
                if (day > 7) {
                    return new DefaultWebRespVO("-1", "发放时间不能超过7天");
                }
            } catch (Exception e) {
                logger.warn("发放时间不能超过7天", e);
            }

        }
        if(timeLimit && StringUtils.isNotBlank(usedStartTime) && StringUtils.isNotBlank(usedEndTime)) {
            try {
                Date startDate = ComUtil.getDateFromStr(usedStartTime, ComUtil.DATE_TYPE5);
                Date endDate = ComUtil.getDateFromStr(usedEndTime, ComUtil.DATE_TYPE5);
                int day = ComUtil.daysBetween(startDate, endDate);
                if (day > 7) {
                    return new DefaultWebRespVO("-1", "使用时间不能超过7天");
                }
            } catch (Exception e) {
                logger.warn("使用时间不能超过7天", e);
            }
        }
        //增加发放类型处理
        if(StringUtils.isBlank(createdUser) && queryType.equals("1")) {
            createdUser = "evcard-tcs";
        }
        PageBeanBO<CouponGrantRecord> pageBeanBO = couponGrantRecordServiceImpl.couponGrantRecordQuery(name, mobilePhone, actionId, activityName,
                type, activityOrgId, status, passStatus, key, createdUser, startCreateTime, endCreateTime, usedStartTime, usedEndTime,isAll, pageNum, pageSize);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);

    }

    /**
     * 优惠券发放记录一览导出
     *
     * @param name            姓名
     * @param mobilePhone     手机号
     * @param actionId        活动编号
     * @param activityName    活动名称
     * @param type            活动类型
     * @param activityOrgId   活动所属公司
     * @param status          使用状态
     * @param passStatus      过期状态
     * @param key             兑换码
     * @param createdUser     发放人
     * @param startCreateTime 发放开始时间
     * @param endCreateTime   发放结束时间
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "exportCouponGrantRecordQuery", method = RequestMethod.GET)
    public DefaultWebRespVO exportCouponGrantRecordQuery(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "mobilePhone", required = false) String mobilePhone,
            @RequestParam(value = "actionId", required = false) String actionId,
            @RequestParam(value = "activityName", required = false) String activityName,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "activityOrgId", required = false) String activityOrgId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "passStatus", required = false) String passStatus,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "createdUser", required = false) String createdUser,
            @RequestParam(value = "startCreateTime", required = false) String startCreateTime,
            @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
            @RequestParam(value = "usedStartTime", required = false) String usedStartTime,
            @RequestParam(value = "usedEndTime", required = false) String usedEndTime,
            @RequestParam(value = "queryType", defaultValue = "0") String queryType,
            HttpServletRequest request, HttpServletResponse response) {
        //增加发放类型处理
        if(StringUtils.isBlank(createdUser) && queryType.equals("1")) {
            createdUser = "evcard-tcs";
        }
        DefaultServiceRespDTO respDTO = couponGrantRecordServiceImpl.exportCouponGrantRecordQuery(name, mobilePhone, actionId, activityName,
                type, activityOrgId, status, passStatus, key, createdUser, startCreateTime, endCreateTime, usedStartTime, usedEndTime,request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "券发放记录导出成功");
    }


    /**
     * 修改状态
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "couponGrantRecordStatus/{ids}", method = RequestMethod.PUT)
    public DefaultWebRespVO couponGrantRecordStatus(@PathVariable String ids) {
        DefaultServiceRespDTO vo = couponGrantRecordServiceImpl.updateCouponGrantRecordStatus(ids);
        return DefaultWebRespVO.SUCCESS;

    }

    /**
     * 查看详细
     *
     * @param startDate    开始时间
     * @param expiresDate  结束时间
     * @param couponOrigin 优惠券name
     * @param couponSeq    优惠券模板id
     * @return
     */
    @RequestMapping(value = "queryCouponGrantRecordDetail", method = RequestMethod.GET)
    public DefaultWebRespVO queryCouponGrantRecordDetail(
            @RequestParam(value = "actionId", required = false) String actionId,
            @RequestParam(value = "startDate", required = true) String startDate,
            @RequestParam(value = "expiresDate", required = true) String expiresDate,
            @RequestParam(value = "couponOrigin", required = true) String couponOrigin,
            @RequestParam(value = "userCouponSeq", required = true) String couponSeq,
            @RequestParam(value = "transactionType", required = false) Integer transactionType,
            @RequestParam(value = "agencyId", required = false) String angencyId) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        DefaultServiceRespDTO respDTO = couponGrantRecordServiceImpl.queryCouponGrantRecordDetail(actionId, startDate, expiresDate,
                couponOrigin, couponSeq, transactionType, angencyId);
        if (respDTO.getCode() == -1) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(respDTO.getMessage());
        } else {
            vo.setCode(Contants.RETURN_SUCCESS_CODE);
            CouponGrantRecordDetailDTO detailDTO = JSON.parseObject(respDTO.getData(), CouponGrantRecordDetailDTO.class);
            vo.setData(detailDTO);
        }
        return vo;

    }


    /**
     * 批量作废用户优惠券
     *
     * @param couponInvalidateVO 作废用户优惠券VO
     * @param request
     * @return
     */
    @RequestMapping(value = "batchInvalidatedUserCoupon", method = RequestMethod.PUT)
    public DefaultWebRespVO batchInvalidatedUserCoupon(@RequestBody UserCouponInvalidateVO couponInvalidateVO,
                                                       HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            UserCouponInvalidateDTO invalidateDTO = new UserCouponInvalidateDTO();
            BeanCopyUtils.copyProperties(couponInvalidateVO, invalidateDTO);
            DefaultServiceRespDTO respDTO = couponGrantRecordServiceImpl.batchInvalidatedUserCoupon(invalidateDTO, request);
            if (respDTO.getCode() == -1) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(respDTO.getMessage());
                return vo;
            }
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "已作废");
        } catch (Exception e) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("批量作废优惠券失败");
            return vo;
        }
    }

    /**
     * 获取用户优惠券操作日志
     * @param userCouponSeq 用户优惠券id
     * @return
     */
    @RequestMapping(value = "getUserCouponOperatorLogs",method = RequestMethod.GET)
    public DefaultWebRespVO getUserCouponOperatorLogs(@RequestParam("userCouponSeq") Long userCouponSeq){
        List<UserCouponOperatorLogDTO> couponInvalidateDTOs =
                couponGrantRecordServiceImpl.getUserCouponOperatorLogs(userCouponSeq);
        return DefaultWebRespVO.getSuccessVO(couponInvalidateDTOs);
    }

}
