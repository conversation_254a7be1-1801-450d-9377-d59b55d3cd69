package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.ccs.provider.dto.ExternalTaskListDTO;
import com.extracme.evcard.ccs.provider.dto.ExternalTaskProcessListDTO;
import com.extracme.evcard.ccs.provider.dto.TemplateFieldDTO;
import com.extracme.evcard.mmp.bo.*;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpExternalTaskDetailDTO;
import com.extracme.evcard.mmp.dto.TaskDisposeProcessDTO;
import com.extracme.evcard.mmp.dto.TaskDisposeRecordDTO;
import com.extracme.evcard.mmp.service.IExteralTaskCenterService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 会员系统任务管理中心
 */
@RestController
public class ExternalTaskCenterController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IExteralTaskCenterService exteralTaskCenterService;

    /**
     * 创建会员系统外部任务履历（外部任务流入）
     * @param mmpTaskCreateBO taskSeq： String 必须，任务编号
     *                        taskOrigin： Integer 必须，任务来源， 0客服系统
     *                        assignTime： Date 必须， 任务流入时间
     *                        orgId:  String 必须， 任务所属机构
     * @param request
     * @return
     */
    @RequestMapping(value = "inner/externalTask/create", method = RequestMethod.POST)
    public DefaultWebRespVO CreateMmpTask(@RequestBody MmpTaskCreateBO mmpTaskCreateBO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = exteralTaskCenterService.createTask(mmpTaskCreateBO, request);
            if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception ex) {
            logger.error("create externalTask 失败。", ex);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("创建会员系统任务失败。");
            return vo;
        }
        vo.setMessage("创建会员系统任务成功");
        return vo;
    }

    /**
     * 任务处理
     * @param mmpTaskDisposeBO
     * @param request
     * @return
     */
    @RequestMapping(value = "api/externalTask/dispose", method = RequestMethod.POST)
    public DefaultWebRespVO disposeMmpTask(@RequestBody MmpTaskDisposeBO mmpTaskDisposeBO, HttpServletRequest request) {
        if (CollectionUtils.isEmpty(mmpTaskDisposeBO.getTaskIdList())) {
            return new DefaultWebRespVO("-1", "任务id不能为空");
        }
        if (CollectionUtils.isEmpty(mmpTaskDisposeBO.getTaskSeqList())) {
            return new DefaultWebRespVO("-1", "任务编号不能为空");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = exteralTaskCenterService.disposeMmpTask(mmpTaskDisposeBO, request);
        return new DefaultWebRespVO(defaultServiceRespDTO.getCode().toString(), defaultServiceRespDTO.getMessage());
    }

    /**
     * 查询任务详情
     * @param taskId  任务ID
     * @param request
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryTaskDetail/{taskId}", method = RequestMethod.GET)
    public DefaultWebRespVO queryTaskDetail(@PathVariable Long taskId, HttpServletRequest request) {
        MmpExternalTaskDetailDTO externalTaskDetailDTO = exteralTaskCenterService.queryTaskDetail(taskId);
        if (externalTaskDetailDTO == null) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "任务不存在");
        }
        return DefaultWebRespVO.getSuccessVO(externalTaskDetailDTO);
    }

    /**
     * 查询任务列表
     * @param searchMmpTaskBO
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryTaskList", method = RequestMethod.POST)
    public DefaultWebRespVO queryTaskList(@RequestBody SearchMmpTaskBO searchMmpTaskBO) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO<ExternalTaskListDTO> pageBeanBO = exteralTaskCenterService.queryTaskList(searchMmpTaskBO);
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception ex) {
            logger.error("query externalTaskList 失败。", ex);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("查询任务列表失败。");
            return vo;
        }
    }

    /**
     * 查询完成模板
     * @param taskId
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryTemplate/{taskId}", method = RequestMethod.GET)
    public DefaultWebRespVO queryTemplate(@PathVariable Long taskId) {
        List<TemplateFieldDTO> templateFieldDTOList = exteralTaskCenterService.queryTemplate(taskId);
        return DefaultWebRespVO.getSuccessVO(templateFieldDTOList);
    }

    /**
     * 上传附件或图片
     * @param file
     * @return
     */
    @RequestMapping(value = "api/externalTask/uploadFile", method = RequestMethod.POST)
    public DefaultWebRespVO uploadFile(@RequestParam("file") CommonsMultipartFile file) {
        return exteralTaskCenterService.uploadFile(file);
    }

    /**
     * 移交其他公司
     * @param mmpTaskTransferBO
     * @param request
     * @return
     */
    @RequestMapping(value = "api/externalTask/transferTask", method = RequestMethod.POST)
    public DefaultWebRespVO transferTask(@RequestBody MmpTaskTransferBO mmpTaskTransferBO, HttpServletRequest request) {
        if (mmpTaskTransferBO.getTaskId() == null) {
            return new DefaultWebRespVO("-1", "任务id不能为空");
        }
        if (StringUtils.isBlank(mmpTaskTransferBO.getOrgId())) {
            return new DefaultWebRespVO("-1", "运营公司id不能为空");
        }
        if (StringUtils.isBlank(mmpTaskTransferBO.getOrgName())) {
            return new DefaultWebRespVO("-1", "运营公司名称不能为空");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = exteralTaskCenterService.updateTransferTask(mmpTaskTransferBO, request);
        return new DefaultWebRespVO(defaultServiceRespDTO.getCode().toString(), defaultServiceRespDTO.getMessage());
    }

    /**
     * 发送沟通消息
     * @param mmpTaskTransferBO
     * @param request
     * @return
     */
    @RequestMapping(value = "api/externalTask/sendMessage", method = RequestMethod.POST)
    public DefaultWebRespVO sendMessage(@RequestBody MmpTaskTransferBO mmpTaskTransferBO, HttpServletRequest request) {
        if (mmpTaskTransferBO.getTaskId() == null) {
            return new DefaultWebRespVO("-1", "任务id不能为空");
        }
        if (StringUtils.isBlank(mmpTaskTransferBO.getMessage())) {
            return new DefaultWebRespVO("-1", "沟通消息不能为空");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = exteralTaskCenterService.sendMessage(mmpTaskTransferBO, request);
        return new DefaultWebRespVO(defaultServiceRespDTO.getCode().toString(), defaultServiceRespDTO.getMessage());
    }

    /**
     * 查询沟通记录
     * @param taskId
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryMessageList/{taskId}", method = RequestMethod.GET)
    public DefaultWebRespVO queryMessageList(@PathVariable Long taskId) {
        List<ExternalTaskProcessListDTO> externalTaskProcessListDTOList = exteralTaskCenterService.queryMessageList(taskId);
        return DefaultWebRespVO.getSuccessVO(externalTaskProcessListDTOList);
    }

    /**
     * 查询任务处理记录
     * @param taskSeq 任务编号
     * @param request
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryTaskDisposeRecord", method = RequestMethod.GET)
    public DefaultWebRespVO queryTaskDisposeRecord(@RequestParam String taskSeq,
                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO<TaskDisposeRecordDTO> pageBeanBO = exteralTaskCenterService.queryTaskDisposeRecord(taskSeq, pageNum, pageSize, isAll);
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception ex) {
            logger.error("query externalDisposeRecord 失败。", ex);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("查询任务处理记录失败。");
            return vo;
        }
    }

    /**
     * 查询任务统计列表
     * @param queryTaskStatisticsBO
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryTaskStatisticsList", method = RequestMethod.POST)
    public DefaultWebRespVO queryTaskStatisticsList(@RequestBody QueryTaskStatisticsBO queryTaskStatisticsBO) {
        return DefaultWebRespVO.getSuccessVO(exteralTaskCenterService.queryTaskStatisticsList(queryTaskStatisticsBO));
    }

    /**
     * 查询任务处理流程
     * @param taskSeq
     * @param request
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryTaskDisposeProcess", method = RequestMethod.GET)
    public DefaultWebRespVO queryTaskDisposeProcess(@RequestParam String taskSeq,
                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO<TaskDisposeProcessDTO> pageBeanBO = exteralTaskCenterService.queryTaskDisposeProcess(taskSeq, pageNum, pageSize, isAll);
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception ex) {
            logger.error("query externalDisposeRecord 失败。", ex);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("查询任务处理流程失败。");
            return vo;
        }
    }

    /**
     * 查询待处理任务数量
     * @param orgId
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryCountByOrgId/{orgId}", method = RequestMethod.GET)
    public DefaultWebRespVO queryCountByOrgId(@PathVariable String orgId) {
        return DefaultWebRespVO.getSuccessVO(exteralTaskCenterService.queryCountByOrgId(orgId));
    }

    /**
     * 查询任务分类
     * @param pid 父id 一级为0
     * @return
     */
    @RequestMapping(value = "api/externalTask/queryProblemType/{pid}", method = RequestMethod.GET)
    public DefaultWebRespVO queryProblemType(@PathVariable Long pid) {
        return DefaultWebRespVO.getSuccessVO(exteralTaskCenterService.queryProblemType(pid));
    }

    /**
     * 导出任务列表
     * @param searchMmpTaskBO
     * @param response
     * @return
     */
    @RequestMapping(value = "api/externalTask/exportExternalTaskList", method = RequestMethod.GET)
    public DefaultWebRespVO exportExternalTaskList(SearchMmpTaskBO searchMmpTaskBO, HttpServletResponse response) {
        try {
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=exportExternalTaskListTitle.xlsx");
            if (searchMmpTaskBO == null || (StringUtils.isBlank(searchMmpTaskBO.getTaskOrgId()) && StringUtils.isBlank(searchMmpTaskBO.getUserOrgId()))) {
                return new DefaultWebRespVO("-1", "任务所属公司不能为空");
            }
            exteralTaskCenterService.exportExternalTaskList(searchMmpTaskBO, response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 导出任务统计列表
     * @param queryTaskStatisticsBO
     * @param response
     * @return
     */
    @RequestMapping(value = "api/externalTask/exportTaskStatisticsList", method = RequestMethod.GET)
    public DefaultWebRespVO exportTaskStatisticsList(QueryTaskStatisticsBO queryTaskStatisticsBO, HttpServletResponse response) {
        try {
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=exportTaskStatisticsList.xlsx");
            exteralTaskCenterService.exportTaskStatisticsList(queryTaskStatisticsBO, response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return DefaultWebRespVO.SUCCESS;
    }
}
