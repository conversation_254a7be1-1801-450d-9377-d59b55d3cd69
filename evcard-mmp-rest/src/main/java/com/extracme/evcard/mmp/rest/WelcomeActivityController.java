package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.bo.WelcomeActivityBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpWelcomeDataPageDTO;
import com.extracme.evcard.mmp.dto.WelcomeActivityDetailDTO;
import com.extracme.evcard.mmp.dto.WelcomeActivityFullDTO;
import com.extracme.evcard.mmp.dto.WelcomeActivityPageDTO;
import com.extracme.evcard.mmp.service.IWelcomeActivityService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * WelcomeActivityController class
 *
 * <AUTHOR>
 * @date 2018/3/20 15:18 Description 欢迎页活动控制器
 */
@RestController
public class WelcomeActivityController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IWelcomeActivityService welcomeActivityServiceImpl;

    private DefaultWebRespVO checkUploadImageFile(CommonsMultipartFile androidImageFile, CommonsMultipartFile androidImageExtraFile,
                                                  CommonsMultipartFile iosImageFile, CommonsMultipartFile iphonexImageFile,
                                                  Integer optType, DefaultWebRespVO vo) {
        //仅新增的时候判断
        if (0 == optType) {
            if (androidImageFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("安卓手机图片不能为空");
                return vo;
            }
            /*
            新能源品牌专项需求，去除安卓手机图片(非主流屏幕)
            if (androidImageExtraFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("安卓手机图片(非主流屏幕)不能为空");
                return vo;
            }
            */
            if (iosImageFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("ios手机图片不能为空");
                return vo;
            }
            /*
            新能源品牌专项需求，去除iphonex手机图片
            if (iphonexImageFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("iphonex手机图片不能为空");
                return vo;
            }
            */
        }
        // 允许上传的文件类型
        String suffixList = "jpg,png,jpeg,gif";
        if (null != androidImageFile) {
            String androidImageFileName = androidImageFile.getOriginalFilename();
            String androidSuffix = androidImageFileName.substring(androidImageFileName.lastIndexOf(".") + 1);
            if (!suffixList.contains(androidSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:安卓手机图片限制格式是jpg,jpeg,png,gif型");
                return vo;
            }
        }
        if (null != androidImageExtraFile) {
            String androidImageExtraFileName = androidImageExtraFile.getOriginalFilename();
            String androidSuffix = androidImageExtraFileName.substring(androidImageExtraFileName.lastIndexOf(".") + 1);
            if (!suffixList.contains(androidSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:安卓手机图片(非主流屏幕)限制格式是jpg,jpeg,png,gif型");
                return vo;
            }
        }
        if (null != iosImageFile) {
            String iosImageFileName = iosImageFile.getOriginalFilename();
            String iosSuffix = iosImageFileName.substring(iosImageFileName.lastIndexOf(".") + 1);
            if (!suffixList.contains(iosSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:苹果手机图片限制格式是jpg,jpeg,png,gif型");
                return vo;
            }
        }
        if (null != iphonexImageFile) {
            String iphonexImageFileName = iphonexImageFile.getOriginalFilename();
            String iphonexSuffix = iphonexImageFileName.substring(iphonexImageFileName.lastIndexOf(".") + 1);
            if (!suffixList.contains(iphonexSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:IphoneX手机图片限制格式是jpg,jpeg,png,gif型");
                return vo;
            }
        }
        return vo;
    }

    private DefaultWebRespVO checkUploadVideoFile(CommonsMultipartFile androidVideoFile,
                                                  CommonsMultipartFile iosVideoFile,
                                                  CommonsMultipartFile iphonexVideoFile,
                                                  Integer optType, DefaultWebRespVO vo) {
        //仅新增的时候判断
        if (0 == optType) {
            if (androidVideoFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("安卓视频不能为空");
                return vo;
            }
            if (iosVideoFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("苹果视频不能为空");
                return vo;
            }
            if (iphonexVideoFile == null) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("iphonex视频不能为空");
                return vo;
            }
        }
        long size = 2 * 1024 * 1024;
        // 允许上传的文件类型
        String suffixList = "mp4";
        if (null != androidVideoFile) {
            String androidVideoFileName = androidVideoFile.getOriginalFilename();
            String androidSuffix = androidVideoFileName.substring(androidVideoFileName.lastIndexOf(".") + 1,
                    androidVideoFileName.length());
            if (!suffixList.contains(androidSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:安卓视频限制格式是mp4型");
                return vo;
            }
            if (size < androidVideoFile.getSize()) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:安卓视频不能超过5M");
                return vo;
            }
        }
        if (null != iosVideoFile) {
            String iosVideoFileName = iosVideoFile.getOriginalFilename();
            String iosSuffix = iosVideoFileName.substring(iosVideoFileName.lastIndexOf(".") + 1,
                    iosVideoFileName.length());
            if (!suffixList.contains(iosSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:苹果视频限制格式是mp4型");
                return vo;
            }
            if (size < iosVideoFile.getSize()) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:苹果视频不能超过5M");
                return vo;
            }
        }
        if (null != iphonexVideoFile) {
            String iphonexVideoFileName = iphonexVideoFile.getOriginalFilename();
            String iphonexSuffix = iphonexVideoFileName.substring(iphonexVideoFileName.lastIndexOf(".") + 1,
                    iphonexVideoFileName.length());
            if (!suffixList.contains(iphonexSuffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:iphonex视频限制格式是mp4型");
                return vo;
            }
            if (size < iphonexVideoFile.getSize()) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:iphonex视频不能超过5M");
                return vo;
            }
        }

        return vo;
    }

    /**
     * 新增欢迎页活动配置
     *
     * @param welcomeActivityFullDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "api/welcome/addWelcomeActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addWelcomeActivity(
            @RequestParam(value = "androidImageFile", required = false) CommonsMultipartFile androidImageFile,
            @RequestParam(value = "androidImageExtraFile", required = false) CommonsMultipartFile androidImageExtraFile,
            @RequestParam(value = "iosImageFile", required = false) CommonsMultipartFile iosImageFile,
            @RequestParam(value = "iphonexImageFile", required = false) CommonsMultipartFile iphonexImageFile,
            @RequestParam(value = "androidVideoFile", required = false) CommonsMultipartFile androidVideoFile,
            @RequestParam(value = "iosVideoFile", required = false) CommonsMultipartFile iosVideoFile,
            @RequestParam(value = "iphonexVideoFile", required = false) CommonsMultipartFile iphonexVideoFile,
            @RequestParam(value = "linkImgFile", required = false) CommonsMultipartFile linkImgFile,
            WelcomeActivityFullDTO welcomeActivityFullDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            Integer type = welcomeActivityFullDTO.getType();
            if (0 != type && 1 != type) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("必须上传图片或者视频");
                return vo;
            }
            if (0 == type) {
                checkUploadImageFile(androidImageFile, androidImageExtraFile, iosImageFile, iphonexImageFile, 0, vo);
                if (Contants.RETURN_ERROR_CODE.equals(vo.getCode())) {
                    return vo;
                }
                /*
                DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.addWelcomeActivity(androidImageFile.getInputStream(),
                        androidImageExtraFile.getInputStream(), iosImageFile.getInputStream(), iphonexImageFile.getInputStream(),
                        androidImageFile.getOriginalFilename(), androidImageExtraFile.getOriginalFilename(),
                        iosImageFile.getOriginalFilename(),
                        iphonexImageFile.getOriginalFilename(), welcomeActivityFullDTO, linkImgFile, request);
                 */
                DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.addWelcomeActivity(androidImageFile.getInputStream(),
                        null, iosImageFile.getInputStream(), null,
                        androidImageFile.getOriginalFilename(), null,
                        iosImageFile.getOriginalFilename(),
                        null, welcomeActivityFullDTO, linkImgFile, request);
                if (respDTO.getCode() == -1) {
                    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
                }
            } else if (1 == type) {
                checkUploadVideoFile(androidVideoFile, iosVideoFile, iphonexVideoFile, 0, vo);
                if (Contants.RETURN_ERROR_CODE.equals(vo.getCode())) {
                    return vo;
                }
                DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.addWelcomeActivity(androidVideoFile.getInputStream(),
                        null, iosVideoFile.getInputStream(), iphonexVideoFile.getInputStream(),
                        androidVideoFile.getOriginalFilename(), null,
                        iosVideoFile.getOriginalFilename(), iphonexVideoFile.getOriginalFilename(), welcomeActivityFullDTO, linkImgFile, request);
                if (respDTO.getCode() == -1) {
                    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
        vo.setMessage("提交成功");
        log.debug("新增欢迎页活动配置信息...");
        return vo;
    }

    /**
     * 修改欢迎页活动配置
     *
     * @param welcomeActivityFullDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "api/welcome/updateWelcomeActivity", method = RequestMethod.POST)
    public DefaultWebRespVO updateWelcomeActivity(
            @RequestParam(value = "androidImageFile", required = false) CommonsMultipartFile androidImageFile,
            @RequestParam(value = "androidImageExtraFile", required = false) CommonsMultipartFile androidImageExtraFile,
            @RequestParam(value = "iosImageFile", required = false) CommonsMultipartFile iosImageFile,
            @RequestParam(value = "iphonexImageFile", required = false) CommonsMultipartFile iphonexImageFile,
            @RequestParam(value = "androidVideoFile", required = false) CommonsMultipartFile androidVideoFile,
            @RequestParam(value = "iosVideoFile", required = false) CommonsMultipartFile iosVideoFile,
            @RequestParam(value = "iphonexVideoFile", required = false) CommonsMultipartFile iphonexVideoFile,
            @RequestParam(value = "linkImgFile", required = false) CommonsMultipartFile linkImgFile,
            WelcomeActivityFullDTO welcomeActivityFullDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            Integer type = welcomeActivityFullDTO.getType();
            if (0 != type && 1 != type) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("必须上传图片或者视频");
                return vo;
            }
            // 修改的时候，如果不修改则h5无法将全路径文件转换为File，所以如果没有修改则传入null
            InputStream androidImageInputStream = null;
            String androidImage = null;
            InputStream androidImageExtraInputStream = null;
            String androidImageExtra = null;
            InputStream iosImageInputStream = null;
            String iosImage = null;
            InputStream iphonexImageInputStream = null;
            String iphonexImage = null;
            InputStream androidVideoInputStream = null;
            String androidVideo = null;
            InputStream iosVideoInputStream = null;
            String iosVideo = null;
            InputStream iphonexVideoInputStream = null;
            String iphonexVideo = null;
            if (null != androidImageFile) {
                androidImageInputStream = androidImageFile.getInputStream();
                androidImage = androidImageFile.getOriginalFilename();
            }
            if (null != androidImageExtraFile) {
                androidImageExtraInputStream = androidImageExtraFile.getInputStream();
                androidImageExtra = androidImageExtraFile.getOriginalFilename();
            }
            if (null != iosImageFile) {
                iosImageInputStream = iosImageFile.getInputStream();
                iosImage = iosImageFile.getOriginalFilename();
            }
            if (null != iphonexImageFile) {
                iphonexImageInputStream = iphonexImageFile.getInputStream();
                iphonexImage = iphonexImageFile.getOriginalFilename();
            }
            if (null != androidVideoFile) {
                androidVideoInputStream = androidVideoFile.getInputStream();
                androidVideo = androidVideoFile.getOriginalFilename();
            }
            if (null != iosVideoFile) {
                iosVideoInputStream = iosVideoFile.getInputStream();
                iosVideo = iosVideoFile.getOriginalFilename();
            }
            if (null != iphonexVideoFile) {
                iphonexVideoInputStream = iphonexVideoFile.getInputStream();
                iphonexVideo = iphonexVideoFile.getOriginalFilename();
            }
            if (0 == type) {
                checkUploadImageFile(androidImageFile, androidImageExtraFile, iosImageFile, iphonexImageFile, 1, vo);
                if (Contants.RETURN_ERROR_CODE.equals(vo.getCode())) {
                    return vo;
                }
                DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.updateWelcomeActivity(androidImageInputStream,
                        androidImageExtraInputStream, iosImageInputStream, iphonexImageInputStream,
                        androidImage, androidImageExtra, iosImage, iphonexImage, welcomeActivityFullDTO, linkImgFile, request);
                if (respDTO.getCode() == -1) {
                    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
                }
            } else if (1 == type) {
                checkUploadVideoFile(androidVideoFile, iosVideoFile, iphonexVideoFile, 1, vo);
                if (Contants.RETURN_ERROR_CODE.equals(vo.getCode())) {
                    return vo;
                }
                DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.updateWelcomeActivity(androidVideoInputStream,
                        null, iosVideoInputStream, iphonexVideoInputStream,
                        androidVideo, null, iosVideo, iphonexVideo, welcomeActivityFullDTO, linkImgFile, request);
                if (respDTO.getCode() == -1) {
                    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
        vo.setMessage("提交成功");
        log.debug("修改欢迎页活动配置信息...");
        return vo;
    }

    /**
     * 立即开始欢迎页活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @RequestMapping(value = "api/welcome/immediateStartWelcomeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.immediateStartWelcomeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始欢迎页活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 发布开始欢迎页活动
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "api/welcome/publishWelcomeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publishWelcomeActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.publishWelcomeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("发布开始欢迎页活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }

    /**
     * 停止欢迎页活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/welcome/suspendWelcomeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendActivity(@PathVariable("id") Long id, HttpServletRequest request) {

        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.suspendWelcomeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.error("停止欢迎页活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }

    /**
     * 删除欢迎页活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/welcome/deleteWelcomeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.deleteWelcomeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.error("删除欢迎页活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 暂停欢迎页活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/welcome/pauseWelcomeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO pauseActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.pauseWelcomeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.error("暂停欢迎页活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已暂停");
    }

    /**
     * 恢复欢迎页活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/welcome/resumeWelcomeActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO resumeActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.resumeWelcomeActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.error("恢复欢迎页活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已恢复");
    }

    /**
     * 欢迎页活动详情信息
     *
     * @param id 活动id
     * @return
     */
    @RequestMapping(value = "api/welcome/welcomeActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO welcomeActivityDetail(@PathVariable("id") Long id) {
        WelcomeActivityDetailDTO welcomeActivityDetailDTO = welcomeActivityServiceImpl.welcomeActivityDetail(id);
        if (null == welcomeActivityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取欢迎页活动详情信息...");
        return DefaultWebRespVO.getSuccessVO(welcomeActivityDetailDTO);
    }

    /**
     * 欢迎页活动列表
     *
     * @return vo 返回值
     */
    @RequestMapping(value = "api/welcome/queryWelcomeActivityListPage", method = RequestMethod.POST)
    public DefaultWebRespVO queryWelcomeActivityListPage(@RequestBody WelcomeActivityBO welcomeActivityBO) {
        PageBeanBO<WelcomeActivityPageDTO> pageBeanBO = welcomeActivityServiceImpl.queryWelcomeActivityListPage(welcomeActivityBO);
        log.debug("欢迎页活动列表...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 查询所有的活动名称，模糊搜索
     *
     * @return
     */
    @RequestMapping(value = "api/welcome/queryAllWelcomeActivityName", method = RequestMethod.GET)
    public DefaultWebRespVO queryAllWelcomeActivityName() {
        List<String> list = welcomeActivityServiceImpl.queryAllWelcomeActivityName();
        return DefaultWebRespVO.getSuccessVO(list);
    }

    /**
     * 欢迎页统计列表
     *
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @param id
     * @return
     */
    @RequestMapping(value = "api/welcome/getWelcomePageStatistics", method = RequestMethod.GET)
    public DefaultWebRespVO queryWelcomePageStatistics(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                       @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                       @RequestParam(value = "id", required = false) Long id) {
        PageBeanBO<MmpWelcomeDataPageDTO> pageBeanBO = welcomeActivityServiceImpl.queryWelcomePageStatistics(id, pageNum, pageSize, isAll);
        log.debug("欢迎页统计列表...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }


    /**
     * 欢迎页统计数据导出
     *
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "api/welcome/exportWelcomePageStatistics", method = RequestMethod.GET)
    public DefaultWebRespVO exportWelcomePageStatistics(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                        @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                        @RequestParam(value = "id", required = false) Long id,
                                                        HttpServletRequest request, HttpServletResponse response) {
        String userName = request.getRemoteUser();
        try {
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition",
                    "attachment;filename=exportWelcomePageStatisticsTitle.xlsx");
            welcomeActivityServiceImpl.exportWelcomePageStatistics(id, pageNum, pageSize, isAll,
                    response.getOutputStream(), userName);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return DefaultWebRespVO.SUCCESS;
    }
    
    /**
     * 查询欢迎页活动信息
     * @param activityId
     * @return
     */
    @RequestMapping(value = "inner/welcome/getWelcomeActivity", method = RequestMethod.POST)
    public DefaultWebRespVO getWelcomeActivity(String activityId) {
    	log.info("----------evcard-rest调用已进入getWelcomeActivity------参数activityId: " +activityId);
        DefaultServiceRespDTO respDTO = welcomeActivityServiceImpl.getWelcomeActivity(activityId);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage(), respDTO.getData());
        }
        log.info("查询欢迎页活动...，返回：" + JSON.toJSONString(respDTO));
        Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
        return DefaultWebRespVO.getSuccessVO(map);
    }
}