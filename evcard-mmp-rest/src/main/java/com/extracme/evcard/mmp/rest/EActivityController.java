package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.EActivityDetailDTO;
import com.extracme.evcard.mmp.dto.EActivityFullDTO;
import com.extracme.evcard.mmp.dto.RechargePackagesTemplateSelectDTO;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.service.IEActivityService;
import com.extracme.evcard.mmp.service.IMarketActivityService;
import com.extracme.evcard.mmp.service.IRechargePackagesTemplateService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2017/12/13
 * \* Time: 15:35
 * \* To change this template use File | Settings | File Templates.
 * \* Description:充值e币活动发券配置
 * \
 */
@Api(value="eCoupon", tags = "充值e币活动发券活动[3]")
@RestController
@RequestMapping("api/eCoupon")
public class EActivityController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IEActivityService eActivityServiceImpl;

    @Resource
    private IRechargePackagesTemplateService packagesTemplateServiceImpl;

    @Resource
    IMarketActivityService marketActivityServiceImpl;

    /**
     * 新增充值e币活动发券配置
     *
     * @param eActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="新增充值e币活动发券配置", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "addEOfferCouponActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addEOfferCouponActivity(@RequestBody EActivityFullDTO eActivityFullDTO,
                                                    HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = eActivityServiceImpl.addEOfferCouponActivity(eActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增充值e币活动发券配置...");
        return vo;
    }

    /**
     * 修改充值e币活动发券配置
     *
     * @param eActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="修改充值e币活动发券配置", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "updateEOfferCouponActivity", method = RequestMethod.PUT)
    public DefaultWebRespVO updateEOfferCouponActivity(@RequestBody EActivityFullDTO eActivityFullDTO,
                                                       HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = eActivityServiceImpl.updateEOfferCouponActivity(eActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改充值e币活动发券配置...");
        return vo;
    }


    /**
     * 获取充值e币活动发券信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value="获取充值e币活动发券信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "eOfferCouponActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO eOfferCouponActivityDetail(@PathVariable("id") Long id) {
        EActivityDetailDTO eActivityDetailDTO = eActivityServiceImpl.eOfferCouponActivityDetail(id);
        if (null == eActivityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取值e币活动发券信息...");
        return DefaultWebRespVO.getSuccessVO(eActivityDetailDTO);
    }

    /**
     * 删除活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "deleteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.deleteActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 停止E币充值活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "suspendEActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendEActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        String operatorContent = "停止";
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.suspendEActivity(id, createOperName, createOperId, operatorContent);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止充值E币活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }

    /**
     * 检测是否存在时间段冲突的充值E币活动
     *
     * @param id 活动id
     * @return
     */
    @ApiOperation(value="检测是否存在时间段冲突的充值E币活动", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "checkTimeConflictEActivity/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO checkTimeConflictEActivity(@PathVariable("id") Long id) {
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.checkTimeConflictEActivity(id);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("根据id检测是否存在时间段冲突的充值E币活动");
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 活动发布（审核通过）
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }

    /**
     * 立即开始充值E币活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="立即开始充值E币活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "immediateStartEActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartEActivity(@PathVariable("id") Long id,
                                                    HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.immediateStartEActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始充值E币活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 暂停充值E币活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="暂停充值E币活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "pauseEActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO pauseEActivity(@PathVariable("id") Long id,
                                           HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.pauseEActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("暂停充值E币活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已暂停");

    }

    /**
     * 恢复充值E币活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="恢复充值E币活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "resumeEActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO resumeEActivity(@PathVariable("id") Long id,
                                            HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = eActivityServiceImpl.resumeEActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("恢复充值E币活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已恢复");

    }

    /**
     * 获取充值E币优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @ApiOperation(value="获取充值E币优惠券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getECouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getECouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        ActivityCouponModelPageDTO eActivityCouponModelDTO = marketActivityServiceImpl.getCouponModelPage(paramsBO);
//        EActivityCouponModelDTO eActivityCouponModelDTO = eActivityServiceImpl.getECouponModelPage(paramsBO);
//        log.debug("获取E优惠券模板信息...");
        return DefaultWebRespVO.getSuccessVO(eActivityCouponModelDTO);
    }

    /**
     * 获取充值E币活动启用的套餐模板
     *
     * @param orgId 活动所属公司
     * @return
     */
    @ApiOperation(value="获取充值E币活动启用的套餐模板", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "getEActivityPackages", method = RequestMethod.GET)
    public DefaultWebRespVO getEActivityPackages(String orgId) {
        List<RechargePackagesTemplateSelectDTO> templateSelectDTOList =
                packagesTemplateServiceImpl.getOrganizationsAllPackagesByOrgId(orgId);
        log.debug("获取E币活动套餐模板...");
        return DefaultWebRespVO.getSuccessVO(templateSelectDTOList);
    }
}