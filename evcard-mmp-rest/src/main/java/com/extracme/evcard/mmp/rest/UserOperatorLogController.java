package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.UserOperatorLogDTO;
import com.extracme.evcard.mmp.service.IUserOperatorService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * UserOperatorLogController class
 *
 * <AUTHOR>
 * @date 2018/2/28 10:19
 * Description 用户操作日志记录
 */
@RestController
@RequestMapping("api/log")
public class UserOperatorLogController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IUserOperatorService userOperatorServiceImpl;

    /**
     * 日志一览
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @param id       id
     * @return vo 返回值
     */
    @RequestMapping(value = "userOperatorLog", method = RequestMethod.GET)
    public DefaultWebRespVO queryUserOperatorLogList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                     @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                     @RequestParam(value = "id", required = true) String id) {
        PageBeanBO<UserOperatorLogDTO> pageBeanBO = userOperatorServiceImpl.queryUserOperatorLogList(pageNum, pageSize, isAll, id);
        if (CollectionUtils.isEmpty(pageBeanBO.getList())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "日志记录不存在");
        }
        log.debug("日志操作情况一览...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);

    }
}