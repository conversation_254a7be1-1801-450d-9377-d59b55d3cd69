package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.SweepActivityOfferCouponDTO;
import com.extracme.evcard.mmp.service.ISweepActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/4/9
 * \* Time: 11:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 发放扫码活动优惠券
 * \
 */
@RestController
@RequestMapping("inner")
public class SweepActivityOfferController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ISweepActivityService sweepActivityServiceImpl;

    /**
     * 发放扫码活动优惠券 evcard-rest调用
     *
     * @param sweepActivityOfferCouponDTO
     * @return
     */
    @RequestMapping(value = "offerSweepActivityCoupon", method = RequestMethod.POST)
    public DefaultWebRespVO offerSweepActivityCoupon(SweepActivityOfferCouponDTO sweepActivityOfferCouponDTO) {
        log.info("----------evcard-rest调用已进入offerSweepActivityCoupon------参数sweepActivityOfferCouponDTO: " + JSON.toJSONString(sweepActivityOfferCouponDTO));
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.offerSweepActivityCoupon(sweepActivityOfferCouponDTO);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage());
        }
        log.info("发放扫码活动优惠券...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券领取成功");
    }

    /**
     * 发放扫码活动优惠券校验 evcard-rest调用
     * @param SweepActivityOfferCouponDTO
     * @return
     */
    @RequestMapping(value = "checkSweepActivityCoupon", method = RequestMethod.POST)
    public DefaultWebRespVO checkSweepActivityCoupon(SweepActivityOfferCouponDTO sweepActivityOfferCouponDTO) {
        log.info("----------evcard-rest调用已进入checkActivityOfferCouponDTO------参数checkActivityOfferCouponDTO:{} ", JSON.toJSONString(sweepActivityOfferCouponDTO));
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.checkSweepActivityCoupon(sweepActivityOfferCouponDTO);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券领取成功");
    }

    /**
     * 查询扫码活动 evcard-rest调用
     *
     * @param sweepActivityOfferCouponDTO
     * @return
     */
    @RequestMapping(value = "getSweepActivity", method = RequestMethod.POST)
    public DefaultWebRespVO getSweepActivity(SweepActivityOfferCouponDTO sweepActivityOfferCouponDTO) {
        log.info("----------evcard-rest调用已进入getSweepActivity------参数sweepActivityOfferCouponDTO: " + JSON.toJSONString(sweepActivityOfferCouponDTO));
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.getSweepActivity(sweepActivityOfferCouponDTO);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage(), respDTO.getData());
        }
        log.info("查询扫码活动...，返回：" + JSON.toJSONString(respDTO));
        Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
        return DefaultWebRespVO.getSuccessVO(map);
    }
}