package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.BatchAddChannelRewardActivityDTO;
import com.extracme.evcard.mmp.dto.ChannelRewardActivityDetailDTO;
import com.extracme.evcard.mmp.dto.ChannelRewardActivityFullDTO;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.service.IChannelRewardActivityService;
import com.extracme.evcard.mmp.service.IMarketActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/16
 * \* Time: 14:10
 * \* To change this template use File | Settings | File Templates.
 * \* Description:渠道注册奖励送券配置
 * \
 */
@Api(value="channelReward", tags = "渠道注册奖励送券配置[4]")
@RestController
@RequestMapping("api/channelReward")
public class ChannelRewardActivityController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IChannelRewardActivityService channelRewardActivityServiceImpl;

    @Resource
    private IMarketActivityService marketActivityServiceImpl;

    /**
     * 新增渠道注册奖励配置
     *
     * @param channelRewardActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="新增渠道注册奖励配置", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "addChannelRewardActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addChannelRewardActivity(
    		@RequestParam(value = "imageFile") CommonsMultipartFile imageFile,
            ChannelRewardActivityFullDTO channelRewardActivityFullDTO,
            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.addChannelRewardActivity(channelRewardActivityFullDTO, imageFile, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增渠道注册奖励活动发券配置信息...");
        return vo;
    }

    /**
     * 批量手动生成活动
     * @param request
     * @return
     */
    @ApiOperation(value="批量手动生成活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "batchAddChannelRewardActivity", method = RequestMethod.POST)
    public DefaultWebRespVO batchAddChannelRewardActivity(@RequestBody BatchAddChannelRewardActivityDTO batchAddChannelRewardActivityDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.batchAddChannelRewardActivity(batchAddChannelRewardActivityDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增渠道注册奖励活动发券配置信息...");
        return vo;
    }


    /**
     * 修改渠道注册奖励配置
     *
     * @param channelRewardActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="修改渠道注册奖励配置", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "updateChannelRewardActivity", method = RequestMethod.POST)
    public DefaultWebRespVO updateChannelRewardActivity(
    		@RequestParam(value = "imageFile", required = false) CommonsMultipartFile imageFile,
            ChannelRewardActivityFullDTO channelRewardActivityFullDTO,
            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.updateChannelRewardActivity(channelRewardActivityFullDTO, imageFile, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改渠道注册奖励活动发券配置信息...");
        return vo;
    }

    /**
     * 获取渠道注册奖励优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @ApiOperation(value="获取渠道注册奖励优惠券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getChannelRewardCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getChannelRewardCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        //ChannelActivityCouponModelDTO channelActivityCouponModelDTO = channelRewardActivityServiceImpl.getChannelRewardCouponModelPage(paramsBO);
        ActivityCouponModelPageDTO couponModelPageDTO = marketActivityServiceImpl.getCouponModelPage(paramsBO);
        return DefaultWebRespVO.getSuccessVO(couponModelPageDTO);
    }

    /**
     * 获取渠道注册奖励活动发券信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value="获取渠道注册奖励活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "channelRewardActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO channelRewardActivityDetail(@PathVariable("id") Long id) {
        ChannelRewardActivityDetailDTO channelRewardActivityDetailDTO =
                channelRewardActivityServiceImpl.channelRewardActivityDetail(id);
        if (null == channelRewardActivityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取渠道注册奖励活动发券配置信息...");
        return DefaultWebRespVO.getSuccessVO(channelRewardActivityDetailDTO);
    }


    /**
     * 立即开始渠道注册奖励活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="立即开始渠道注册奖励活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "immediateStartChannelRewardActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartChannelRewardActivity(@PathVariable("id") Long id,
                                                                HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.immediateStartChannelRewardActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始渠道注册奖励活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 立即开始渠道注册奖励活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="发布渠道注册奖励活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始渠道注册奖励活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }



    /**
     * 删除渠道注册奖励活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="删除渠道注册奖励活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "deleteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.deleteActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 停止渠道注册奖励活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="停止渠道注册奖励活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "suspendChannelRewardActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendChannelRewardActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        String operatorContent = "停止";
        DefaultServiceRespDTO respDTO =
                channelRewardActivityServiceImpl.suspendChannelRewardActivity(id, createOperName, createOperId, operatorContent);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止渠道注册奖励活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }
    
    /**
     * 
     * @param activityId
     * @return
     */
    @ApiOperation(value="获取渠道注册活动扫码活动券", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "activityId", value = "活动id", required = true, paramType = "query", dataType = "Long")})
    @RequestMapping(value = "getChannelRewardActivity", method = RequestMethod.POST)
    public DefaultWebRespVO getChannelRewardActivity(String activityId) {
        log.info("----------evcard-rest调用已进入getChannelRewardActivity------参数activityId: " +activityId);
        DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.getChannelRewardActivity(activityId);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage(), respDTO.getData());
        }
        log.info("查询渠道活动...，返回：" + JSON.toJSONString(respDTO));
        Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
        return DefaultWebRespVO.getSuccessVO(map);
    }

    /**
     *
     * @param activityId
     * @return
     */
    @ApiOperation(value="短信发送日志", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "activityId", value = "活动id", required = true, paramType = "query", dataType = "Long")})
    @RequestMapping(value = "getActivitySmsLog/{activityId}", method = RequestMethod.GET)
    public DefaultWebRespVO getActivitySmsLog(@PathVariable("activityId") Long activityId) {
        log.info("导出渠道注册活动短信发送日志，活动编号activityId:{}", activityId);
        try {
            DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.getActivitySmsLog(activityId);
            Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
            return DefaultWebRespVO.getSuccessVO(map);
        } catch (Exception e) {
            log.error("导出渠道注册活动短信发送日志失败，activityId:{}", activityId, e);
            return new DefaultWebRespVO("-1", "导出失败");
        }
    }
}