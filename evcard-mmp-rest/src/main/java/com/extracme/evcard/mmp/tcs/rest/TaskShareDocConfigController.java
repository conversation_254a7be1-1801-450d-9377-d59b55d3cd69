package com.extracme.evcard.mmp.tcs.rest;

import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.service.tcs.ITcsTaskCenterService;
import com.extracme.evcard.tcs.provider.api.dto.TaskOperateLogDTO;
import com.extracme.evcard.tcs.provider.api.dto.model.PackageFile;
import com.extracme.evcard.tcs.provider.api.dto.model.TcsShareDoc;
import com.extracme.evcard.tcs.provider.api.dto.model.resultOrUpdateModel.ResultOrUpdateShareDoc;
import com.extracme.evcard.tcs.provider.api.dto.model.UserMessgae;
import com.extracme.evcard.tcs.provider.api.service.IShareDocConfigServiceProvider;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/06
 * 分享文案配置
 */
@RestController
@RequestMapping("api/shareDoc")
public class TaskShareDocConfigController {

    @Resource
    IShareDocConfigServiceProvider iShareDocConfigServiceProvider;

    @Resource
    ITcsTaskCenterService tcsTaskCenterServiceImpl;

    /**
     * 文案展示页 and 查询
     * @param scenes
     * @return
     */
    @RequestMapping(value = "initLoad", method = RequestMethod.GET)
    public DefaultWebRespVO initLoad(@RequestParam(value = "scenes", defaultValue = "0")Integer scenes, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            TcsShareDoc tcsShareDoc = iShareDocConfigServiceProvider.initLoad(scenes);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功");
            defaultWebRespVO.setData(tcsShareDoc);
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败");
        }
        return defaultWebRespVO;
    }

    /**
     * 文案修改或添加
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "saveOrUpdate", method = RequestMethod.POST)
    public DefaultWebRespVO saveOrUpdate(@RequestParam("scenes") Integer scenes,@RequestParam("title")String title, @RequestParam("content") String content,
                                         HttpServletRequest request){
        ComModel comModel = ComUtil.getUserInfo(request);
        UserMessgae userMessgae = new UserMessgae();
        userMessgae.setUpdateOperId(comModel.getUpdateOperId());
        userMessgae.setUpdateOperName(comModel.getUpdateOperName());
        ResultOrUpdateShareDoc updateShareDoc = new ResultOrUpdateShareDoc();
        updateShareDoc.setScenes(scenes);
        updateShareDoc.setTitle(title);
        updateShareDoc.setContent(content);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        CommonsMultipartFile multipartFile = null;
        PackageFile file = new PackageFile();
        try {
            multipartFile = (CommonsMultipartFile)multipartRequest.getFile("shareIconFile");
            if (multipartFile != null) {
                String fileName = multipartFile.getOriginalFilename();
                file.setName(fileName);
                file.setBytes(multipartFile.getBytes());
                updateShareDoc.setFile(file);
            }else{
                updateShareDoc.setFile(null);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            iShareDocConfigServiceProvider.update(updateShareDoc, userMessgae);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功");
        }catch (Exception e){
            e.printStackTrace();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败");
        }
        return defaultWebRespVO;
    }

    /**
     * 日志展示
     * @param
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "showLog", method = RequestMethod.GET)
    public DefaultWebRespVO showLog(@RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                    @RequestParam(value = "isAll", defaultValue = "1")Integer isAll){

        List<Long> ids = iShareDocConfigServiceProvider.getShareDocIds();
        PageBeanBO<TaskOperateLogDTO> pageBean = tcsTaskCenterServiceImpl.queryLogs(ids, 3,
                pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }
}
