package com.extracme.evcard.mmp.tcs.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.service.tcs.ITcsTaskCenterService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.tcs.provider.api.dto.MessageConfigDTO;
import com.extracme.evcard.tcs.provider.api.dto.OperateDTO;
import com.extracme.evcard.tcs.provider.api.dto.TaskOperateLogDTO;
import com.extracme.evcard.tcs.provider.api.dto.model.PageBeanDto;
import com.extracme.evcard.tcs.provider.api.service.IMessageConfigServiceProvider;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/09/06
 * 消息通知配置
 */
@RestController
@RequestMapping("api/notification")
public class TaskMessageConfigController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    IMessageConfigServiceProvider iMessageConfigServiceProvider;

    @Resource
    ITcsTaskCenterService tcsTaskCenterServiceImpl;

    /**
     * 页面初始化内容 and 查询
     * @return
     */
    @RequestMapping(value = "initLoad", method = RequestMethod.GET)
    public DefaultWebRespVO initLoad(@RequestParam(value = "pageNum",defaultValue = "1")Integer pageNum,
                                     @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                     @RequestParam(value = "status", required = false)Integer status, @RequestParam(value = "title", required = false)String title,
                                     @RequestParam(value = "startTime", required = false)String startTime, @RequestParam(value = "endTime", required = false)String endTime){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            PageBeanDto<MessageConfigDTO> tcsMessageInfos = iMessageConfigServiceProvider.initLoad(pageNum, pageSize, status, title, startTime, endTime);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
            defaultWebRespVO.setData(tcsMessageInfos);
        }catch (Exception e){
            logger.error("查询失败", e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    @RequestMapping(value = "getDetail", method = RequestMethod.GET)
    public DefaultWebRespVO getDetail(@RequestParam("id") Long id){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            MessageConfigDTO messageConfigDTO = iMessageConfigServiceProvider.getDetail(id);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setData(messageConfigDTO);
            defaultWebRespVO.setMessage("成功");
        }catch (Exception e){
            logger.error("查询失败", e);
            defaultWebRespVO.setData("-1");
            defaultWebRespVO.setMessage("失败");
        }
        return defaultWebRespVO;
    }

    /**
     * 消息新增
     * @param title
     * @param content
     * @param startTime
     * @param endTime
     * @return
     */
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public DefaultWebRespVO save(@RequestParam("title")String title, @RequestParam("content")String content,
                                 @RequestParam("startTime")String startTime, @RequestParam("endTime")String endTime, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        if (title.length()>20){
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("标题长度不可超出20个字符！");
            return defaultWebRespVO;
        }
        if (content.length()>100){
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("内容长度不可超过100字符！");
            return defaultWebRespVO;
        }
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
            messageConfigDTO.setTitle(title);
            messageConfigDTO.setContent(content);
            messageConfigDTO.setStartTime(startTime);
            messageConfigDTO.setEndTime(endTime);
            iMessageConfigServiceProvider.save(messageConfigDTO, operateDTO);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("创建成功！");
        }catch (Exception e){
            logger.error("创建失败", e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("创建失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 消息修改
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public DefaultWebRespVO update(@RequestParam("id")Long id,@RequestParam("title")String title,@RequestParam("content")String content,
                                   @RequestParam("startTime")String startTime,@RequestParam("endTime")String endTime, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setId(id);
        messageConfigDTO.setTitle(title);
        messageConfigDTO.setContent(content);
        messageConfigDTO.setStartTime(startTime);
        messageConfigDTO.setEndTime(endTime);
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            iMessageConfigServiceProvider.update(messageConfigDTO, operateDTO);
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("更新失败，id=" + id, e);
            defaultWebRespVO.setMessage("失败！");
            defaultWebRespVO.setCode("-1");
        }
        return defaultWebRespVO;
    }

    /**
     * 发布消息
     * @param id
     * @return
     */
    @RequestMapping(value = "release/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO release(@PathVariable Long id, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            BaseResponse result = iMessageConfigServiceProvider.release(id, operateDTO);
            logger.debug("调用结果-----------------------------"+JSON.toJSONString(result));
            if (result.getCode() < 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("发布失败，id=" + id, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 上线
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "online/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO online(@PathVariable Long id, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            BaseResponse result = iMessageConfigServiceProvider.online(id, operateDTO);
            if (result.getCode() < 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("上线失败，id=" + id, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 消息停止
     * @param id
     * @return
     */
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO stop(@PathVariable Long id, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            BaseResponse result = iMessageConfigServiceProvider.stop(id, operateDTO);
            if (result.getCode() < 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("下线失败，id=" + id, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 暂停
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "timeOut/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO timeOut(@PathVariable Long id, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            BaseResponse result = iMessageConfigServiceProvider.timeOut(id, operateDTO);
            if (result.getCode() < 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("失败，id=" + id, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 消息删除
     * @param id
     * @return
     */
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO delete(@PathVariable Long id, HttpServletRequest request){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        OperateDTO operateDTO = ComUtil.getOperatorInfo(request);
        try {
            BaseResponse result = iMessageConfigServiceProvider.delete(id, operateDTO);
            if (result.getCode() < 0){
                defaultWebRespVO.setCode("-1");
                defaultWebRespVO.setMessage(result.getMessage());
                return defaultWebRespVO;
            }
            defaultWebRespVO.setCode("0");
            defaultWebRespVO.setMessage("成功！");
        }catch (Exception e){
            logger.error("失败，id=" + id, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("失败！");
        }
        return defaultWebRespVO;
    }

    /**
     * 日志展示
     * @param id
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "showLog", method = RequestMethod.GET)
    public DefaultWebRespVO showLog(@RequestParam("id") Long id, @RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                    @RequestParam(value = "isAll", defaultValue = "1")Integer isAll){
        List<Long> configIds = Arrays.asList(id);
        PageBeanBO<TaskOperateLogDTO> pageBean = tcsTaskCenterServiceImpl.queryLogs(configIds, 2,
                pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }
}
