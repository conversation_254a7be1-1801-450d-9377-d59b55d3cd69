package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.DateUtils;
import com.extracme.evcard.mmp.dto.activity.AddAppHomeConfigTo;
import com.extracme.evcard.mmp.dto.activity.SearchAppHomeConfigTo;
import com.extracme.evcard.mmp.dto.activity.TopCity;
import com.extracme.evcard.mmp.exception.AppConfigBusinessException;
import com.extracme.evcard.mmp.exception.AppConfigException;
import com.extracme.evcard.mmp.service.IUserService;
import com.extracme.evcard.mmp.service.activity.AppHomeBannerDisplayService;
import com.extracme.evcard.mmp.vo.*;
import com.extracme.evcard.sts.service.BasicService;
import com.extracme.evcard.sts.to.SearchBaseOperateLogTo;
import com.extracme.framework.core.dto.UserDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * app 首页banner 配置
 */
@RestController
@RequestMapping("api/bookOrderHomePage")
public class BookOrderHomeDisplayController {

    private static Logger logger = LoggerFactory.getLogger(BookOrderHomeDisplayController.class);

    @Autowired
    private IUserService userService;

    @Autowired
    private AppHomeBannerDisplayService appHomeBannerDisplayService;

    @Autowired
    private BasicService basicService;

    /**
     * 新增APP首页显示配置
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/addBookAppHomeConfig", method = RequestMethod.POST)
    public DefaultWebRespVO addBookAppHomeConfig(@RequestBody @Valid BookAddAppHomeConfigVo vo, BindingResult bs,
                                                 HttpServletRequest request) {
        if (bs.hasErrors()) {
            logger.warn("新增APP首页显示配置=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", bs.getAllErrors().get(0).getDefaultMessage());
            return result;
        }

        if (CollectionUtils.isEmpty(vo.getCityList())) {
            logger.warn("新增APP首页显示配置=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "请选择活动城市");
            return result;
        }

        Integer homeBannerPicSort = vo.getHomeBannerPicSort();
        if (homeBannerPicSort != null && (homeBannerPicSort < 0 || homeBannerPicSort > 9)) {
            logger.warn("新增APP首页显示配置=" + JSON.toJSONString(vo) + "  首页显示图片排序范围0-9");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "首页显示图片排序范围0-9");
            return result;
        }

        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            UserDTO user = userService.selectByUsername(request.getRemoteUser());
            AddAppHomeConfigTo to = new AddAppHomeConfigTo();
            BeanUtils.copyProperties(vo, to);
            Date startTime = DateUtils.getDateFromStr(vo.getStartTime(), DateUtils.DATE_TYPE1);
            Date endTime = DateUtils.getDateFromStr(vo.getEndTime(), DateUtils.DATE_TYPE1);
            to.setStartTime(startTime);
            to.setEndTime(endTime);
            to.setOperUserId(user.getId());
            to.setOperUserName(user.getName());
            to.setOperOrgCode(user.getOrgCode());
            appHomeBannerDisplayService.addBookAppHomeConfig(to);
        } catch (AppConfigException e) {
            logger.warn("新增APP首页显示配置=" + JSON.toJSONString(vo) + " " + e.getMessage(), e);
            respVO.setCode(String.valueOf(e.getCode()));
            respVO.setMessage(e.getMessage());
        }
        return respVO;
    }

    /**
     * 查询APP首页显示配置列表
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/getBookAppHomeConfigList", method = RequestMethod.POST)
    public DefaultWebRespVO getBookAppHomeConfigList(@RequestBody SearchAppHomeConfigVo vo, HttpServletRequest request) {
        SearchAppHomeConfigTo to = new SearchAppHomeConfigTo();
        BeanUtils.copyProperties(vo, to);
        //开始位置
        to.setOffset((vo.getPageNum() - 1) * vo.getPageSize());
        //查询数量
        to.setLimit(vo.getPageSize());
        UserDTO user = userService.selectByUsername(request.getRemoteUser());
        to.setUserOrgCode(user.getOrgCode());
        return DefaultWebRespVO.getSuccessVO(appHomeBannerDisplayService.getBookAppHomeConfigList(to));
    }

    /**
     * 查询APP首页显示配置详情
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/getBookAppHomeConfigDetail", method = RequestMethod.POST)
    public DefaultWebRespVO getAppHomeConfigDetail(@RequestBody SaveDailyRentOilPriceConfigVo vo, HttpServletRequest request) {
        if (vo.getId() == null || vo.getId() <= 0) {
            logger.warn("查询APP首页显示配置详情=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "请选择记录");
            return result;
        }
        return DefaultWebRespVO.getSuccessVO(appHomeBannerDisplayService.getBookAppHomeConfigDetail(vo.getId()));
    }

    /**
     * 修改APP首页显示配置
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/updateBookAppHomeConfig", method = RequestMethod.POST)
    public DefaultWebRespVO updateBookAppHomeConfig(@RequestBody @Valid BookAddAppHomeConfigVo vo, BindingResult bs,
                                                    HttpServletRequest request) {
        if (bs.hasErrors()) {
            logger.warn("修改APP首页显示配置=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", bs.getAllErrors().get(0).getDefaultMessage());
            return result;
        }
        if (vo.getId() == null) {
            logger.warn("修改APP首页显示配置=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "请选择修改的记录");
            return result;
        }

        List<TopCity> cityList = vo.getCityList();
        if (CollectionUtils.isEmpty(cityList)) {
            logger.warn("修改APP首页显示配置=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "请选择活动城市");
            return result;
        }

        Integer homeBannerPicSort = vo.getHomeBannerPicSort();
        if (homeBannerPicSort != null && (homeBannerPicSort < 0 || homeBannerPicSort > 9)) {
            logger.warn("修改APP首页显示配置=" + JSON.toJSONString(vo) + "  首页显示图片排序范围0-9");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "首页显示图片排序范围0-9");
            return result;
        }

        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            UserDTO user = userService.selectByUsername(request.getRemoteUser());
            AddAppHomeConfigTo to = new AddAppHomeConfigTo();
            BeanUtils.copyProperties(vo, to);
            Date startTime = DateUtils.getDateFromStr(vo.getStartTime(), DateUtils.DATE_TYPE1);
            Date endTime = DateUtils.getDateFromStr(vo.getEndTime(), DateUtils.DATE_TYPE1);
            to.setStartTime(startTime);
            to.setEndTime(endTime);
            to.setOperUserId(user.getId());
            to.setOperUserName(user.getName());
            to.setOperOrgCode(user.getOrgCode());
            appHomeBannerDisplayService.updateBookAppHomeConfig(vo.getId(), to);
        } catch (AppConfigException e) {
            logger.warn("修改APP首页显示配置=" + JSON.toJSONString(vo) + " " + e.getMessage(), e);
            respVO.setCode(String.valueOf(e.getCode()));
            respVO.setMessage(e.getMessage());
        }
        return respVO;
    }

    /**
     * 下线APP首页显示配置
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/offLineBookAppHomeConfig", method = RequestMethod.POST)
    public DefaultWebRespVO offLineBookAppHomeConfig(@RequestBody SaveDailyRentOilPriceConfigVo vo,
                                                     HttpServletRequest request) {
        if (vo.getId() == null || vo.getId() <= 0) {
            logger.warn("下线APP首页显示配置=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "请选择需要下线的记录");
            return result;
        }
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            UserDTO user = userService.selectByUsername(request.getRemoteUser());
            appHomeBannerDisplayService.offLineBookAppHomeConfig(vo.getId(), user.getId(), user.getName(), user.getOrgCode());
        } catch (AppConfigException e) {
            logger.warn("下线APP首页显示配置=" + JSON.toJSONString(vo) + " " + e.getMessage(), e);
            respVO.setCode(String.valueOf(e.getCode()));
            respVO.setMessage(e.getMessage());
        }
        return respVO;
    }

    /**
     * 查询APP首页显示操作日志
     *
     * @param vo
     * @return
     */
    @RequestMapping(value = "/searchBookAppHomeLogList", method = RequestMethod.POST)
    public DefaultWebRespVO searchBookAppHomeLogList(@RequestBody SearchDailyRentDefaultPriceConfigListVo vo, HttpServletRequest request) {
        if (vo.getId() == null) {
            logger.warn("查询APP首页显示操作日志=" + JSON.toJSONString(vo) + "  参数缺失");
            DefaultWebRespVO result = new DefaultWebRespVO("1", "请选择查询的记录");
            return result;
        }
        SearchBaseOperateLogTo configListTO = new SearchBaseOperateLogTo();
        BeanUtils.copyProperties(vo, configListTO);
        //开始位置
        configListTO.setOffset((vo.getPageNum() - 1) * vo.getPageSize());
        //查询数量
        configListTO.setLimit(vo.getPageSize());
        configListTO.setLogType(65);
        configListTO.setForeignKey(vo.getId().toString());
        return DefaultWebRespVO.getSuccessVO(basicService.searchEntranceDisplayConfigLogList(configListTO));
    }

    /**
     * 查询待审核APP首页显示配置列表
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/searchBookWaitReviewConfigList", method = RequestMethod.POST)
    public DefaultWebRespVO searchBookWaitReviewConfigList(@RequestBody SearchAppHomeConfigVo vo, HttpServletRequest request) {
        SearchAppHomeConfigTo to = new SearchAppHomeConfigTo();
        BeanUtils.copyProperties(vo, to);
        //开始位置
        to.setOffset((vo.getPageNum() - 1) * vo.getPageSize());
        //查询数量
        to.setLimit(vo.getPageSize());
        UserDTO user = userService.selectByUsername(request.getRemoteUser());
        to.setUserOrgCode(user.getOrgCode());
        to.setReviewStatus(0);
        return DefaultWebRespVO.getSuccessVO(appHomeBannerDisplayService.getBookAppHomeConfigList(to));
    }

    /**
     * 审核APP首页显示配置
     *
     * @return DefaultWebRespVO
     */
    @RequestMapping(value = "/bookReviewAppHomeConfig", method = RequestMethod.POST)
    public DefaultWebRespVO bookReviewAppHomeConfig(@RequestBody ReviewAppHomeConfigVo vo, HttpServletRequest request) {
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            UserDTO user = userService.selectByUsername(request.getRemoteUser());
            appHomeBannerDisplayService.bookreviewAppHomeConfig(vo.getId(), vo.getReviewStatus(), user.getId(), user.getName(), user.getOrgCode());
        } catch (AppConfigBusinessException e) {
            logger.warn("审核APP首页显示配置=|" + JSON.toJSONString(vo) + "| " + e.getMessage(), e);
            respVO.setCode(String.valueOf(e.getCode()));
            respVO.setMessage(e.getMessage());
        }
        return respVO;
    }
}
