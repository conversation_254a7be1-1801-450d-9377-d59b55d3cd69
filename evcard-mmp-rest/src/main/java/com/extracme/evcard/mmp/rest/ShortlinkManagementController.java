package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpShortLinkLogDTO;
import com.extracme.evcard.mmp.dto.MmpShortLinkManagementDTO;
import com.extracme.evcard.mmp.service.MmpShortlinkManagementService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 短链接管理
 */
@RestController
@RequestMapping("api/shortlink")
@Api(tags = "短链接管理接口文档")
public class ShortlinkManagementController {
	@Autowired
	private MmpShortlinkManagementService mmpShortlinkManagementService;


	private final Logger log = LoggerFactory.getLogger(this.getClass());

	/**
	 * 新增短链
	 * @param mmpShortLinkManagementDTO
	 * @return
	 */
	@PostMapping("insertOrUpdateShortlink")
	public DefaultWebRespVO  insertOrUpdateShortlink(@RequestBody MmpShortLinkManagementDTO mmpShortLinkManagementDTO,HttpServletRequest request){

		DefaultWebRespVO vo = null;
		try {
			MmpShortLinkManagementDTO MmpShortLinkManagementDTO = mmpShortlinkManagementService.insertOrUpdateShortlink(mmpShortLinkManagementDTO, request);
			vo = DefaultWebRespVO.getSuccessVO(MmpShortLinkManagementDTO);
		}catch (Exception ex) {
			log.error("新增或修改短链失败", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增或修改短链失败");
		}
		return vo;
	}

	/**
	 * 查询所有短链列表
	 * @param mmpShortLinkManagementDTO
	 * @return
	 */
	@PostMapping("getShortlinkList")
	public PageBeanBO<MmpShortLinkManagementDTO> getShortlinkList(@RequestBody MmpShortLinkManagementDTO mmpShortLinkManagementDTO){
		return mmpShortlinkManagementService.getShortlinkList(mmpShortLinkManagementDTO);
	}
	/**
	 * 短链重定向
	 */
	@GetMapping("shortlinkRedirect")
	public void shortlinkRedirect(@RequestParam(value = "randomCode")String randomCode,HttpServletRequest request, HttpServletResponse response){
		mmpShortlinkManagementService.shortlinkRedirect(randomCode,request,response);
	}

	/**
	 * 重新生成二维码
	 * @param mmpShortLinkManagementDTO
	 * @return
	 */
	@PostMapping("reCreateQRCode")
	public MmpShortLinkManagementDTO reCreateQRCode(MmpShortLinkManagementDTO mmpShortLinkManagementDTO){
		return mmpShortlinkManagementService.reCreateQRCode(mmpShortLinkManagementDTO);
	}

	@GetMapping("createQrCode")
	public String createQrCode(@RequestParam(value = "shortUrl")String shortUrl,@RequestParam(value = "randomCode")String randomCode){
		return mmpShortlinkManagementService.createQrCode(shortUrl,randomCode);
	}
	@GetMapping("getLogPageByLinkedId")
	public PageBeanBO<MmpShortLinkLogDTO> getLogPageByLinkedId(@RequestParam(value = "linkedId")Long linkedId, Integer pageNum, Integer pageSize, Integer isAll){
		return mmpShortlinkManagementService.getLogPageByLinkedId(linkedId,pageNum,pageSize,isAll);
	}
	@PostMapping("getLongUrlParams")
	public String getLongUrlParams(@RequestBody Map<String,String> map){
		String randomCode = map.get("randomCode");
		return mmpShortlinkManagementService.getLongUrlParams(randomCode);
	}

}
