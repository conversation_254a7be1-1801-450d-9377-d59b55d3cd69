package com.extracme.evcard.mmp.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.tcs.ITcsTaskCenterService;
import com.extracme.evcard.tcs.provider.api.bo.TaskQueryBO;
import com.extracme.evcard.tcs.provider.api.dto.task.TaskListPageDTO;
import com.extracme.evcard.tcs.provider.api.service.task.ITaskCenterServiceProvider;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.model.PageBean;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import com.extracme.evcard.mmp.service.IAgencyService;

import java.util.*;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：AgencyController
 * 类描述：关联企业控制层
 * 创建人：sunb-孙彬
 * 创建时间：2017年9月20日下午9:07:09
 * 修改备注
 *
 * @version2.0
 */
@RestController
@RequestMapping("api")
public class AgencyController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    IAgencyService agencyServiceImpl;

    /**
     * 获取关联企业下拉框
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "agencyInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryAgencyInfoList(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<AgencyDTO> agencyList = agencyServiceImpl.getAgencyAll(request);
            return DefaultWebRespVO.getSuccessVO(agencyList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("获取关联企业下拉框...");
        return vo;
    }

    /**
     * 包夜车活动名称
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "nightCarActivity", method = RequestMethod.GET)
    public DefaultWebRespVO queryNightCarActivityList(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<ActivityNameDTO> nightCarActivityList = agencyServiceImpl.queryNightCarActivityList(request);
            return DefaultWebRespVO.getSuccessVO(nightCarActivityList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
        }
        log.debug("获取包夜车活动名称");
        return vo;
    }

    /**
     * 获取渠道来源下拉框
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "appInfo", method = RequestMethod.POST)
    public DefaultWebRespVO queryAppAllList(@RequestBody AppDTO appDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<AppDTO> agencyList = agencyServiceImpl.getAppAll(appDTO, request);
            return DefaultWebRespVO.getSuccessVO(agencyList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("获取渠道来源下拉框...");
        return vo;
    }

    /**
     * 获取会员所属公司下拉框
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "orgInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryOrgAllList(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<OrgInfoDTO> agencyList = agencyServiceImpl.findAll(request);
            return DefaultWebRespVO.getSuccessVO(agencyList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("获取会员所属公司下拉框...");
        return vo;
    }

    /**
     * 获取会员所属公司下拉框-全部
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "orgInfoList", method = RequestMethod.GET)
    public DefaultWebRespVO queryOrgInfoList(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<OrgInfoDTO> agencyList = agencyServiceImpl.findAllOrgs(request);
            return DefaultWebRespVO.getSuccessVO(agencyList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("获取会员所属公司下拉框...");
        return vo;
    }

    /**
     * 获取员所属公司的省市区缩写下拉框
     *
     * @param orgId
     * @return
     */
    @RequestMapping(value = "plateInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryPlateInfoList(@RequestParam(value = "orgId", required = true) String orgId) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<OrgInfoDTO> orgList = agencyServiceImpl.queryPlateInfoList(orgId);
            return DefaultWebRespVO.getSuccessVO(orgList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
        }
        log.debug("获取所属公司的所在省，直辖市的缩写...");
        return vo;
    }

    /**
     * 获取免押等级下拉框
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "exemptDepositGradeInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryExemptDepositGradeList(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            List<MmpDepositGradeDTO> depositGradeList = agencyServiceImpl.queryExemptDepositGradeList(request);
            return DefaultWebRespVO.getSuccessVO(depositGradeList);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
        }
        log.debug("获取免押等级下拉框...");
        return vo;
    }

    /**
     * 优惠券发放记录一览活动名称下拉框
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "activityName", method = RequestMethod.GET)
    public DefaultWebRespVO queryActivityNameList(HttpServletRequest request) {
        List<String> activityNameList = agencyServiceImpl.queryActivityNameList(request);
        log.debug("获取包夜车活动名称");
        return DefaultWebRespVO.getSuccessVO(activityNameList);
    }


    /**
     * 根据会员名称查询会员手机号
     *
     * @param name 会员名称
     * @return
     */
    @RequestMapping(value = "queryTelByName", method = RequestMethod.GET)
    public DefaultWebRespVO queryTelByName(@RequestParam(value = "name", required = true) String name) {
        List<String> namelist = agencyServiceImpl.queryTelByName(name);
        log.debug("根据会员名称查询会员手机号...");
        return DefaultWebRespVO.getSuccessVO(namelist);
    }

    /**
     * 根据会员手机号查询会员名称
     *
     * @param mobilePhone 会员手机号
     * @return
     */
    @RequestMapping(value = "queryNameByTel", method = RequestMethod.GET)
    public DefaultWebRespVO queryNameByTel(@RequestParam(value = "mobilePhone", required = true) String mobilePhone) {
        List<String> namelist = agencyServiceImpl.queryNameByTel(mobilePhone);
        if(CollectionUtils.isEmpty(namelist)) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "手机号为" + mobilePhone + "的会员不存在。");
        }
        log.debug("根据会员手机号查询会员名称...");
        return DefaultWebRespVO.getSuccessVO(namelist.get(0));
    }

    /**
     * 根据活动编号查询活动名称、类型、活动所属单位
     *
     * @param actionId 活动编号
     * @return
     */
    @RequestMapping(value = "activityInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryActivityInfoById(@RequestParam(value = "actionId", required = true) String actionId) {
        ActivityInfoDTO activityInfoDTO = agencyServiceImpl.queryActivityInfoById(actionId);
        log.debug("根据活动编号查询活动名称、类型、活动所属单位...");
        return DefaultWebRespVO.getSuccessVO(activityInfoDTO);
    }

    /**
     * 根据活动名称、类型、活动所属单位查询活动编号
     *
     * @param activityName 活动编号
     * @param type         活动类型
     * @param orgId        所属单位
     * @return
     */
    @Resource
    ITcsTaskCenterService tcsTaskCenterServiceImpl;

    @RequestMapping(value = "activityId", method = RequestMethod.GET)
    public DefaultWebRespVO queryActivityId(
            @RequestParam(value = "activityName", required = false) String activityName,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "orgId", required = false) String orgId,
            @RequestParam(value = "queryType", defaultValue = "0") Integer queryType,
            @RequestParam(value = "activityOrgId", required = false) String activityOrgId) {
        //查询任务中心,则查询任务中心id列表
        if(queryType.equals(1)) {
            TaskQueryBO taskQueryBO = new TaskQueryBO();
            taskQueryBO.setType(String.valueOf(type));
            taskQueryBO.setIsAll(0);
            taskQueryBO.setPageSize(100);
            PageBeanBO<TaskListPageDTO> list = tcsTaskCenterServiceImpl.queryList(taskQueryBO);
            List<String> result = new ArrayList<>(5);
            if(list != null && CollectionUtils.isNotEmpty(list.getList())) {
                for (TaskListPageDTO task: list.getList()) {
                    result.add(String.valueOf(task.getId()));
                }
            }
            return DefaultWebRespVO.getSuccessVO(result);
        }

        if(StringUtils.isNotBlank(activityOrgId)) {
            orgId = activityOrgId;
        }
        List<String> activityIdlist = agencyServiceImpl.queryActivityId(activityName, type, orgId);
        log.debug("根据活动名称、类型、活动所属单位查询活动编号...");
        return DefaultWebRespVO.getSuccessVO(activityIdlist);
    }

    /**
     * 根据活动名称、类型、活动所属单位查询活动编号和活动名称
     *
     * @param activityName 活动编号
     * @param type         活动类型
     * @param orgId        所属单位
     * @return
     */
    @RequestMapping(value = "queryActivityIdAndName", method = RequestMethod.GET)
    public DefaultWebRespVO queryActivityIdAndName(
            @RequestParam(value = "activityName", required = false) String activityName,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "orgId", required = false) String orgId,
            @RequestParam(value = "queryType", defaultValue = "0") Integer queryType,
            @RequestParam(value = "activityOrgId", required = false) String activityOrgId) {
        //查询任务中心,则查询任务中心id列表
        if(queryType.equals(1)) {
            TaskQueryBO taskQueryBO = new TaskQueryBO();
            taskQueryBO.setType(String.valueOf(type));
            taskQueryBO.setIsAll(0);
            taskQueryBO.setPageSize(100);
            PageBeanBO<TaskListPageDTO> list = tcsTaskCenterServiceImpl.queryList(taskQueryBO);
            List<ActivityIdAndNameDTO> idAndNameDTOList=new ArrayList<>();
            if(list != null && CollectionUtils.isNotEmpty(list.getList())) {
                for (int i=0,j=list.getList().size();j>i;j--){
                    ActivityIdAndNameDTO dto=new ActivityIdAndNameDTO();
                    dto.setId(String.valueOf(list.getList().get(j).getId()));
                    dto.setName(String.valueOf(list.getList().get(j).getTaskName()));
                    idAndNameDTOList.add(dto);
                }
            }
            return DefaultWebRespVO.getSuccessVO(idAndNameDTOList);
        }

        if(StringUtils.isNotBlank(activityOrgId)) {
            orgId = activityOrgId;
        }
        List<ActivityIdAndNameDTO> activityInfoDTOList = agencyServiceImpl.queryActivityIdAndName(activityName, type, orgId);
        log.debug("根据活动名称、类型、活动所属单位查询活动编号和名称...");
        return DefaultWebRespVO.getSuccessVO(activityInfoDTOList);
    }
}
