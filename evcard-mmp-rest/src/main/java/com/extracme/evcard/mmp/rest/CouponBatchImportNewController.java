package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.CouponBatchImport;
import com.extracme.evcard.mmp.service.CouponBatchImportImpl;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

@RestController
@RequestMapping("api")
public class CouponBatchImportNewController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    CouponBatchImport couponBatchImport;

    /**
     * 无门槛
     * @param file
     * @param id
     * @param authority
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "startBatchImportCouponUnreserved", method = RequestMethod.POST)
    public DefaultWebRespVO startBatchImportCouponUnreserved(@RequestParam(value = "file1", required = true) CommonsMultipartFile file,
                                                             @RequestParam(value = "id", required = true) Long id,
                                                             @RequestParam(value = "authority", defaultValue = "000" ) String authority, HttpServletRequest request,
                                                             HttpServletResponse response){
        DiskFileItem fi = (DiskFileItem) file.getFileItem();
        File file1 = fi.getStoreLocation();
        String fileName = file.getOriginalFilename();
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = couponBatchImport.startBatchImportCouponUnreserved(id, file1, fileName,authority,
                    request, response);
            respVO = this.webRespVO(respDTO);
        } catch (Exception e) {
            log.warn("发券异常, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
        }
        log.debug("开始无门槛优惠券批量导入活动");
        return respVO;
    }

    /**
     * 短摸板
     * @param file
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "startBatchImportCouponActivity", method = RequestMethod.POST)
    public DefaultWebRespVO startBatchImportCouponActivity(
            @RequestParam(value = "file1", required = false) CommonsMultipartFile file,
            @RequestParam(value = "id", required = false) Long id, HttpServletRequest request,
            HttpServletResponse response) {
        DiskFileItem fi = (DiskFileItem) file.getFileItem();
        File file1 = fi.getStoreLocation();
        String fileName = file.getOriginalFilename();
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = couponBatchImport.startBatchImportCouponActivity(id, file1, fileName,
                    request, response);
            respVO = this.webRespVO(respDTO);
        } catch (Exception e) {
            log.warn("发券异常",e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
        }
        log.debug("开始优惠券批量导入活动");
        return respVO;
    }

    /**
     * 长摸板
     * @param file
     * @param id
     * @param authority
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "startBatchImportCouponLong",  method = RequestMethod.POST)
    public DefaultWebRespVO startBatchImportCouponLong(
            @RequestParam(value = "file1", required = true) CommonsMultipartFile file,
            @RequestParam(value = "id", required = true) Long id,
            @RequestParam(value = "authority", defaultValue = "000" ) String authority,HttpServletRequest request,
            HttpServletResponse response) {
        DiskFileItem fi = (DiskFileItem) file.getFileItem();
        File file1 = fi.getStoreLocation();
        String fileName = file.getOriginalFilename();
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = couponBatchImport.startBatchImportCouponLong(id, file1, fileName,authority,
                    request, response);
            respVO = this.webRespVO(respDTO);
        } catch (Exception e) {
            log.warn("发券异常" , e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
        }
        log.debug("开始优惠券批量导入活动");
        return respVO;
    }

    private DefaultWebRespVO webRespVO(DefaultServiceRespDTO respDTO){
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }else if (respDTO.getCode() == 1){
            return new DefaultWebRespVO("1", respDTO.getMessage());
        }else if (respDTO.getCode() == -10){
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage());
        }else {
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动结束");
        }
    }

}
