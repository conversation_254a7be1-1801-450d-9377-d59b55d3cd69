package com.extracme.evcard.mmp.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.AnnouncementDTO;
import com.extracme.evcard.mmp.service.IAnnouncementService;
import com.extracme.evcard.mmp.vo.AnnouncementVO;
import com.extracme.evcard.sts.exception.AppConfigException;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;

import java.util.Collections;

@RestController
@RequestMapping("api/announcement")
public class AnnouncementController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Resource
	IAnnouncementService announcementServiceImpl;

	/**
	 * 新增公告信息
	 * @param announcementVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "addAnnouncement", method = RequestMethod.POST)
	public DefaultWebRespVO addAnnouncement(@RequestBody AnnouncementVO announcementVO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		AnnouncementDTO announcementDTO = new AnnouncementDTO();
		BeanCopyUtils.copyProperties(announcementVO, announcementDTO);
		DefaultServiceRespDTO respDTO;
		try {
			respDTO = announcementServiceImpl.addAnnouncement(announcementDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (AppConfigException e) {
			vo.setCode("-1");
			vo.setMessage("新增失败,"+e.getMessage());
			return vo;
		}catch (Exception e) {
			log.debug("调用APP配置后台服务异常" + e);
			vo.setCode("-1");
			vo.setMessage("新增失败");
			return vo;
		} 
		vo.setMessage("提交成功");
		log.debug("新增公告信息...");
		return vo;
	}

	/**
	 * 修改公告信息
	 * @param announcementVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updateAnnouncement", method = RequestMethod.POST)
	public DefaultWebRespVO updateAnnouncement(@RequestBody AnnouncementVO announcementVO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		AnnouncementDTO announcementDTO = new AnnouncementDTO();
		BeanCopyUtils.copyProperties(announcementVO, announcementDTO);
		try {
			DefaultServiceRespDTO respDTO = announcementServiceImpl.updateAnnouncement(announcementDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (AppConfigException e) {
			vo.setCode("-1");
			vo.setMessage("修改失败,"+e.getMessage());
			return vo;
		}catch (Exception e) {
			log.debug("调用APP配置后台服务异常" + e);
			vo.setCode("-1");
			vo.setMessage("修改失败");
			return vo;
		}
		vo.setMessage("提交成功");
		log.debug("修改公告信息...");
		return vo;
	}

	/**
	 * 获取公告列表
	 * @param pageNum
	 * @param pageSize
	 * @param isAll
	 * @param name
	 * @param id
	 * @param orgId
	 * @param announcementStatus
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping(value = "queryAnnouncementList", method = RequestMethod.GET)
	public DefaultWebRespVO queryAnnouncementList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
			@RequestParam(value = "name", required = false) String name,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "orgId", required = false) String orgId,
			@RequestParam(value = "announcementStatus", required = false) Integer announcementStatus,
			@RequestParam(value = "startTime", required = false) String startTime,
			@RequestParam(value = "endTime", required = false) String endTime) {
		PageBeanBO<AnnouncementDTO> pageBeanBO = announcementServiceImpl.queryAnnouncementList(pageNum, pageSize, isAll,
				name, id, orgId, announcementStatus, startTime, endTime);
		log.debug("获取公告列表...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}

	/**
	 * 获取公告详情信息
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "getAnnouncementById/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getAnnouncementById(@PathVariable("id") Long id) {
		AnnouncementDTO announcementDeatil = announcementServiceImpl.getAnnouncementById(id);
		if (null == announcementDeatil) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "公告信息不存在");
		}
		log.debug("获取公告详情信息...");
		return DefaultWebRespVO.getSuccessVO(announcementDeatil);
	}

	/**
	 * 立即上线
	 * @param id
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "onLineImmediately/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO onLineImmediately(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = announcementServiceImpl.onLineImmediately(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (AppConfigException e) {
			vo.setCode("-1");
			vo.setMessage("上线失败,"+e.getMessage());
			return vo;
		} catch (Exception e) {
			log.debug("调用APP配置后台服务异常" + e);
			vo.setCode("-1");
			vo.setMessage("上线失败");
			return vo;
		}
		log.debug("立即上线");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "公告已上线");
	}

	/**
	 * 立即下线
	 * @param id
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "offLineImmediately/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO offLineImmediately(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = announcementServiceImpl.offLineImmediately(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (AppConfigException e) {
			vo.setCode("-1");
			vo.setMessage("下线失败,"+e.getMessage());
			return vo;
		} catch (Exception e) {
			log.debug("调用APP配置后台服务异常" + e);
			vo.setCode("-1");
			vo.setMessage("下线失败");
			return vo;
		}
		log.debug("立即下线");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "公告已下线");
	}

	/**
	 * 删除公告
	 * @param id
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deleteAnnouncement/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO deleteAnnouncement(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = announcementServiceImpl.deleteAnnouncement(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (AppConfigException e) {
			vo.setCode("-1");
			vo.setMessage("删除失败,"+e.getMessage());
			return vo;
		} catch (Exception e) {
			log.debug("调用APP配置后台服务异常" + e);
			vo.setCode("-1");
			vo.setMessage("删除失败");
			return vo;
		}
		log.debug("删除公告");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "删除公告");
	}
}
