package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dao.MmpActivityChannelLinkDetailMapper;
import com.extracme.evcard.mmp.dto.MmpActivityChannelLinkDto;
import com.extracme.evcard.mmp.dto.MmpActivityChannelLinkListDto;
import com.extracme.evcard.mmp.dto.MmpActivityChannelLinkPageDto;
import com.extracme.evcard.mmp.dto.MmpShortLinkManagementDTO;
import com.extracme.evcard.mmp.model.MmpActivityChannelLinkDetail;
import com.extracme.evcard.mmp.service.MmpActivityChannelLinkService;
import com.extracme.evcard.mmp.service.MmpShortlinkManagementService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> 短链接管理
 */
@RestController
@RequestMapping("api/channelLink")
public class ActivityChannelLinkController {
	@Autowired
	private MmpActivityChannelLinkService mmpActivityChannelLinkService;
	private final Logger log = LoggerFactory.getLogger(this.getClass());

	/**
	 * 新增或修改渠道对应短链
	 * @param mmpActivityChannelLinkDto
	 * @return
	 */
	@PostMapping("insertOrUpdateActivityChannelLink")
	public DefaultWebRespVO insertOrUpdateActivityChannelLink(@RequestBody MmpActivityChannelLinkDto mmpActivityChannelLinkDto, HttpServletRequest request){
		DefaultWebRespVO vo = null;
		try {
			MmpActivityChannelLinkPageDto activityChannelLinkPageDto =mmpActivityChannelLinkService.insertOrUpdateActivityChannelLink(mmpActivityChannelLinkDto,request);
			if (activityChannelLinkPageDto == null) {
				vo = new DefaultWebRespVO();
				vo.setCode(Contants.RETURN_ERROR_CODE);
				vo.setMessage("该渠道已应用，请勿重复添加");
			}else {
				vo = DefaultWebRespVO.getSuccessVO(activityChannelLinkPageDto);
			}
		}catch (Exception ex) {
			log.error("新增或修改渠道对应短链失败", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增/修改短链失败");
		}
		return vo;
	}

	/**
	 * 通过渠道信息批量新增
	 * @param mmpActivityChannelLinkListDto
	 * @param request
	 * @return
	 */
	@PostMapping("insertActivityChannelLinkByList")
	public DefaultWebRespVO insertActivityChannelLinkByList(@RequestBody MmpActivityChannelLinkListDto mmpActivityChannelLinkListDto, HttpServletRequest request){
		DefaultWebRespVO vo = null;
		try {
			List<MmpActivityChannelLinkPageDto> activityChannelLinkPageDtos =mmpActivityChannelLinkService.insertActivityChannelLinkByList(mmpActivityChannelLinkListDto,request);
			if (activityChannelLinkPageDtos == null){
				vo = new DefaultWebRespVO();
				vo.setCode(Contants.RETURN_ERROR_CODE);
				vo.setMessage("存在已应用的二级渠道，请修改后重试");
			}else {
				vo = DefaultWebRespVO.getSuccessVO(activityChannelLinkPageDtos);
			}
		}catch (Exception ex) {
			log.error("新增或修改渠道对应短链失败", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增/修改短链失败");
		}
		return vo;
	}

	/**
	 * 查询所有渠道对应短链列表
	 * @param activityId
	 * @return
	 */
	@GetMapping("getChannelLinksByActivityId")
	public DefaultWebRespVO getChannelLinksByActivityId(@RequestParam(value = "activityId") Long activityId){
		DefaultWebRespVO vo = null;
		try {
			List<MmpActivityChannelLinkPageDto> activityChannelLinkPageDtos = mmpActivityChannelLinkService.getChannelLinksByActivityId(activityId);
			vo = DefaultWebRespVO.getSuccessVO(activityChannelLinkPageDtos);
		}catch (Exception ex) {
			log.error("查询短链列表失败", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("查询失败");
		}
		return vo;
	}

}
