package com.extracme.evcard.mmp.rest;

/**
 * 市场活动
 * TODO 市场活动整体功能迁移
 */
import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.service.IMarketActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("api/activity")
public class MarketActivityController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IMarketActivityService marketActivityServiceImpl;

    /**
     *
     * @param couponModelDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "checkCouponModel", method = RequestMethod.POST)
    public DefaultWebRespVO checkCouponModel(@RequestBody MarketActivityCouponModelDTO couponModelDTO,
                                             HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = marketActivityServiceImpl.checkCouponModel(couponModelDTO, request.getRemoteUser());
        if (respDTO.getCode() != 0) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        //任务中心的任务不校验子公司发券限制
        if(couponModelDTO.getType() != null && couponModelDTO.getType() >= 100) {
            return DefaultWebRespVO.SUCCESS;
        }
        respDTO = marketActivityServiceImpl.checkCouponOfferLimit(couponModelDTO);
        if (respDTO.getCode() != 0) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return DefaultWebRespVO.SUCCESS;
    }
    /**
     * @description:市场活动获取优惠券模板
     * @return:
     * @author: DongYU
     * @param:
     */
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    DefaultWebRespVO getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO,
                                        HttpServletRequest request){
        ActivityCouponModelPageDTO couponModelPage  = marketActivityServiceImpl.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        logger.debug("获取活动发券信息...");
        return DefaultWebRespVO.getSuccessVO(couponModelPage);
    }

    /**
     * 查询活动发放优惠券统计
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "queryCouponStatistics/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryFirstOrderCouponStatistics(@PathVariable("id") Long id) {
        return marketActivityServiceImpl.queryFirstOrderCouponStatistics(id);
    }

    @RequestMapping(value = "rentMethodGroupList/{serviceType}", method = RequestMethod.GET)
    public DefaultWebRespVO rentMethodGroupList(@PathVariable("serviceType") Integer serviceType) {
        List<RentMethodDto> result = marketActivityServiceImpl.rentMethodGroupList(serviceType);
        return DefaultWebRespVO.getSuccessVO(result);
    }

    @RequestMapping(value = "rentMethodList/{serviceType}", method = RequestMethod.GET)
    public DefaultWebRespVO rentMethodList(@PathVariable("serviceType") Integer serviceType) {
        List<RentMethodDto> result = marketActivityServiceImpl.rentMethodList(serviceType);
        return DefaultWebRespVO.getSuccessVO(result);
    }


    @RequestMapping(value = "packageList", method = RequestMethod.POST)
    public DefaultWebRespVO packageList(@RequestBody PackageQueryDTO queryDto,
                                        HttpServletRequest request) {
        try {
            List<PackageViewDTO> result = marketActivityServiceImpl.queryPackageList(queryDto);
            return DefaultWebRespVO.getSuccessVO(result);
        }catch (Exception e) {
            logger.error("获取套餐列表失败, queryDto=" + JSON.toJSONString(queryDto), e);
        }
        return new DefaultWebRespVO("-1", "查询套餐列表失败");
    }
}
