package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.MembershipBlacklistBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MembershipBlacklistDTO;
import com.extracme.evcard.mmp.service.IMembershipBlacklistService;
import com.extracme.evcard.mmp.service.ListPageBeanBO;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 * 黑名单
 */
@Api(tags = "黑名单管理接口")
@RestController
@RequestMapping("/api/membershipBlacklist")
@Slf4j
public class MembershipBlacklistController {
    @Resource
    private IMembershipBlacklistService membershipBlacklistService;


    /**
     * 参数：
     * userName
     * userMobile
     * orgName
     * createOperName
     * status
     * */
    @ApiOperation(value="查询黑名单信息", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageNum",value = "起始页",dataType = "String",required = true),
            @ApiImplicitParam(name = "pageSize",value = "每页展示的行数",dataType = "String",required = true),
            @ApiImplicitParam(name = "userName",value = "姓名",dataType = "String",required = false),
            @ApiImplicitParam(name = "userMobile",value = "手机号码",dataType = "String",required = false),
            @ApiImplicitParam(name = "orgId",value = "公司编号",dataType = "String",required = false),
            @ApiImplicitParam(name = "createOperName",value = "操作人名称",dataType = "String",required = false),
            @ApiImplicitParam(name = "status",value = "状态",dataType = "String",required = false)
    })
    @ApiResponses(value={@ApiResponse(code=0, message="SUCCESS，成功"),
            @ApiResponse(code=-1, message="0以外其他错误码，失败")
    })
    @PostMapping("/query")
    public BaseResultVo<ListPageBeanBO<MembershipBlacklistBO>> query(@RequestBody(required = false) Map<String, Object> map) {
        if (ObjectUtils.isEmpty(map)){
            map=new HashMap<>();
        }
        log.info("提交参数" + map);
        BaseResultVo<ListPageBeanBO<MembershipBlacklistBO>> result = new BaseResultVo<>();
        try {
            ListPageBeanBO<MembershipBlacklistBO> listPageBeanBO= membershipBlacklistService.findBlacklist(map);
            if(!ObjectUtils.isEmpty(listPageBeanBO)){
                result.setCode(Contants.RETURN_SUCCESS_CODE);
                result.setMessage("查询成功");
                result.setData(listPageBeanBO);
                return result;
            }
            result.setCode(Contants.RETURN_ERROR_CODE);
            result.setMessage("查询失败没有该记录数");
        } catch (Exception e) {
            log.error("黑名单查询异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "查询失败");
        }
        return result;
    }

    @ApiOperation(value="新增黑名单信息", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @PostMapping("/add")
    public BaseResultVo add(@RequestBody(required = false) MembershipBlacklistDTO blacklist,
                            HttpServletRequest request) {
        log.info("提交对象" + blacklist);
        BaseResultVo result = new BaseResultVo();
        try {
            if(ObjectUtils.isEmpty(blacklist.getUserMobile())){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE,"请输入手机号");
            }
            if(membershipBlacklistService.queryUserInfoCount(blacklist.getUserMobile())<=0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE,"会员信息没有该手机号的记录");
            }
            MembershipBlacklistBO membershipBlacklistBO= membershipBlacklistService.getUserInfo(blacklist.getUserMobile());
            if(StringUtils.isNotBlank(membershipBlacklistBO.getOrgId())){
                ComModel comModel = ComUtil.getUserInfo(request);
                String orgId=comModel.getOrgId();
                if(!"00".equals(orgId)&&!membershipBlacklistBO.getOrgId().equals(orgId)){
                    return new BaseResultVo(Contants.RETURN_ERROR_CODE,"会员不属于当前公司!");
                }
            }
            blacklist.setUserMobile(membershipBlacklistBO.getUserMobile());
            blacklist.setUserId(membershipBlacklistBO.getPkId());
            blacklist.setUserName(membershipBlacklistBO.getUserName());

            if(membershipBlacklistService.queryEmpty(blacklist.getUserId())){
                result.setCode(Contants.RETURN_ERROR_CODE);
                result.setMessage("添加黑名单成员失败,已存在该信息。");
                return result;
            }
            if (membershipBlacklistService.saveBlacklist(blacklist,request)) {
                result.setCode(Contants.RETURN_SUCCESS_CODE);
                result.setMessage("添加黑名单成员成功");
                return result;
            }
            result.setCode(Contants.RETURN_ERROR_CODE);
            result.setMessage("添加黑名单成员失败");
        } catch (Exception e) {
            log.error("黑名单添加异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "添加异常");
        }
        return result;
    }


    @ApiOperation(value="根据手机号码查询会员信息，帮助添加黑名单信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "userMobile",value = "手机号码",dataType = "String",required = true, paramType = "path"),
    })
    @GetMapping("/getUserInfo/{userMobile}")
    public BaseResultVo<MembershipBlacklistBO> getUserInfo(@PathVariable(value = "userMobile") String userMobile) {
        log.info("提交与手机号:" + userMobile);
        BaseResultVo<MembershipBlacklistBO> result = new BaseResultVo<>();
        try {
            MembershipBlacklistBO membershipBlacklistBO = membershipBlacklistService.getUserInfo(userMobile);
            if (ObjectUtils.isEmpty(membershipBlacklistBO)){
                result.setCode(Contants.RETURN_ERROR_CODE);
                result.setMessage("会员信息不存在");
                return result;
            }
            result.setMessage("查询成功");
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setData(membershipBlacklistBO);
            log.info("提交的会员:" + result.getData());
        } catch (Exception e) {
            log.error("黑名单查询异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "查询异常");
        }
        return result;
    }


    @ApiOperation(value="根据会员序号审核黑名单", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id",value = "会员id",dataType = "Long",required = true, paramType = "path"),
    })
    @PutMapping("/audit/{id}")
    public BaseResultVo audit(@PathVariable(value="id") Long id,
                              HttpServletRequest request) {
        log.info("会员id" + id);
        try {
            DefaultServiceRespDTO defaultServiceRespDTO = membershipBlacklistService.audit(id,request);
            return new BaseResultVo(defaultServiceRespDTO.getCode().toString(),defaultServiceRespDTO.getMessage());
        } catch (Exception e) {
            log.error("黑名单审核异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "审核异常");
        }
    }


    @ApiOperation(value="根据会员序号删除黑名单信息", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id",value = "会员id",dataType = "Long",required = true, paramType = "path"),
    })
    @PutMapping("/delete/{id}")
    public BaseResultVo delete(@PathVariable(value="id") Long id,
                               HttpServletRequest request) {
        log.info("删除id" + id);
        BaseResultVo result = new BaseResultVo();
        try {
            if (membershipBlacklistService.removeBlacklistLogic(id,request).getCode()==0) {
                return BaseResultVo.SUCCESS();
            }
            result.setCode(Contants.RETURN_ERROR_CODE);
            result.setMessage("修改黑名单成员失败");
        } catch (Exception e) {
            log.error("黑名单删除异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "删除异常");
        }
        return BaseResultVo.SUCCESS();
    }

}

