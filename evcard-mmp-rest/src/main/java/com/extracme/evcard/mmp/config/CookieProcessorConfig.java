package com.extracme.evcard.mmp.config;

import org.apache.tomcat.util.http.LegacyCookieProcessor;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CookieProcessorConfig {
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
        return (factory) -> factory.addContextCustomizers(
                /**
                 * 允许域名以 . 开头
                 * sooFilter.logout -> addCookie
                 */
                (context) -> context.setCookieProcessor(new LegacyCookieProcessor()));
    }
}
