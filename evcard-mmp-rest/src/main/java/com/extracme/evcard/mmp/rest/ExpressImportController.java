package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.ExpressQueryDTO;
import com.extracme.evcard.mmp.service.ExpressImportService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：ExpressImport
 * 类描述：会员管理,快递单号导入
 * 创建：qianhao
 * 创建时间：2018年1月16日下午2:07:24
 * 修改备注
 *
 * @version 1.0
 */
@RestController
@RequestMapping("api")
public class ExpressImportController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    ExpressImportService expressImportServiceImpl;

    /**
     * 下载快递单号导入模板
     *
     * @param
     * @return vo
     */
    @RequestMapping(value = "downloadExpressTemplate", method = RequestMethod.GET)
    public DefaultWebRespVO downloadExpressTemplate() {
        DefaultServiceRespDTO respDTO = expressImportServiceImpl.downloadExpressTemplate();
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("下载快递单号导入模板...");
        return DefaultWebRespVO.getSuccessVO(respDTO.getData());
    }

    /**
     * 快递单号批量导入
     *
     * @return vo
     */
    @RequestMapping(value = "addExpressImport", method = RequestMethod.POST)
    public DefaultWebRespVO addExpressImport(@RequestParam("file") CommonsMultipartFile file, HttpServletRequest request) {

        DefaultWebRespVO vo = new DefaultWebRespVO();
        DiskFileItem fi = (DiskFileItem) file.getFileItem();

        if (file.getSize() < 1) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("文件上传失败：文件不能为空！");
            return vo;
        }
        // 获得文件名
        String fileName = file.getOriginalFilename();
        // 获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        // 允许上传的文件类型
        String suffixList = "xls,xlsx";

        if (!suffixList.contains(suffix.trim().toLowerCase())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("文件上传失败:文件限制格式是xls,xlsx型！");
            return vo;
        }

        File fileTemp = fi.getStoreLocation();
        DefaultServiceRespDTO respDTO = expressImportServiceImpl.addExpressImport(fileTemp, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        vo.setMessage("提交成功");
        log.debug("快递单号导入...");
        return vo;
    }

    /**
     * 快递单号一览和查询
     *
     * @param name
     * @param mobilePhone
     * @param isAll
     * @param pageNum
     * @param pageSize
     * @return vo
     */
    @RequestMapping(value = "expressQuery", method = RequestMethod.GET)
    public DefaultWebRespVO expressQuery(@RequestParam(value = "name", required = false) String name,
                                         @RequestParam(value = "mobilePhone", required = false) String mobilePhone, @RequestParam(value = "expressNo", required = false) String expressNo,
                                         @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                         @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        PageBeanBO<ExpressQueryDTO> pageBeanBO = expressImportServiceImpl.expressQuery(name, mobilePhone, expressNo, isAll, pageNum, pageSize);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }
}
