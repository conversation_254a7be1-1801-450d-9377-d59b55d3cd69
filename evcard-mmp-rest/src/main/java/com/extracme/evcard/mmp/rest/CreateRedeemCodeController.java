package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.ICreateRedeemCodeService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：CreateRedeemCodeController
 * 类描述：生成兑换码控制层
 * 创建人：sunb-孙彬
 * 创建时间：2018年1月17日上午10:50:32
 * 修改备注
 *
 * @version1.0
 */
@Api(value="brandActivity", tags = "品牌合作活动发券[6]")
@RestController
@RequestMapping("api")
public class CreateRedeemCodeController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ICreateRedeemCodeService createRedeemCodeServiceImpl;

    /**
     * 新增生成兑换码活动
     *
     * @param createRedeemCodeDTO
     * @param request
     * @return
     */

    @ApiOperation(value="新增生成兑换码活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "createRedeemCodeActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addCreateRedeemCodeActivity(@RequestBody CreateRedeemCodeDTO createRedeemCodeDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO =
                    createRedeemCodeServiceImpl.addCreateRedeemCodeActivity(createRedeemCodeDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
            vo.setData(respDTO.getData());
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增生成兑换码...");
        return vo;
    }

    /**
     * 生成兑换码活动详情
     *
     * @param id 活动id
     * @return
     */
    @ApiOperation(value="生成兑换码活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "createRedeemCodeActivity/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO getCreateRedeemCodeActivityDetail(@PathVariable("id") Long id) {
        CouponBatchImportActivityDetailDTO importActivityDetailDTO =
                createRedeemCodeServiceImpl.getCreateRedeemCodeActivityDetail(id);
        log.debug("生成兑换码活动详情...");
        return DefaultWebRespVO.getSuccessVO(importActivityDetailDTO);
    }


    /**
     * 发布
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "redeemCodeActivity/publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = createRedeemCodeServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }


    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "redeemCodeActivity/delete/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = createRedeemCodeServiceImpl.delete(id,request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 生成兑换码
     *
     * @param createRedeemCodeModelDTO
     * @return
     */
    @ApiOperation(value="生成兑换码", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "createRedeemCode", method = RequestMethod.POST)
    public DefaultWebRespVO getCreateRedeemCode(@RequestBody CreateRedeemCodeModelDTO createRedeemCodeModelDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = createRedeemCodeServiceImpl.addCreateRedeemCode(createRedeemCodeModelDTO, request);
            if (respDTO.getCode() == -1) {
                log.info("生成兑换码失败：" + respDTO.getMessage());
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
            log.info("生成兑换码成功...");
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "生成兑换码成功");
        } catch (Exception e) {
            log.info("生成兑换码失败...", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("生成兑换码失败");
            return vo;
        }
    }

    /**
     * 活动所发优惠券的详情
     *
     * @param id 活动送券配置表id
     * @return
     */
    @ApiOperation(value="活动所发优惠券的详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "couponDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO getCouponDetail(@PathVariable("id") Long id) {
        DefaultServiceRespDTO respDTO = createRedeemCodeServiceImpl.getCouponDetail(id);
        if (respDTO.getCode() == -1) {
            log.info("活动详情失败：" + respDTO.getMessage());
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        RedeemCodeCouponDetailDTO redeemCodeCouponDetailDTO =
                JSON.parseObject(respDTO.getData(), RedeemCodeCouponDetailDTO.class);
        log.debug("活动所发优惠券的详情...");
        return DefaultWebRespVO.getSuccessVO(redeemCodeCouponDetailDTO);
    }

    /**
     * 导出兑换码
     *
     * @param dto
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportCreateRedeemCode", method = RequestMethod.POST)
    //@RequestMapping(value = "exportCreateRedeemCode/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO exportCreateRedeemCode(@RequestBody ExportRedeemCodeModelDTO dto, HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO2 respDTO = createRedeemCodeServiceImpl.handleExportCreateRedeemCode(dto, request);
        if (respDTO.getCode() != 0) {
            log.info("导出兑换码失败：" + respDTO.getMessage());
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("导出兑换码...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "导出兑换码成功！", respDTO.getData());
    }

    /**
     * 获取兑换码活动优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @ApiOperation(value="获取兑换码活动优惠券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCreateRedeemCodeCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getCreateRedeemCodeCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        CreateRedeemCodeCouponModelDTO redeemCodeCouponModelDto = createRedeemCodeServiceImpl.getCreateRedeemCodeCouponModelPage(paramsBO);
        log.debug("获取兑换码活动优惠券模板列表...");
        return DefaultWebRespVO.getSuccessVO(redeemCodeCouponModelDto);
    }
}
