package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.membership.core.dto.blacklist.*;
import com.extracme.evcard.membership.core.service.IChannelBlacklistService;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 渠道黑名单接口
 */
@Api(tags = "渠道黑名单管理接口")
@RestController
@RequestMapping("/api/channelBlacklist")
@Slf4j
public class ChannelBlacklistController {

    @Resource
    IChannelBlacklistService channelBlacklistServiceImpl;

    /**
     * 获取黑名单列表
     * @param pageNum
     * @param pageSize
     * @param userName
     * @param mobilePhone
     * @param certificateNum
     * @param enableStatus
     * @return
     */
    @ApiOperation(value="黑名单信息列表", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @GetMapping("/list")
    DefaultWebRespVO listChannelBlacklist(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                          @RequestParam(value = "userName", required = false) String userName,
                                          @RequestParam(value = "mobilePhone", required = false) String mobilePhone,
                                          @RequestParam(value = "certificateNum", required = false) String certificateNum,
                                          @RequestParam(value = "enableStatus", required = false) Integer enableStatus){
        ListChannelBlacklistDto dto = new ListChannelBlacklistDto();
        dto.setUserName(userName);
        dto.setMobilePhone(mobilePhone);
        dto.setCertificateNum(certificateNum);
        dto.setEnableStatus(enableStatus);
        dto.setPageNum(pageNum);
        dto.setPageSize(pageSize);
        return DefaultWebRespVO.getSuccessVO(channelBlacklistServiceImpl.listChannelBlacklist(dto));
    }

    /**
     * 插入黑名单
     * @param dto
     * @return
     */
    @ApiOperation(value="新增黑名单信息", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @PostMapping("")
    DefaultWebRespVO insert(@RequestBody UpdateChannelBlacklistDto dto){
        try {
            dto.setBlacklistSource(2);//1=手动添加，2=会员系统同步
            if (channelBlacklistServiceImpl.insert(dto) <=0){
                new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增黑名单失败");
            }
        }
        catch (Exception e) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "新增黑名单成功");
    }

    /**
     * 更新黑名单
     * @param dto
     * @return
     */
    @ApiOperation(value="修改黑名单信息", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @PutMapping("")
    DefaultWebRespVO updateById(@RequestBody UpdateChannelBlacklistDto dto, HttpServletRequest request){
        try {
            ComModel comModel = ComUtil.getUserInfo(request);
            dto.setOperateUserId(comModel.getCreateOperId());
            if (channelBlacklistServiceImpl.updateById(dto) <= 0) {
                return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "新增黑名单失败");
            }
        }
        catch (Exception e) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "修改黑名单成功");
    }

    /**
     * 根据id获取黑名单
     * @param id
     * @return
     */
    @ApiOperation(value="根据id查询渠道黑名单信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @GetMapping("/{id}")
    DefaultWebRespVO getById(@PathVariable Integer id){
        return DefaultWebRespVO.getSuccessVO(channelBlacklistServiceImpl.getById(id));
    }

    /**
     * 获取黑白名单日志（分页）
     * @param pageNum
     * @param pageSize
     * @param channelBlacklistId
     * @return
     */
    @ApiOperation(value="获取黑白名单日志", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @GetMapping("/log")
    DefaultWebRespVO listChannelBlacklistLog(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                             @RequestParam(value = "channelBlacklistId", required = true) Integer channelBlacklistId) {
        ListChannelBlacklistLogDto dto = new ListChannelBlacklistLogDto();
        dto.setChannelBlacklistId(channelBlacklistId);
        dto.setPageSize(pageSize);
        dto.setPageNum(pageNum);
        return DefaultWebRespVO.getSuccessVO(channelBlacklistServiceImpl.listChannelBlacklistLog(dto));
    }

    @ApiOperation(value="新增黑名单信息（内部使用）", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @PostMapping("/inner/insert")
    DefaultWebRespVO innerInsert(@RequestBody UpdateChannelBlacklistDto dto){
        try {
            if (channelBlacklistServiceImpl.insertOrUpdate(dto) <=0){
                new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "同步黑名单失败");
            }
        }
        catch (Exception e) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "同步黑名单成功");
    }
}
