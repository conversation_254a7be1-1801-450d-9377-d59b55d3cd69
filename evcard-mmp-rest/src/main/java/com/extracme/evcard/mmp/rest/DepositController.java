package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.config.UserAuthRequired;
import com.extracme.evcard.mmp.dto.deposit.*;
import com.extracme.evcard.mmp.service.IDepositService;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("api")
public class DepositController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    IDepositService depositService;


    /**
     * 会员系统-个人中心，获取押金信息
     *
     * @param dto
     * @param request
     * @return
     */
    //
    @RequestMapping(value = "getMemberDepositInfos", method = {RequestMethod.GET, RequestMethod.POST})
    public DefaultWebRespVO getMemberDepositInfos(@RequestBody GetMemberDepositInfosDTO dto, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            GetMemberDepositInfosData memberDepositInfos = depositService.getMemberDepositInfos(dto);
            return DefaultWebRespVO.getSuccessVO(memberDepositInfos);
        } catch (BusinessException e) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        return vo;
    }

    /**
     * 会员系统-个人中心，获取押金日志操作记录
     *
     *
     * @param dto
     * @param request
     * @return
     */
    @UserAuthRequired(resKey = "GGs4PTDBTtKX-uFg74cDnZ")
    @RequestMapping(value = "getMemberDepositLogs", method = {RequestMethod.GET, RequestMethod.POST})
    public DefaultWebRespVO getMemberDepositLogs(@RequestBody GetMemberDepositLogsDTO dto, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            GetMemberDepositLogsData memberDepositLogs = depositService.getMemberDepositLogs(dto);
            return DefaultWebRespVO.getSuccessVO(memberDepositLogs);
        } catch (BusinessException e) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        return vo;
    }

    /**
     * 会员系统-个人中心，押金急速退押
     *
     *
     * @param dto
     * @param request
     * @return
     */
    @UserAuthRequired(resKey = "GGs4PTDBTtKX-uFg74cDnX")
    @RequestMapping(value = "rapidRefund", method = {RequestMethod.GET, RequestMethod.POST})
    public DefaultWebRespVO rapidRefund(@RequestBody RapidRefundDTO dto, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            return DefaultWebRespVO.getSuccessVO(depositService.rapidRefund(dto,request));
        } catch (BusinessException e) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        return vo;
    }

    /**
     * 会员系统-个人中心，查询押金急速退押列表
     *
     *
     * @param dto
     * @param request
     * @return
     */
    @RequestMapping(value = "queryRapidRefundRecord", method = {RequestMethod.GET, RequestMethod.POST})
    public DefaultWebRespVO queryRapidRefundRecord(@RequestBody QueryRapidRefundRecordDTO dto, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            QueryRapidRefundRecordData queryRapidRefundRecordData = depositService.queryRapidRefundRecord(dto);
            return DefaultWebRespVO.getSuccessVO(queryRapidRefundRecordData);
        } catch (BusinessException e) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        return vo;
    }


}
