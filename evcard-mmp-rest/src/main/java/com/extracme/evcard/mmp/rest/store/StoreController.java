package com.extracme.evcard.mmp.rest.store;

/**
 * 门店接口
 */
import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.dto.store.OrgStoreQueryInput;
import com.extracme.evcard.mmp.service.store.StoreControlService;
import com.extracme.evcard.rpc.vipcard.dto.StoreBasicDto;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("api/store")
public class StoreController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private StoreControlService storeControlService;

    @RequestMapping(value = "getStoreListByOrgCity", method = RequestMethod.POST)
    public DefaultWebRespVO getStoreListByOrgCity(@RequestBody OrgStoreQueryInput queryDto,
                                        HttpServletRequest request) {
        try {
            List<StoreBasicDto> result = storeControlService.getStoreListByOrgCity(queryDto);
            return DefaultWebRespVO.getSuccessVO(result);
        }catch (Exception e) {
            logger.error("获取门店列表失败, queryDto=" + JSON.toJSONString(queryDto), e);
        }
        return new DefaultWebRespVO("-1", "查询门店列表失败");
    }


    @RequestMapping(value = "goodsVehicleModel", method = RequestMethod.GET)
    public DefaultWebRespVO queryVehicleModel(HttpServletRequest request) {
        List<VehicleModel> list = storeControlService.getGoodsVehicleModelList();
        return DefaultWebRespVO.getSuccessVO(list);
    }

}
