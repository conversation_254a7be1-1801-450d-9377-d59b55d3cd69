package com.extracme.evcard.mmp.common.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.rest.MdRestClient;
import com.extracme.evcard.mmp.rest.entity.QueryVehicleBrandInfoNoLoginRequest;
import com.extracme.evcard.mmp.rest.entity.QueryVehicleBrandInfoNoLoginResponse;
import com.extracme.evcard.mmp.rest.entity.QueryVehicleLevelListRequest;
import com.extracme.evcard.mmp.rest.entity.QueryVehicleLevelListResponse;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("api/vehicle")
public class VehicleController {
    @Autowired
    private MdRestClient mdRestClient;

    @RequestMapping(value = "queryVehicleLevelList", method = {RequestMethod.POST})
    public DefaultWebRespVO queryVehicleLevelList(HttpServletRequest request) {
        QueryVehicleLevelListResponse queryVehicleLevelListResponse = mdRestClient.queryVehicleLevelList(new QueryVehicleLevelListRequest());
        if (queryVehicleLevelListResponse == null) {
            return new DefaultWebRespVO("-1", "查询失败");
        }
        if (queryVehicleLevelListResponse.getCode() == 0) {
            return DefaultWebRespVO.getSuccessVO(queryVehicleLevelListResponse.getData());
        } else {
            log.error("queryVehicleLevelList 失败，queryVehicleLevelListResponse={}", JSON.toJSONString(queryVehicleLevelListResponse));
            return new DefaultWebRespVO("-1", queryVehicleLevelListResponse.getMessage());
        }
    }


    @RequestMapping(value = "queryVehicleBrandInfo", method = {RequestMethod.POST})
    public DefaultWebRespVO queryVehicleBrandInfoNoLogin(HttpServletRequest request) {
        QueryVehicleBrandInfoNoLoginResponse queryVehicleBrandInfoNoLoginResponse = mdRestClient.queryVehicleBrandInfoNoLogin(new QueryVehicleBrandInfoNoLoginRequest());
        if (queryVehicleBrandInfoNoLoginResponse == null) {
            return new DefaultWebRespVO("-1", "查询失败");
        }
        if (queryVehicleBrandInfoNoLoginResponse.getCode() == 0) {
            return DefaultWebRespVO.getSuccessVO(queryVehicleBrandInfoNoLoginResponse.getData());
        } else {
            log.error("queryVehicleBrandInfoNoLogin 失败，queryVehicleBrandInfoNoLoginResponse={}", JSON.toJSONString(queryVehicleBrandInfoNoLoginResponse));
            return new DefaultWebRespVO("-1", queryVehicleBrandInfoNoLoginResponse.getMessage());
        }
    }

}
