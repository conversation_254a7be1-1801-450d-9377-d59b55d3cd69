package com.extracme.evcard.mmp.tcs.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.tcs.INoviceTaskConfigService;
import com.extracme.evcard.mmp.service.tcs.TaskCouponViewPageDTO;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.evcard.mmp.vo.NoviceTaskConfigVO;
import com.extracme.evcard.tcs.provider.api.bo.TaskCouponModelQueryBO;
import com.extracme.evcard.tcs.provider.api.dto.task.TaskCouponModelDTO;
import com.extracme.evcard.tcs.provider.api.dto.task.TaskDetailDTO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2019/11/18
 */
@Api(value="noviceTask", tags = "新手任务")
@RestController
@RequestMapping("api/noviceTask")
public class NoviceTaskController {
    private static final Logger logger = LoggerFactory.getLogger(DailyOrderTaskController.class);

    @Resource
    INoviceTaskConfigService noviceTaskConfigService;

    /**
     * 活动详情获取
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public BaseResultVo queryDetail(@PathVariable("id") Long id, HttpServletRequest request){
        TaskDetailDTO taskDetailDTO = noviceTaskConfigService.queryDetail(id);
        if (null == taskDetailDTO) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return BaseResultVo.getSuccessVO(taskDetailDTO);
    }

    /**
     * 券模板列表
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public BaseResultVo getCouponModelPage(@RequestBody TaskCouponModelQueryBO paramsBO,
                                               HttpServletRequest request) {
        TaskCouponViewPageDTO couponModelPage = noviceTaskConfigService.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        return BaseResultVo.getSuccessVO(couponModelPage);
    }

    /**
     * 活动新增
     * @param taskConfigDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public BaseResultVo add(@RequestParam(value = "bannerImageFile", required = false) CommonsMultipartFile bannerFile,
                                @RequestParam(value = "bgImageFile", required = false) CommonsMultipartFile taskImgFile,
                                NoviceTaskConfigVO taskConfigDTO,
                                HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            //处理优惠券模板
            if(StringUtils.isNotBlank(taskConfigDTO.getCouponModelsVal())) {
                try{
                    List<TaskCouponModelDTO> couponModels = JSON.parseArray(taskConfigDTO.getCouponModelsVal(), TaskCouponModelDTO.class);
                    taskConfigDTO.setCouponModels(couponModels);
                }catch (Exception ex){
                    logger.error("新增活动异常, couponModelsVal转换失败", ex);
                    return new BaseResultVo(Contants.RETURN_ERROR_CODE, "模板数据不正确");
                }
            }
            //处理图片信息
            taskConfigDTO.setBannerFile(ComUtil.fromMultipartFile(bannerFile, "【新手任务专区banner】"));
            taskConfigDTO.setTaskImgFile(ComUtil.fromMultipartFile(taskImgFile, "【活动背景图片】"));

            DefaultServiceRespDTO respDTO = noviceTaskConfigService.add(taskConfigDTO, request);
            if (respDTO.getCode() != 0) {
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("新增活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 活动更新
     * @param taskConfigDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    public BaseResultVo update(@RequestParam(value = "bannerImageFile", required = false) CommonsMultipartFile bannerFile,
                            @RequestParam(value = "bgImageFile", required = false) CommonsMultipartFile taskImgFile,
                            NoviceTaskConfigVO taskConfigDTO, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            //处理优惠券模板
            if(StringUtils.isNotBlank(taskConfigDTO.getCouponModelsVal())) {
                try{
                    List<TaskCouponModelDTO> couponModels = JSON.parseArray(taskConfigDTO.getCouponModelsVal(), TaskCouponModelDTO.class);
                    taskConfigDTO.setCouponModels(couponModels);
                    List<Long> couponSeqs = JSON.parseArray(taskConfigDTO.getUpdateCouponSeqListVal(), Long.class);
                    taskConfigDTO.setUpdateCouponSeqList(couponSeqs);
                }catch (Exception ex){
                    logger.error("新增活动异常, couponModelsVal转换失败", ex);
                    return new BaseResultVo(Contants.RETURN_ERROR_CODE, "模板数据不正确");
                }
            }
            //处理图片信息
            taskConfigDTO.setBannerFile(ComUtil.fromMultipartFile(bannerFile, "【新手任务专区banner】"));
            taskConfigDTO.setTaskImgFile(ComUtil.fromMultipartFile(taskImgFile, "【活动背景图片】"));
            DefaultServiceRespDTO respDTO = noviceTaskConfigService.update(taskConfigDTO, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("更新活动异常，id=" + taskConfigDTO.getId(), e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public BaseResultVo delete(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = noviceTaskConfigService.delete(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动发布
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public BaseResultVo publish(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = noviceTaskConfigService.publish(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "发布活动失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动启动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    public BaseResultVo start(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = noviceTaskConfigService.start(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("启动活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }

    /**
     * 活动停止
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    public BaseResultVo stop(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = noviceTaskConfigService.stop(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("停止活动异常，id=" + id, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }
}
