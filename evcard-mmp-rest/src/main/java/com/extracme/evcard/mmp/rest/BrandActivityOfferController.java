package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.BrandActivityOfferCouponDTO;
import com.extracme.evcard.mmp.service.IBrandActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/7/6
 * \* Time: 13:46
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 发放品牌活动优惠券
 * \
 */
@RestController
@RequestMapping("inner")
public class BrandActivityOfferController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IBrandActivityService brandActivityServiceImpl;


    /**
     * 发放品牌活动优惠券 evcard-rest调用
     *
     * @param brandActivityOfferCouponDTO
     * @return
     */
    @RequestMapping(value = "offerBrandActivityCoupon", method = RequestMethod.POST)
    public DefaultWebRespVO offerBrandActivityCoupon(BrandActivityOfferCouponDTO brandActivityOfferCouponDTO) {
        log.info("----------evcard-rest调用已进入offerBrandActivityCoupon------参数brandActivityOfferCouponDTO: " + JSON.toJSONString(brandActivityOfferCouponDTO));
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.offerBrandActivityCoupon(brandActivityOfferCouponDTO);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage());
        }
        log.info("发放品牌活动优惠券...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券领取成功");
    }

    /**
     * 查询品牌活动 evcard-rest调用
     *
     * @param brandActivityOfferCouponDTO
     * @return
     */
    @RequestMapping(value = "getBrandActivity", method = RequestMethod.POST)
    public DefaultWebRespVO getBrandActivity(BrandActivityOfferCouponDTO brandActivityOfferCouponDTO) {
        log.info("----------evcard-rest调用已进入getBrandActivity------参数brandActivityOfferCouponDTO: " + JSON.toJSONString(brandActivityOfferCouponDTO));
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.getBrandActivity(brandActivityOfferCouponDTO);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage(), respDTO.getData());
        }
        log.info("查询品牌活动...，返回：" + JSON.toJSONString(respDTO));
        Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
        return DefaultWebRespVO.getSuccessVO(map);
    }
}