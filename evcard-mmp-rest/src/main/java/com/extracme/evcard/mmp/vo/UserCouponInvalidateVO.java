package com.extracme.evcard.mmp.vo;

import java.io.Serializable;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/7/13
 * \* Time: 10:46
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 作废用户优惠券
 * \
 */
public class UserCouponInvalidateVO implements Serializable{

    private static final long serialVersionUID = -3312432609638914204L;

    /**
     * 用户优惠券id
     */
    private List<Long> couponSeqs;

    /**
     * 作废原因
     */
    private String reason;

    /**
     * 操作类别： 1系统作废 2退款作废
     */
    private Integer operateType;

    public List<Long> getCouponSeqs() {
        return couponSeqs;
    }

    public void setCouponSeqs(List<Long> couponSeqs) {
        this.couponSeqs = couponSeqs;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }
}