package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.EParkActivityDTO;
import com.extracme.evcard.mmp.dto.EParkActivityDetailDTO;
import com.extracme.evcard.mmp.service.EParkActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(value="epark", tags = "随E停[10]")
@RestController
@RequestMapping("api/epark")
public class EParkActivityController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Resource
	private EParkActivityService eParkActivityServiceImpl;

	/**
	 * 新增随E停活动配置信息
	 * 
	 * @param eParkActivityFullDTO
	 * @param request
	 * @return
	 */
	@ApiOperation(value="新增随E停活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
	@RequestMapping(value = "addEParkActivity", method = RequestMethod.POST)
	public DefaultWebRespVO addInviteActivity(@RequestBody EParkActivityDTO eParkActivityFullDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = eParkActivityServiceImpl.addEParkActivity(eParkActivityFullDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			e.printStackTrace();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage(e.getMessage());
			return vo;
		}
		vo.setMessage("提交成功");
		log.debug("新增随E停活动配置信息...");
		return vo;
	}

	/**
	 * 修改随E停活动信息
	 * 
	 * @param eParkActivityFullDTO
	 * @param request
	 * @return
	 */
	@ApiOperation(value="修改随E停活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
	@RequestMapping(value = "updateEParkActivity", method = RequestMethod.POST)
	public DefaultWebRespVO updateBrandActivity(@RequestBody EParkActivityDTO eParkActivityFullDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		DefaultServiceRespDTO respDTO = eParkActivityServiceImpl.updateEParkActivity(eParkActivityFullDTO, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		vo.setMessage("提交成功");
		log.debug("修改随E停活动信息...");
		return vo;
	}

	/**
	 * 获取随E停活动详情信息
	 */
	@ApiOperation(value="获取随E停活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
	@RequestMapping(value = "eparkActivityDetail/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO eparkActivityDetail(@PathVariable("id") Long id) {
		EParkActivityDetailDTO eparkActivityDetail = eParkActivityServiceImpl.eparkActivityDetail(id);
		if (null == eparkActivityDetail) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
		}
		log.debug("获取随E停活动详情信息...");
		return DefaultWebRespVO.getSuccessVO(eparkActivityDetail);
	}

	/**
	 * 立即开始随E停活动
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@ApiOperation(value="立即开始随E停活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
	@RequestMapping(value = "immediateStartEParkActivity/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO immediateStartEParkActivity(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = eParkActivityServiceImpl.immediateStartEParkActivity(id, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("立即开始随E停活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
	}

	/**
	 * 立即开始渠道注册奖励活动
	 *
	 * @param id      活动id
	 * @param request
	 * @return
	 */
	@ApiOperation(value="发布随E停活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
	@RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = eParkActivityServiceImpl.publish(id, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
	}

	/**
	 * 停止随E停活动
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@ApiOperation(value="停止随E停活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
	@RequestMapping(value = "suspendEParkActivity/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO suspendEParkActivity(@PathVariable("id") Long id, HttpServletRequest request) {
		ComModel comModel = ComUtil.getUserInfo(request);
		String createOperName = comModel.getCreateOperName();
		Long createOperId = comModel.getCreateOperId();
		String operatorContent = "停止";
		DefaultServiceRespDTO respDTO = eParkActivityServiceImpl.suspendEParkActivity(id, createOperName, createOperId,
				operatorContent);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("停止随E停活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
	}

	/**
	 * 删除随E停活动
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@ApiOperation(value="删除随E停活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
	@ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
	@RequestMapping(value = "deleteEParkActivity/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO deleteEParkActivity(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = eParkActivityServiceImpl.deleteEParkActivity(id, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("删除随E停活动");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
	}

}
