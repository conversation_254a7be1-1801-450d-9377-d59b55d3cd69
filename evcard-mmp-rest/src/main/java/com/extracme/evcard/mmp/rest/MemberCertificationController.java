package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.MemberAuthLogDTO;
import com.extracme.evcard.membership.core.dto.UpdateLicenceReviewDto;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.service.IMemberCertificationService;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dao.MembershipInfoMapper;
import com.extracme.evcard.mmp.dao.MmpUserMapper;
import com.extracme.evcard.mmp.dto.LoginUserInfo;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("api/memberAuth")
public class MemberCertificationController {
    @Resource
    MembershipInfoMapper membershipInfoMapper;
    @Resource
    MmpUserMapper mmpUserMapper;
    @Resource
    private IMemberCertificationService memberCertificationService;

    @RequestMapping(value = "passAuditIdentity", method = RequestMethod.POST)
    public DefaultWebRespVO passAuditIdentity(@RequestBody AuditIdCardInput input, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String authId = membershipInfoMapper.getAuthIdByMid(input.getMid());
            if (StringUtils.isNotBlank(authId)) {
                //权限验证
                ComModel comModel = ComUtil.getUserInfo(request);
                DefaultServiceRespDTO dto = ComUtil.addAuthority(authId, comModel, membershipInfoMapper);
                if (!StringUtils.equals(dto.getCode().toString(), Contants.RETURN_SUCCESS_CODE)) {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }

                dto = memberCertificationService.passAuditIdentity(input);
                if (dto.getCode() == 0) {
                    return DefaultWebRespVO.SUCCESS;
                } else {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }
            }
        } catch (Exception e) {
            log.error("会员身份证人工审核通过异常<{}>,入参<{}>", e, JSON.toJSONString(input));
        }
        vo.setCode(Contants.RETURN_ERROR_CODE);
        vo.setMessage("请求失败，请稍后重试");
        return vo;
    }

    @RequestMapping(value = "notPassAuditIdentity", method = RequestMethod.POST)
    public DefaultWebRespVO notPassAuditIdentity(@RequestBody AuditIdCardInput input, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String authId = membershipInfoMapper.getAuthIdByMid(input.getMid());
            if (StringUtils.isNotBlank(authId)) {
                //权限验证
                ComModel comModel = ComUtil.getUserInfo(request);
                DefaultServiceRespDTO dto = ComUtil.addAuthority(authId, comModel, membershipInfoMapper);
                if (!StringUtils.equals(dto.getCode().toString(), Contants.RETURN_SUCCESS_CODE)) {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }

                dto = memberCertificationService.notPassAuditIdentity(input);
                if (dto.getCode() == 0) {
                    return DefaultWebRespVO.SUCCESS;
                } else {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }
            }
        } catch (Exception e) {
            log.error("会员身份证人工审核不通过异常<{}>,入参<{}>", e, JSON.toJSONString(input));
        }
        vo.setCode(Contants.RETURN_ERROR_CODE);
        vo.setMessage("请求失败，请稍后重试");
        return vo;
    }

    @RequestMapping(value = "reAuditIdentity", method = RequestMethod.POST)
    public DefaultWebRespVO reAuditIdentity(@RequestBody AuditIdCardInput input, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String authId = membershipInfoMapper.getAuthIdByMid(input.getMid());
            if (StringUtils.isNotBlank(authId)) {
                //权限验证
                ComModel comModel = ComUtil.getUserInfo(request);
                DefaultServiceRespDTO dto = ComUtil.addAuthority(authId, comModel, membershipInfoMapper);
                if (!StringUtils.equals(dto.getCode().toString(), Contants.RETURN_SUCCESS_CODE)) {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }

                dto = memberCertificationService.reAuditIdentity(input);
                if (dto.getCode() == 0) {
                    return DefaultWebRespVO.SUCCESS;
                } else {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }
            }
        } catch (Exception e) {
            log.error("会员身份证人工审核异常<{}>,入参<{}>", e, JSON.toJSONString(input));
        }
        vo.setCode(Contants.RETURN_ERROR_CODE);
        vo.setMessage("请求失败，请稍后重试");
        return vo;
    }

    @RequestMapping(value = "updateDrivingLicenceReviewStatus", method = RequestMethod.POST)
    public DefaultWebRespVO updateDrivingLicenceReviewStatus(@RequestBody UpdateLicenceReviewDto updateAuthIdItemsDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String authId = membershipInfoMapper.getAuthIdByMid(updateAuthIdItemsDTO.getMid());
            if (StringUtils.isNotBlank(authId)) {
                //权限验证
                ComModel comModel = ComUtil.getUserInfo(request);
                DefaultServiceRespDTO dto = ComUtil.addAuthority(authId, comModel, membershipInfoMapper);
                if (!StringUtils.equals(dto.getCode().toString(), Contants.RETURN_SUCCESS_CODE)) {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }

                BaseResponse baseResponse = memberCertificationService.updateDrivingLicenceReviewStatus(updateAuthIdItemsDTO);
                if (baseResponse.getCode() == 0) {
                    return DefaultWebRespVO.SUCCESS;
                } else {
                    vo.setCode(String.valueOf(baseResponse.getCode()));
                    vo.setMessage(baseResponse.getMessage());
                    return vo;
                }
            }
        } catch (Exception e) {
            log.error("会员驾照人工审核异常<{}>,入参<{}>", e, JSON.toJSONString(updateAuthIdItemsDTO));
        }
        vo.setCode(Contants.RETURN_ERROR_CODE);
        vo.setMessage("请求失败，请稍后重试");
        return vo;
    }


    @RequestMapping(value = "queryAuditLogs", method = RequestMethod.GET)
    public DefaultWebRespVO queryAuditLogs(@RequestParam(value = "type") Integer type,
                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                           @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                           @RequestParam(value = "mid") String mid, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String authId = membershipInfoMapper.getAuthIdByMid(mid);
            if (StringUtils.isNotBlank(authId)) {
                //权限验证
                ComModel comModel = ComUtil.getUserInfo(request);
                DefaultServiceRespDTO dto = ComUtil.addAuthority(authId, comModel, membershipInfoMapper);
                if (!StringUtils.equals(dto.getCode().toString(), Contants.RETURN_SUCCESS_CODE)) {
                    vo.setCode(String.valueOf(dto.getCode()));
                    vo.setMessage(dto.getMessage());
                    return vo;
                }

                PageBeanDto<MemberAuthLogDTO> dtoList = memberCertificationService.queryAuditLogs(type, pageSize, pageNumber, mid);
                if (dtoList.getCode() == 0) {
                    try {
                        List<MemberAuthLogDTO> list = dtoList.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<Long> collect = list.stream().map(MemberAuthLogDTO::getOperatorId).collect(Collectors.toList());
                            Long[] ids = collect.toArray(new Long[]{});
                            List<LoginUserInfo> loginUserInfos = mmpUserMapper.selectOperatorByIds(ids);
                            Map<Long, String> map = new HashMap<>();
                            loginUserInfos.stream().forEach(m -> {
                                map.put(m.getId(), m.getOrgName());
                            });

                            list.stream().forEach(m -> {
                                Long operatorId = m.getOperatorId();
                                String name = map.get(operatorId);
                                m.setOperatorOrgName(name);
                            });
                        }
                    } catch (Exception e) {
                    }
                    PageBeanBO<MemberAuthLogDTO> pageBeanBO = new PageBeanBO<>();
                    pageBeanBO.setList(dtoList.getList());
                    PageBO pageBO = new PageBO(dtoList.getPage().getPageNo(),dtoList.getPage().getPageSize());
                    pageBO.setTotal(dtoList.getPage().getCount());
                    pageBeanBO.setPage(pageBO);
                    return DefaultWebRespVO.getSuccessVO(pageBeanBO);
                } else {
                    vo.setCode(String.valueOf(dtoList.getCode()));
                    vo.setMessage(dtoList.getMessage());
                    return vo;
                }
            }
        } catch (Exception e) {
            log.error("查询认证接口异常<{}>,入参mid<{}>", e, mid);
        }
        vo.setCode(Contants.RETURN_ERROR_CODE);
        vo.setMessage("请求失败，请稍后重试");
        return vo;
    }


}
