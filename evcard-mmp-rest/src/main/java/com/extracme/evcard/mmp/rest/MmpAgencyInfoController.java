package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.QueryMmpAgencyBO;
import com.extracme.evcard.mmp.dto.MmpAgencyInfoDTO;
import com.extracme.evcard.mmp.service.MmpAgencyInfoServiceImpl;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping(value = "api")
public class MmpAgencyInfoController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    MmpAgencyInfoServiceImpl mmpAgencyInfoService;

    /**
     *
     * @param queryMmpAgencyBO
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "mmpAgency/query", method = RequestMethod.POST)
    public DefaultWebRespVO queryAgencyInfo(@RequestBody QueryMmpAgencyBO queryMmpAgencyBO, HttpServletRequest request, HttpServletResponse response) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        String socialCreditCode = queryMmpAgencyBO.getSocialCreditCode();
        try {
            if(StringUtils.isBlank(socialCreditCode)) {
                vo.setMessage("请输入统一社会信用代码。");
                vo.setCode("-1");
                return vo;
            }
            if (socialCreditCode.length() > 18) {
                vo.setMessage("统一社会信用代码长度必须小于等于18。");
                vo.setCode("-1");
                return vo;
            }

            MmpAgencyInfoDTO mmpAgencyInfoDTO = mmpAgencyInfoService.queryAgencyInfo(socialCreditCode);
            if (null != mmpAgencyInfoDTO) {
                return DefaultWebRespVO.getSuccessVO(mmpAgencyInfoDTO);
            } else {
                vo.setCode("-1");
                vo.setMessage("系统中还未创建该统一社会信用代码的关联企业，请输入企业名称创建。");
            }
        } catch (Exception e) {
            logger.error("查询失败" + socialCreditCode, e);
            vo.setCode("-1");
            vo.setMessage("查询失败.");
        }
        return vo;
    }
}

