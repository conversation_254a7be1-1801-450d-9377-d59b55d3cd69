package com.extracme.evcard.mmp.rest;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IAppLayerActivityService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.ILayerActivityService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

/**
 * \* Created with IntelliJ IDEA. \* User: Elin \* Date: 2018/4/2 \* Time: 15:14
 * \* To change this template use File | Settings | File Templates. \*
 * Description: 弹层活动 \
 */
@RestController
public class LayerActivityController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    ILayerActivityService layerActivityServiceImpl;
    @Autowired
    IAppLayerActivityService appLayerActivityServiceImpl;

    /**
     * 活动创建
     * @param imageFile 弹窗图片
     * @param shareImageFile 图片链接
     * @param shareIconFile 分享链接图标
     * @param layerActivityFullDTO 活动配置属性
     * @param request
     * @return
     */
    @RequestMapping(value = "api/layer/add", method = RequestMethod.POST)
    public DefaultWebRespVO addLayerActivity(
            @RequestParam(value = "imageFile") CommonsMultipartFile imageFile,
            @RequestParam(value = "shareImageFile", required = false) CommonsMultipartFile shareImageFile,
            @RequestParam(value = "shareIconFile", required = false) CommonsMultipartFile shareIconFile,
            LayerActivityFullDTO layerActivityFullDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            //活动配置信息
            layerActivityFullDTO.setImage(InputFileDTO.fromMultipartFile(imageFile, "【弹窗图片】"));
            layerActivityFullDTO.setShareImage(InputFileDTO.fromMultipartFile(shareImageFile, "【图片链接】"));
            layerActivityFullDTO.setShareIcon(InputFileDTO.fromMultipartFile(shareIconFile, "【分享链接图标】"));
            //操作人信息
            ComModel comModel = ComUtil.getUserInfo(request);
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(comModel.getCreateOperId());
            operatorDTO.setOperatorName(comModel.getCreateOperName());
            //活动创建
            DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.add(layerActivityFullDTO, operatorDTO);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            logger.error("新增活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     *
     * @param imageFile 弹窗图片
     * @param shareImageFile 图片链接
     * @param shareIconFile 分享链接图标
     * @param layerActivityFullDTO 活动配置属性
     * @param request
     * @return
     */
    @RequestMapping(value = "api/layer/update", method = RequestMethod.POST)
    public DefaultWebRespVO updateLayerActivity(
            @RequestParam(value = "imageFile", required = false) CommonsMultipartFile imageFile,
            @RequestParam(value = "shareImageFile", required = false) CommonsMultipartFile shareImageFile,
            @RequestParam(value = "shareIconFile", required = false) CommonsMultipartFile shareIconFile,
            LayerActivityFullDTO layerActivityFullDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            //活动配置信息
            layerActivityFullDTO.setImage(InputFileDTO.fromMultipartFile(imageFile, "【弹窗图片】"));
            layerActivityFullDTO.setShareImage(InputFileDTO.fromMultipartFile(shareImageFile, "【图片链接】"));
            layerActivityFullDTO.setShareIcon(InputFileDTO.fromMultipartFile(shareIconFile, "【分享链接图标】"));
            //操作人信息
            ComModel comModel = ComUtil.getUserInfo(request);
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(comModel.getCreateOperId());
            operatorDTO.setOperatorName(comModel.getCreateOperName());
            //活动更新
            DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.update(layerActivityFullDTO, operatorDTO);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("更新活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    @RequestMapping(value = "api/layer/query/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDetail(@PathVariable("id") Long id) {
        LayerActivityFullDetailDTO layerActivityDetailDTO = appLayerActivityServiceImpl.queryDetails(id);
        return DefaultWebRespVO.getSuccessVO(layerActivityDetailDTO);
    }

    @RequestMapping(value = "api/layer/layerActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO layerActivityDetail(@PathVariable("id") Long id) {
        LayerActivityDetailDTO layerActivityDetailDTO = layerActivityServiceImpl.layerActivityDetail(id);
        return DefaultWebRespVO.getSuccessVO(layerActivityDetailDTO);
    }

    /**
     * 发布弹窗活动
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "api/layer/publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publishActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        //操作人信息
        ComModel comModel = ComUtil.getUserInfo(request);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.publish(id, operatorDTO);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("发布弹窗活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }

    /**
     * 立即开始活动弹层活动
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "api/layer/start/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartLayerActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        //操作人信息
        ComModel comModel = ComUtil.getUserInfo(request);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.start(id, operatorDTO);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("立即开始活动弹层活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 停止活动弹层活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/layer/stop/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendLayerActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        //操作人信息
        ComModel comModel = ComUtil.getUserInfo(request);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.stop(id, operatorDTO);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("停止活动弹层活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }

    /**
     * 删除活动弹层活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/layer/delete/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteLayerActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        //操作人信息
        ComModel comModel = ComUtil.getUserInfo(request);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.delete(id, operatorDTO);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("删除活动弹层活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 暂停活动弹层活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/layer/pause/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO pauseLayerActivity(@PathVariable("id") Long id,
                                               HttpServletRequest request) {
        //操作人信息
        ComModel comModel = ComUtil.getUserInfo(request);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.pause(id, operatorDTO);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("暂停活动弹层活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已暂停");
    }

    /**
     * 恢复活动弹层活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/layer/resume/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO resumeActivity(@PathVariable("id") Long id,
                                           HttpServletRequest request) {
        //操作人信息
        ComModel comModel = ComUtil.getUserInfo(request);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.resume(id, operatorDTO);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("恢复活动弹层活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已恢复");
    }

    /**
     * 弹窗活动列表
     *
     * @param pageNum        页码
     * @param pageSize       每页显示条数
     * @param isAll          1-统计总数
     * @param name           活动名称
     * @param activitySeq    活动ID
     * @param activityStatus 活动状态
     * @param orgId          所属公司
     * @param startDate      活动开始时间
     * @param endDate        活动结束时间
     * @param optUser        操作人
     * @param city           城市
     * @return vo  返回值
     */
    @RequestMapping(value = "api/layer/list", method = RequestMethod.GET)
    public DefaultWebRespVO queryLayerActivityList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                   @RequestParam(value = "countByStatus", defaultValue = "0") Integer countByStatus,
                                                   @RequestParam(value = "name", required = false) String name,
                                                   @RequestParam(value = "activitySeq", required = false) String activitySeq,
                                                   @RequestParam(value = "activityStatus", required = false) String activityStatus,
                                                   @RequestParam(value = "orgId", required = false) String orgId,
                                                   @RequestParam(value = "startDate", required = false) String startDate,
                                                   @RequestParam(value = "endDate", required = false) String endDate,
                                                   @RequestParam(value = "optUser", required = false) String optUser,
                                                   @RequestParam(value = "city", required = false) String city,
                                                   @RequestParam(value = "number", required = false) Integer number,
                                                   @RequestParam(value = "serviceType", required = false) Integer serviceType,
                                                   @RequestParam(value = "sortBy", defaultValue = "0") Integer sortBy) {

        LayerListDataDTO pageBeanBO = appLayerActivityServiceImpl.query(
                pageNum, pageSize, isAll, countByStatus, name, activitySeq,
                activityStatus, orgId, startDate, endDate, optUser, city, number, serviceType, sortBy);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 配置情况查询：运营公司指定日(若干)/指定时(最多1个)的弹窗活动列表
     *
     * @param orgId          所属公司
     * @param date           日期： yyyyMMdd 或者yyyyMMddHH
     * @return vo
     * @remark 不包含已停止和已删除的活动, 最多24个，按开始时间升序
     */
    @RequestMapping(value = "api/layer/dailyList", method = RequestMethod.GET)
    public DefaultWebRespVO dailyList(@RequestParam(value = "orgId", required = false) String orgId,
                                      @RequestParam(value = "date") String date,
                                      @RequestParam(value = "number") Integer number) {
        //TODO 参数检查
        if(StringUtils.isBlank(date) || (date.length() != 8 && date.length() != 10)) {
            return new DefaultWebRespVO("-1", "参数不合法");
        }
        List<ActivityCenterPageDTO> pageBeanBO = appLayerActivityServiceImpl.dailyList(orgId, date, number);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 配置情况查询：运营公司指定日/月的弹窗数量统计
     *
     * @param orgId          所属公司
     * @param date           日期： yyyyMMdd 或者yyyyMM
     * @return vo
     * @remark 不包含已停止和已删除的活动, 按时/按日统计
     */
    @RequestMapping(value = "api/layer/dailyQuery", method = RequestMethod.GET)
    public DefaultWebRespVO dailyQuery(@RequestParam(value = "orgId", required = false) String orgId,
                                       @RequestParam(value = "date") String date) {
        //TODO 参数检查
        if(StringUtils.isBlank(date) || (date.length() != 8 && date.length() != 6)) {
            return new DefaultWebRespVO("-1", "参数不合法");
        }
        Map<String, int[]> countResult = appLayerActivityServiceImpl.dailyQuery(orgId, date);
        return DefaultWebRespVO.getSuccessVO(countResult);
    }

    /**
     * 置顶活动弹层活动
     *
     * @param id      活动id
     * @param layerId 子表中的id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "api/layer/updateTopStatus/{id}/{layerId}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateTopStatus(@PathVariable("id") Long id,
                                            @PathVariable("layerId") Long layerId,
                                            HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = layerActivityServiceImpl.updateTopStatus(id, layerId, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        logger.debug("置顶活动弹层活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "已置顶");
    }

    /**
     * 查询所有的活动名称，模糊搜索
     *
     * @return
     */
    @RequestMapping(value = "api/layer/queryAllLayerActivityName", method = RequestMethod.GET)
    public DefaultWebRespVO queryAllLayerActivityName() {
        List<String> nameList = layerActivityServiceImpl.queryAllLayerActivityName();
        return DefaultWebRespVO.getSuccessVO(nameList);
    }

    /**
     * 广告页数据列表
     *
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @param activitySeq
     * @return
     */
    @RequestMapping(value = "api/layer/getLayerStatistics", method = RequestMethod.GET)
    public DefaultWebRespVO getLayerStatistics(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                               @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                               @RequestParam(value = "activitySeq", required = false) String activitySeq) {

        PageBeanBO<MmpLayerDataPageDTO> pageBeanBO = layerActivityServiceImpl.getLayerStatistics(
                pageNum, pageSize, isAll, activitySeq);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 广告页数据统计
     *
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "api/layer/exportLayerPageStatistics", method = RequestMethod.GET)
    public DefaultWebRespVO exportLayerPageStatistics(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                      @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                      @RequestParam(value = "id", required = false) String id,
                                                      HttpServletRequest request, HttpServletResponse response) {
        String userName = request.getRemoteUser();
        try {
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition",
                    "attachment;filename=exportLayerPageStatisticsTitle.xlsx");
            layerActivityServiceImpl.exportLayerPageStatistics(id, pageNum, pageSize, isAll,
                    response.getOutputStream(), userName);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return DefaultWebRespVO.SUCCESS;
    }
    
    /**
     * 查询弹窗活动信息
     * @param activityId
     * @return
     */
    @RequestMapping(value = "inner/layer/getLayerActivity", method = RequestMethod.POST)
    public DefaultWebRespVO getLayerActivity(String activityId,String number) {
    	logger.info("----------evcard-rest调用已进入getLayerActivity------参数activityId: " +activityId+",参数number"+number);
        DefaultServiceRespDTO respDTO = layerActivityServiceImpl.getLayerActivity(activityId,number);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage(), respDTO.getData());
        }
        logger.info("查询弹窗活动...，返回：" + JSON.toJSONString(respDTO));
        Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
        return DefaultWebRespVO.getSuccessVO(map);
    }

    /**
     * 首页弹窗初始化
     * @param request
     * @return
     */
    @RequestMapping(value = "api/layer/init", method = RequestMethod.POST)
    public DefaultWebRespVO init(HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        DefaultServiceRespDTO respDTO = appLayerActivityServiceImpl.init();
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        vo.setMessage("提交成功");
        return vo;
    }


    /**
     * 查询短租活动列表
     * @param queryVO
     * @param request
     * @return
     */
    @RequestMapping(value = "api/layer/shortRentActivities", method = RequestMethod.POST)
    public DefaultWebRespVO getShortRentActivities(@RequestBody ShortRentActivityQueryDTO queryVO, HttpServletRequest request) {
        try{
            List<ShortRentActivityDTO> result = appLayerActivityServiceImpl.getShortRentActivities(queryVO);
            logger.error("查询短租活动列表： 参数={}, 结果={}", JSON.toJSONString(queryVO), JSON.toJSONString(result));
            return DefaultWebRespVO.getSuccessVO(result);
        }catch (Exception ex) {
            logger.error("查询短租活动列表失败", ex);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询失败");
        }
    }
}