package com.extracme.evcard.mmp.rest;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.CommonConstant;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.ChannelDetailInfoDTO;
import com.extracme.evcard.mmp.service.IChannelManagementService;
import com.extracme.evcard.mmp.service.ISecondChannelManagementService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.service.IChannelRewardActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

@RestController
@RequestMapping("inner")
public class ChannelActivityController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IChannelRewardActivityService channelRewardActivityServiceImpl;
    @Resource
    private IChannelManagementService channelManagementService;

    @Resource
    private ISecondChannelManagementService secondChannelManagementService;
    /**
     * 渠道注册奖励活动信息
     * @param activityId
     * @return
     */
    @RequestMapping(value = "getChannelRewardActivity", method = RequestMethod.POST)
    public DefaultWebRespVO getChannelRewardActivity(String activityId) {
        log.info("----------evcard-rest调用已进入getChannelRewardActivity------参数activityId: " +activityId);
        DefaultServiceRespDTO respDTO = channelRewardActivityServiceImpl.getChannelRewardActivity(activityId);
        if (respDTO.getCode() < 0) {
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage(), respDTO.getData());
        }
        log.info("查询渠道活动...，返回：" + JSON.toJSONString(respDTO));
        Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
        return DefaultWebRespVO.getSuccessVO(map);
    }

    @RequestMapping(value = "getChannelDetail", method = RequestMethod.POST)
    public DefaultWebRespVO getChannelDetail(@RequestParam(value = "appKey")  String appKey, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            ChannelDetailInfoDTO detailInfoDTO = null;
            if (StringUtils.isBlank(appKey)) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("appkey 不能为空");
            }else{
                if (appKey.startsWith(CommonConstant.SECOND_CHANNEL_PRE)) {
                    detailInfoDTO = secondChannelManagementService.getChannelDetail(appKey);
                }else{
                    detailInfoDTO =channelManagementService.getChannelDetail(appKey);
                }
                return DefaultWebRespVO.getSuccessVO(detailInfoDTO);
            }
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("获取渠道详情...");
        return vo;
    }
}