package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.membership.credit.dto.MemberPointsAccountDto;
import com.extracme.evcard.membership.credit.dto.MemberPointsAccountQueryDto;
import com.extracme.evcard.membership.credit.dto.MemberPointsHistoryDto;
import com.extracme.evcard.membership.credit.dto.MemberPointsHistoryQueryDto;
import com.extracme.evcard.membership.credit.service.IMemberPointsService;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RequestMapping("api/memberPoints")
@RestController
@ResponseBody
public class MemberPointsController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());

	@Resource
	IMemberPointsService memberPointsService;

	/**
	 * 获取会员积分变动履历
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value="queryUserHistoryPage", method={RequestMethod.POST})
	public DefaultWebRespVO queryUserHistoryPage(@RequestBody MemberPointsHistoryQueryDto queryDto){
		PageBeanDto<MemberPointsHistoryDto> pageBeanBO = null;
		try {
			//queryDto.setAuthId("101");
			pageBeanBO = memberPointsService.queryUserHistoryPage(queryDto);
			return DefaultWebRespVO.getSuccessVO(pageBeanBO);
		} catch (Exception e) {
			log.error("查询会员积分变动履历异常, authId=" + queryDto.getAuthId(), e);
			return new DefaultWebRespVO("-1", "查询记录失败");
		}
	}

	/**
	 * 获取会员积分账户信息
	 * @param queryDto
	 * @param request
	 * @return
	 */
	@RequestMapping(value="queryUserAccount", method={RequestMethod.POST})
	public DefaultWebRespVO queryUserAccount(@RequestBody MemberPointsAccountQueryDto queryDto, HttpServletRequest request){
		MemberPointsAccountDto resp = null;
		try {
			//暂时使用101测试账号
			//queryDto.setAuthId("101");
			resp = memberPointsService.queryUserAccount(queryDto);
			return DefaultWebRespVO.getSuccessVO(resp);
		} catch (Exception e) {
			log.error("查询会员积分数据异常, authId=" + queryDto.getAuthId(), e);
			return new DefaultWebRespVO("-1", "查询记录失败");
		}
	}
}
