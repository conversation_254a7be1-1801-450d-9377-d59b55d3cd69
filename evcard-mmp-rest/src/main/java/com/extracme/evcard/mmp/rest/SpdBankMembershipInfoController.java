package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.EnterpriseMembershipInfoDTO;
import com.extracme.evcard.mmp.dto.SpdBankSecretDTO;
import com.extracme.evcard.mmp.service.SpdBankMembershipInfoService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：EnterpriseMembershipInfoController
 * 类描述：浦发会员控制层
 * 创建人：sunb-孙彬
 * 创建时间：2018年1月16日上午10:36:29
 * 修改备注
 *
 * @version1.0
 */
@RestController
@RequestMapping("api")
public class SpdBankMembershipInfoController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private SpdBankMembershipInfoService spdBankMembershipInfoServiceImpl;

    /**
     * 浦发会员信息一览
     *
     * @param pageNum      页码
     * @param pageSize     每页显示条数
     * @param name         姓名
     * @param mobilePhone  手机号
     * @param signStatus   签约状态(1:已签约,0:已签约待同步,-1:已解约)
     * @param frozenStatus 押金状态(1:已冻结,0:待冻结,-1:已解冻)
     * @return
     */
    @RequestMapping(value = "spdBankMembershipInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getIspdBankMembershipInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(value = "name", required = false) String name,
                                                               @RequestParam(value = "mobilePhone", required = false) String mobilePhone, @RequestParam(value = "signStatus", required = false) Integer signStatus,
                                                               @RequestParam(value = "frozenStatus", required = false) Integer frozenStatus, @RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {
        PageBeanBO<EnterpriseMembershipInfoDTO> pageBeanBO = spdBankMembershipInfoServiceImpl.getAllspdBankMembershipInfo(pageNum, pageSize, name, mobilePhone, signStatus, frozenStatus, isAll);
        log.debug("浦发会员信息一览...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 冻结押金结果查询
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "frozenDepositResult/{driverCode}", method = RequestMethod.GET)
    public DefaultWebRespVO queryFrozenDepositResult(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.queryFrozenDepositResult(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("冻结押金结果查询...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 解冻押金结果查询
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "unfrozenDepositResult/{driverCode}", method = RequestMethod.GET)
    public DefaultWebRespVO queryUnFrozenDepositResult(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO  = spdBankMembershipInfoServiceImpl.queryUnFrozenDepositResult(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("解冻押金结果查询...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 扣款结果查询
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "decreaseDepositResult/{driverCode}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDecreaseDepositResult(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.queryDecreaseDepositResult(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("扣款结果查询...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 审核通过，同步结果
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "signCheck/{driverCode}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateSignCheck(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.updateSignCheck(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("审核通过，同步结果...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 冻结押金
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "frozenDeposit/{driverCode}", method = RequestMethod.PUT)
    public DefaultWebRespVO frozenDeposit(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.frozenDeposit(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("冻结押金...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 解约
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "cancel/{driverCode}", method = RequestMethod.PUT)
    public DefaultWebRespVO cancel(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.cancel(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("解约...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 解冻
     *
     * @param driverCode 证件号
     * @return
     */
    @RequestMapping(value = "unfrozenDeposit/{driverCode}", method = RequestMethod.PUT)
    public DefaultWebRespVO unfrozenDeposit(@PathVariable String driverCode) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.unfrozenDeposit(driverCode);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("解冻...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }


    /**
     * 显示浦发会员敏感信息
     *
     * @param authId  会员id
     * @param request
     * @return
     */
    @RequestMapping(value = "getSecretSpdBankInfo/{authId}", method = RequestMethod.GET)
    public DefaultWebRespVO getSecretSpdBankInfo(@PathVariable String authId, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = spdBankMembershipInfoServiceImpl.getSecretSpdBankInfo(authId, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        SpdBankSecretDTO secretDTO = JSON.parseObject(respDTO.getData(),SpdBankSecretDTO.class);
        return DefaultWebRespVO.getSuccessVO(secretDTO);
    }
}
