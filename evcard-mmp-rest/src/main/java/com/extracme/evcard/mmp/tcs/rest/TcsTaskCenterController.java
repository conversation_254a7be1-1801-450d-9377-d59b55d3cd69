package com.extracme.evcard.mmp.tcs.rest;

import com.extracme.evcard.mmp.service.tcs.ITcsTaskCenterService;
import com.extracme.evcard.tcs.provider.api.bo.TaskQueryBO;
import com.extracme.evcard.tcs.provider.api.dto.TaskOperateLogDTO;
import com.extracme.evcard.tcs.provider.api.dto.task.TaskListPageDTO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("api/task")
public class TcsTaskCenterController {
    @Resource
    ITcsTaskCenterService tcsTaskCenterServiceImpl;

    /**
     * 查询任务配置列表
     * @param taskQueryBO
     * @param request
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public DefaultWebRespVO queryList(@RequestBody TaskQueryBO taskQueryBO, HttpServletRequest request) {
        PageBeanBO<TaskListPageDTO> pageBean = tcsTaskCenterServiceImpl.queryList(taskQueryBO);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }

    /**
     * 查询任务操作日志
     * @param id
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "showLog", method = RequestMethod.GET)
    public DefaultWebRespVO showLog(@RequestParam("id") Long id,
                                    @RequestParam(value = "configType", defaultValue = "1")Integer configType,
                                    @RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                    @RequestParam(value = "isAll", defaultValue = "1")Integer isAll) {
        List<Long> configIds = Arrays.asList(id);
        PageBeanBO<TaskOperateLogDTO> pageBean = tcsTaskCenterServiceImpl.queryLogs(configIds, 0,
                pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }
}
