package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.dto.BfsMemberInfoDTO;
import com.extracme.evcard.mmp.service.IMembershipInfoService;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 提供给业财系统查询会员基本信息的接口
 * @date 2022/3/3 14:13
 */
@RestController
@RequestMapping(value = "inner")
public class BfsMemberInfoController {

    @Resource
    private IMembershipInfoService membershipInfoService;

    /**
     * 查询会员基本信息for bfs
     *
     * @param authId
     * @return
     */
    @RequestMapping(value = "/memberInfoForBfs", method = RequestMethod.GET)
    public DefaultWebRespVO queryMemberInfoForBfs(@RequestParam String authId) {
        BfsMemberInfoDTO bfsMemberInfoDTO = membershipInfoService.queryMemberInfoForBfs(authId);
        return DefaultWebRespVO.getSuccessVO(bfsMemberInfoDTO);
    }
}
