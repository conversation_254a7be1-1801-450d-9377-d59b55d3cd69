package com.extracme.evcard.mmp.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @description APP首页显示查询
 * @date 2020年09月21日 17:05
 */
@Data
public class SearchAppHomeConfigVo extends PageInfoVO {

    /**
     * 活动id
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 状态 不传默认全部 -1 全部 0待审核 1待生效 2已下线 3生效中 4不通过
     */
    private Integer reviewStatus;

    /**
     * 分类 1未登录 2已登录
     */
    private Integer configType;

    /**
     * 展示平台
     * 0:EVCARD 1:享道APP 2:H5 3:支付宝小程序 4:微信小程序
     */
    private Integer displayPlatform;

    /**
     * 展示产品线 1 立即用车 2 预约用车
     */
    private Integer productLine;

    /**
     * 城市id
     */
    private Integer cityId;
}
