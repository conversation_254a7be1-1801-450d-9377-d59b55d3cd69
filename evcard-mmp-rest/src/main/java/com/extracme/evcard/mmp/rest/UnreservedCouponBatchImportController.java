package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.CouponBatchImportDTO;
import com.extracme.evcard.mmp.service.UnreservedCouponBatchImportService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

@RestController
@RequestMapping("api")
public class UnreservedCouponBatchImportController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private UnreservedCouponBatchImportService unreservedCouponBatchImportService;


    /**
     *
     *
     * @description: 新增无门槛优惠券批量导入活动

     * @return:
     * @author: DongYU
     * @param :couponBatchImportDTO
     * @param :request
     */
    @RequestMapping(value = "addUnreservedCouponImport", method = RequestMethod.POST)
    public DefaultWebRespVO addBatchImportCouponLong(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
                                                     HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = unreservedCouponBatchImportService.addUnservedCouponImportActivity(couponBatchImportDTO,
                    request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.info("新增无门槛优惠券批量导入活动失败...", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("新增无门槛优惠券批量导入活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }
    /**
     *
     *
     * @description: 修改无门槛优惠券导入活动

     * @return:
     * @author: DongYU
     * @param: couponBatchImportDTO
     * @param :request
     */
    @RequestMapping(value = "updateUnservedCouponImport", method = RequestMethod.POST)
    public DefaultWebRespVO updateUnservedCouponImportActivity(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
                                                        HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = unreservedCouponBatchImportService
                    .updateUnservedCouponImportActivity(couponBatchImportDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.info("修改优惠券批量导入活动失败...", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修改优惠券批量导入活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     *
     *
     * @description:删除无门槛优惠券活动

     * @return:
     * @author: DongYU
     * @param: id 活动id
     * @param :request
     */
    @RequestMapping(value = "deleteCouponImport/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteBatchImportCouponLong(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = unreservedCouponBatchImportService.deleteUnservedCouponImportActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除无门槛优惠券批量导入活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "无门槛优惠券批量导入活动已删除");
    }


    /**
     * 开始优惠券批量导入活动 - 无门槛
     *
     * @param file
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "startBatchImportCouponUnreservedOld", method = RequestMethod.POST)
    public DefaultWebRespVO startBatchImportCouponLong(
            @RequestParam(value = "file1", required = true) CommonsMultipartFile file,
            @RequestParam(value = "id", required = true) Long id,
            @RequestParam(value = "authority", defaultValue = "000" ) String authority,HttpServletRequest request,
            HttpServletResponse response) {
        DiskFileItem fi = (DiskFileItem) file.getFileItem();
        File file1 = fi.getStoreLocation();
        String fileName = file.getOriginalFilename();
        try {
            DefaultServiceRespDTO respDTO = unreservedCouponBatchImportService.startBatchImportCouponUnreserved(id, file1, fileName,authority,
                    request, response);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }else if (respDTO.getCode() == 1){
                return new DefaultWebRespVO("1", respDTO.getMessage());
            }
        } catch (Exception e) {
            log.warn("发券异常, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
        }
        log.debug("开始无门槛优惠券批量导入活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动结束");
    }

}
