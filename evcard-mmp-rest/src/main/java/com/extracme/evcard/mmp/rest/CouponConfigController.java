package com.extracme.evcard.mmp.rest;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.bo.CouponConfigBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.CouponConfigDTO;
import com.extracme.evcard.mmp.exception.ImportException;
import com.extracme.evcard.mmp.service.ICouponConfigService;
import com.extracme.evcard.rpc.coupon.dto.CouponQuotaConfigureDto;
import com.extracme.evcard.rpc.coupon.dto.CouponQuotaLogDto;
import com.extracme.evcard.rpc.coupon.dto.QuotaLogConditionDto;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import com.github.pagehelper.Page;

@RequestMapping("api")
@RestController
@ResponseBody
public class CouponConfigController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Resource
	ICouponConfigService couponConfigService;
	/**
	 * 查询额度配置信息列表
	 * @param couponConfigBO
	 * @param request
	 * @return
	 */
	@RequestMapping(value="getCouponConfigList", method={RequestMethod.POST})
	public DefaultWebRespVO getOperateCityList(@RequestBody  CouponConfigBO couponConfigBO,HttpServletRequest request){
		List<CouponConfigDTO> list = null;
		list = couponConfigService.getCouponConfigList(couponConfigBO,request.getRemoteUser());
		Page<CouponConfigDTO> page = (Page<CouponConfigDTO>) list;
		PageBO pageBO = new PageBO();
		pageBO.setPageNum(couponConfigBO.getPageNum());
		pageBO.setPageSize(couponConfigBO.getPageSize());
        BeanCopyUtils.copyProperties(page, pageBO);
        PageBeanBO<CouponConfigDTO> pageBeanBO = new PageBeanBO<CouponConfigDTO>(pageBO, list);
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}
	
	
	@RequestMapping(value="updateSumAvailable", method={RequestMethod.PUT})
	public DefaultWebRespVO updateOperateCity(@RequestBody CouponConfigDTO couponConfigDTO,HttpServletRequest request){
		String username = request.getRemoteUser();
		try{
			DefaultServiceRespDTO result = couponConfigService.updateSumAvailable(couponConfigDTO,username);
		}catch(ImportException e){
			return new DefaultWebRespVO("-1",e.getMessage());
		}
		return DefaultWebRespVO.SUCCESS;
	}
	/**
	 * 修改额度
	 * @param couponConfigDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value="updateCouponConfig", method={RequestMethod.PUT})
	public DefaultWebRespVO updateCouponConfig(@RequestBody CouponConfigDTO couponConfigDTO,HttpServletRequest request){
		try {
			DefaultServiceRespDTO respDTO = couponConfigService.updateCouponConfig(couponConfigDTO,request);
			if (respDTO.getCode() == -1) {
			    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.warn("修改额度异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改额度失败");
		}
		return DefaultWebRespVO.SUCCESS;
	}
	
	/**
	 * 查询额度列表
	 * @param couponConfigBO
	 * @param request
	 * @return
	 */
	@RequestMapping(value="queryCouponConfigList", method={RequestMethod.POST})
	public DefaultWebRespVO queryCouponConfigList(@RequestBody  CouponConfigBO couponConfigBO,HttpServletRequest request){
		PageBeanDto<CouponQuotaConfigureDto> pageBeanBO = null;
		try {
			pageBeanBO = couponConfigService.queryCouponConfigList(couponConfigBO,request);
			return DefaultWebRespVO.getSuccessVO(pageBeanBO);
		} catch (Exception e) {
			log.warn("查询额度异常",e);
			return DefaultWebRespVO.getSuccessVO(pageBeanBO);
		}
	}
	
	/**
	 * 查询额度变更日志
	 * @param couponConfigBO
	 * @param request
	 * @return
	 */
	@RequestMapping(value="getUpdateCouponConfigLog", method={RequestMethod.POST})
	public DefaultWebRespVO getUpdateCouponConfigLog(@RequestBody  CouponConfigBO couponConfigBO,HttpServletRequest request){
		PageBeanDto<CouponQuotaLogDto> pageBeanBO = null;
		try {
			pageBeanBO = couponConfigService.getUpdateCouponConfigLog(couponConfigBO);
			return DefaultWebRespVO.getSuccessVO(pageBeanBO);
		} catch (Exception e) {
			log.warn("查询额度日志异常",e);
			return DefaultWebRespVO.getSuccessVO(pageBeanBO);
		}
	}
	
	/**
	 * 导出额度变更日志
	 * @param quotaLogConditionDto
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "exportCouponConfigLog", method = RequestMethod.GET)
	public DefaultWebRespVO exportCouponConfigLog(QuotaLogConditionDto quotaLogConditionDto,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			response.setHeader("Content-Type", "application/vnd.ms-excel");
			response.setHeader("content-disposition",
					"attachment;filename=QuotaChangeLog.xlsx");
			couponConfigService.exportCouponConfigLog(quotaLogConditionDto,response.getOutputStream(),request);
		} catch (IOException e) {
			log.warn("导出额度日志异常", e);
			return new DefaultWebRespVO("-1", "导出额度日志异常");
		}
		return DefaultWebRespVO.SUCCESS;
	}
}
