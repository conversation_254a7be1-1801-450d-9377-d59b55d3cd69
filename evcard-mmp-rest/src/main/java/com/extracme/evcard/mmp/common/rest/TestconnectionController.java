package com.extracme.evcard.mmp.common.rest;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.service.TestconnectionService;

@RestController
@ResponseBody
public class TestconnectionController {
	
	@Resource
	TestconnectionService testconnectionServiceImpl;
	
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	private static final String SUCCESS = "success";
	private static final String FAIL = "fail";
	@RequestMapping(value="testconnection",method = RequestMethod.GET)
	public void testConnection(HttpServletRequest request ,HttpServletResponse response){
		try{
			// 1 检查redis链接是否正常
			int repRD = testconnectionServiceImpl.checkRedisConnect();
	        if (repRD != 0){
	        	response.getOutputStream().write(FAIL.getBytes());
	        }
			// 2 检查数据库链接是否正常
	        int repDB = testconnectionServiceImpl.checkDBConnect();
	        if (repDB != 0){
	        	response.getOutputStream().write(FAIL.getBytes());
	        }
	        
			response.getOutputStream().write(SUCCESS.getBytes());
			
		}catch(Exception e){
			e.printStackTrace();
			logger.error("健康检查异常",e);
			try{
				response.getOutputStream().write(FAIL.getBytes());
			}catch(IOException e1){
				e1.printStackTrace();
			}
		}
		
		
	}
	
	
	
	
	
}
