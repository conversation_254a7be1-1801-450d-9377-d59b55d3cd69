package com.extracme.evcard.mmp.vo;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：UpdateAuthIdItemsVO
 * 类描述：更新用户状态VO
 * 创建：qianhao
 * 创建时间：2017年9月22日下午12:25:15
 * 修改备注
 * @version 1.0
 */
public class UpdateAuthIdItemsVO {

    /** 用户ID **/
    private String authId;

    /** 操作日志 **/
    private String operatorContent;

    /** 勾选的审核项 **/
    private String reviewItems;

    private String mobilePhone;

    private String mail;

    private String sendMsg;

    /** 审核不通过详情 **/
    private String reviewRemark;

    /** 审核不通过概要编号(1:姓名不正确，2：邮寄地址不正确，3：照片不正确) **/
    private String reviewItemIds;

    /** 审核不通过原因 **/
    private String reviewItemName;

    /** 认证状态 认证状态 0 未认证 1 未刷脸 2 已认证 */
    private String authenticationStatus;
    /** 1 ocr提取  2 手动输入*/
    private String driverLicenseInputType;

    public String getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(String authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getReviewItemIds() {
        return reviewItemIds;
    }

    public void setReviewItemIds(String reviewItemIds) {
        this.reviewItemIds = reviewItemIds;
    }

    public String getReviewItemName() {
        return reviewItemName;
    }

    public void setReviewItemName(String reviewItemName) {
        this.reviewItemName = reviewItemName;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getOperatorContent() {
        return operatorContent;
    }

    public void setOperatorContent(String operatorContent) {
        this.operatorContent = operatorContent;
    }

    public String getReviewItems() {
        return reviewItems;
    }

    public void setReviewItems(String reviewItems) {
        this.reviewItems = reviewItems;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getSendMsg() {
        return sendMsg;
    }

    public void setSendMsg(String sendMsg) {
        this.sendMsg = sendMsg;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getDriverLicenseInputType() {
        return driverLicenseInputType;
    }

    public void setDriverLicenseInputType(String driverLicenseInputType) {
        this.driverLicenseInputType = driverLicenseInputType;
    }
}
