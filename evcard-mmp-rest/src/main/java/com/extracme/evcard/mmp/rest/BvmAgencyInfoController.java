package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.dto.BvmAgencyInfoDTO;
import com.extracme.evcard.mmp.service.IAgencyService;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：BvmAgencyInfoController
 * 类描述：提供给企业用车平台调用
 * 创建人：林鑫
 * 创建时间：2018年6月27日上午10:00:00
 * 修改备注
 * @version1.0
 */
@RestController
@RequestMapping("inner")
public class BvmAgencyInfoController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    public IAgencyService agencyService;

    @RequestMapping(value = "getBvmAgencyInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getBvmAgencyInfo(@RequestParam(value = "agencyId", required = true) String agencyId){
        BvmAgencyInfoDTO bvmAgencyInfoDTO = agencyService.getBvmAgencyInfo(agencyId);
        if(bvmAgencyInfoDTO != null){
            return  DefaultWebRespVO.getSuccessVO(bvmAgencyInfoDTO);
        }else{
            return  new DefaultWebRespVO("1","获取数据失败");
        }
    }
}
