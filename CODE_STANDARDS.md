# 代码规范说明文档

## 返回类型规范

### ❌ 错误用法
在本项目中，**不要使用** `BaseResponse<T>` 作为服务层方法的返回类型。

```java
// ❌ 错误示例
public BaseResponse<String> someMethod(String input) {
    return new BaseResponse<>(0, "成功", "result");
}
```

### ✅ 正确用法
应该使用 `DefaultServiceRespDTO` 作为服务层方法的返回类型。

```java
// ✅ 正确示例
public DefaultServiceRespDTO someMethod(String input) {
    DefaultServiceRespDTO result = new DefaultServiceRespDTO(0, "成功");
    result.setData("result");
    return result;
}
```

## 具体规范说明

### 1. 服务接口层 (service-api)
- **接口返回类型**: 使用 `DefaultServiceRespDTO`
- **方法命名**: 使用动词开头，语义清晰
- **参数校验**: 在实现类中进行，不在接口中定义

```java
// 接口定义示例
public interface IExampleService {
    /**
     * 示例方法
     * @param input 输入参数
     * @return DefaultServiceRespDTO，data字段包含结果数据
     */
    DefaultServiceRespDTO exampleMethod(ExampleInput input);
}
```

### 2. 服务实现层 (service)
- **返回类型**: 统一使用 `DefaultServiceRespDTO`
- **成功返回**: 使用构造函数设置code和message，通过setData设置数据
- **失败返回**: 使用构造函数设置错误码和错误信息

```java
// 实现类示例
@Service
public class ExampleServiceImpl implements IExampleService {
    
    @Override
    public DefaultServiceRespDTO exampleMethod(ExampleInput input) {
        // 参数校验
        if (input == null) {
            return new DefaultServiceRespDTO(-1, "输入参数不能为空");
        }
        
        try {
            // 业务逻辑处理
            String result = processBusinessLogic(input);
            
            // 成功返回
            DefaultServiceRespDTO response = new DefaultServiceRespDTO(0, "操作成功");
            response.setData(result);
            return response;
            
        } catch (Exception e) {
            log.error("处理异常", e);
            return new DefaultServiceRespDTO(-1, "系统异常：" + e.getMessage());
        }
    }
}
```

### 3. 控制器层 (rest)
- **接收类型**: 接收 `DefaultServiceRespDTO`
- **返回类型**: 转换为 `DefaultWebRespVO`
- **异常处理**: 统一异常处理

```java
// 控制器示例
@RestController
public class ExampleController {
    
    @RequestMapping(value = "example", method = RequestMethod.POST)
    public DefaultWebRespVO exampleApi(@RequestBody ExampleInput input) {
        try {
            DefaultServiceRespDTO response = exampleService.exampleMethod(input);
            
            if (response.getCode() == 0) {
                return DefaultWebRespVO.getSuccessVO(response.getData(), response.getMessage());
            } else {
                return new DefaultWebRespVO(String.valueOf(response.getCode()), response.getMessage());
            }
        } catch (Exception e) {
            log.error("接口调用异常", e);
            return new DefaultWebRespVO("-1", "接口调用失败：" + e.getMessage());
        }
    }
}
```

## 常见错误和修正

### 错误1: 使用 BaseResponse<T>
```java
// ❌ 错误
public BaseResponse<String> queryData(String id) {
    return new BaseResponse<>(0, "成功", "data");
}

// ✅ 修正
public DefaultServiceRespDTO queryData(String id) {
    DefaultServiceRespDTO result = new DefaultServiceRespDTO(0, "成功");
    result.setData("data");
    return result;
}
```

### 错误2: 不一致的返回类型
```java
// ❌ 错误 - 接口和实现类返回类型不一致
public interface IExampleService {
    BaseResponse<String> method1(String input);
    DefaultServiceRespDTO method2(String input);
}

// ✅ 修正 - 统一使用 DefaultServiceRespDTO
public interface IExampleService {
    DefaultServiceRespDTO method1(String input);
    DefaultServiceRespDTO method2(String input);
}
```

### 错误3: 控制器中错误的类型转换
```java
// ❌ 错误
@RequestMapping("example")
public DefaultWebRespVO example(@RequestParam String id) {
    BaseResponse<String> response = service.queryData(id);
    return DefaultWebRespVO.getSuccessVO(response.getData());
}

// ✅ 修正
@RequestMapping("example")
public DefaultWebRespVO example(@RequestParam String id) {
    DefaultServiceRespDTO response = service.queryData(id);
    if (response.getCode() == 0) {
        return DefaultWebRespVO.getSuccessVO(response.getData(), response.getMessage());
    } else {
        return new DefaultWebRespVO(String.valueOf(response.getCode()), response.getMessage());
    }
}
```

## 检查清单

在编写或修改代码时，请检查以下项目：

### 服务层检查
- [ ] 接口方法返回类型是否为 `DefaultServiceRespDTO`
- [ ] 实现类方法返回类型是否与接口一致
- [ ] 成功时是否正确设置 code=0 和 message
- [ ] 失败时是否正确设置错误码和错误信息
- [ ] 是否通过 setData() 方法设置返回数据

### 控制器层检查
- [ ] 是否正确接收 `DefaultServiceRespDTO` 类型
- [ ] 是否正确转换为 `DefaultWebRespVO` 类型
- [ ] 是否处理了成功和失败两种情况
- [ ] 是否添加了异常处理

### 通用检查
- [ ] 是否添加了适当的日志记录
- [ ] 是否添加了参数校验
- [ ] 是否添加了方法注释
- [ ] 代码是否编译无错误

## 修改历史

### 2024-07-28
- 修复了 `IAgencyService#longRentMemberSync` 方法的返回类型问题
- 修复了 `IAgencyService#queryAgencyDiscountCode` 方法的返回类型问题
- 统一了所有相关接口和实现类的返回类型为 `DefaultServiceRespDTO`
- 更新了控制器层的类型转换逻辑

## 注意事项

1. **向后兼容性**: 修改返回类型可能影响现有调用方，需要同步更新
2. **测试覆盖**: 修改后需要更新相关的单元测试和集成测试
3. **文档更新**: 修改接口后需要同步更新API文档
4. **代码审查**: 所有涉及返回类型的修改都应该经过代码审查

## 总结

统一使用 `DefaultServiceRespDTO` 作为服务层的返回类型，可以：
- 保持代码风格的一致性
- 减少类型转换的复杂性
- 提高代码的可维护性
- 避免编译错误和运行时异常

请严格遵循此规范，避免再次出现类似的返回类型错误。
