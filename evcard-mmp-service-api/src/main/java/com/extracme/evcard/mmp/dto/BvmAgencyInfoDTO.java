package com.extracme.evcard.mmp.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by lx920 on 2018/6/22.
 * 提供给企业用车平台显示企业信息
 */
public class BvmAgencyInfoDTO {

    /** 机构ID */
    private String agencyId;

    /** 分段式折扣 1-普通 2-广铁 */
    private Integer discountRule;

    /** 机构名称*/
    private String agencyName;

    /** 联系人*/
    private String contact;

    /** 结算方式 */
    private Double payWay;

    /** 免押等级一人数*/
    private Integer firstClassNumberLimit;

    /** 实际一级免押人数*/
    private Integer level0Count;

    /** 实际一级免押人数*/
    private Integer level1Count;

    /** 免押等级二人数*/
    private Integer secondClassNumberLimit;

    /** 实际二级免押人数*/
    private Integer level2Count;

    /** 免押等级三人数*/
    private Integer thirdClassNumberLimit;

    /** 实际三级免押人数*/
    private Integer level3Count;

    /** 免押等级四人数*/
    private Integer fourthClassNumberLimit;

    /** 实际四级免押人数*/
    private Integer level4Count;

    /** 营业执照*/
    private String licenseNoImgUrl;

    /** 合同 */
    private String contractImgUrl;

    /** 税务登记证*/
    private String taxRegistrationImgUrl;

    /** 组织机构代码证*/
    private String orgCodeImgUrl;

    /** 用车阈值*/
    private Double vehicleThreshold;

    /** 企业折扣详情*/
    private DiscountRuleDTO agencyDiscountRule;

    /** 个人折扣详情*/
    private DiscountRuleDTO personalDiscoutRule;

    // 企业免押金额（新）
    private BigDecimal exemptDepositAmount;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 创建方式 1会员系统 2政企框架合同 3长租客户
     */
    private Integer createWay;

    /**
     * 长租框架合同编号
     */
    private String longRentContractId;

    /**
     * 长租框架合同名称
     */
    private String longRentContractName;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private Integer depositGuarantor;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private Integer orderPayer;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private Integer defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店
     */
    private Integer businessSource;

    public BigDecimal getExemptDepositAmount() {
        return exemptDepositAmount;
    }

    public void setExemptDepositAmount(BigDecimal exemptDepositAmount) {
        this.exemptDepositAmount = exemptDepositAmount;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getCreateWay() {
        return createWay;
    }

    public void setCreateWay(Integer createWay) {
        this.createWay = createWay;
    }

    public String getlongRentContractId() {
        return longRentContractId;
    }

    public void setlongRentContractId(String longRentContractId) {
        this.longRentContractId = longRentContractId;
    }

    public String getLongRentContractName() {
        return longRentContractName;
    }

    public void setLongRentContractName(String longRentContractName) {
        this.longRentContractName = longRentContractName;
    }

    public Integer getDepositGuarantor() {
        return depositGuarantor;
    }

    public void setDepositGuarantor(Integer depositGuarantor) {
        this.depositGuarantor = depositGuarantor;
    }

    public Integer getOrderPayer() {
        return orderPayer;
    }

    public void setOrderPayer(Integer orderPayer) {
        this.orderPayer = orderPayer;
    }

    public Integer getDefaultingParty() {
        return defaultingParty;
    }

    public void setDefaultingParty(Integer defaultingParty) {
        this.defaultingParty = defaultingParty;
    }

    public Integer getBusinessSource() {
        return businessSource;
    }

    public void setBusinessSource(Integer businessSource) {
        this.businessSource = businessSource;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public Integer getDiscountRule() {
        return discountRule;
    }

    public void setDiscountRule(Integer discountRule) {
        this.discountRule = discountRule;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Double getPayWay() {
        return payWay;
    }

    public void setPayWay(Double payWay) {
        this.payWay = payWay;
    }

    public Integer getFirstClassNumberLimit() {
        return firstClassNumberLimit;
    }

    public void setFirstClassNumberLimit(Integer firstClassNumberLimit) {
        this.firstClassNumberLimit = firstClassNumberLimit;
    }

    public Integer getSecondClassNumberLimit() {
        return secondClassNumberLimit;
    }

    public void setSecondClassNumberLimit(Integer secondClassNumberLimit) {
        this.secondClassNumberLimit = secondClassNumberLimit;
    }

    public Integer getThirdClassNumberLimit() {
        return thirdClassNumberLimit;
    }

    public void setThirdClassNumberLimit(Integer thirdClassNumberLimit) {
        this.thirdClassNumberLimit = thirdClassNumberLimit;
    }

    public Integer getFourthClassNumberLimit() {
        return fourthClassNumberLimit;
    }

    public void setFourthClassNumberLimit(Integer fourthClassNumberLimit) {
        this.fourthClassNumberLimit = fourthClassNumberLimit;
    }

    public String getLicenseNoImgUrl() {
        return licenseNoImgUrl;
    }

    public void setLicenseNoImgUrl(String licenseNoImgUrl) {
        this.licenseNoImgUrl = licenseNoImgUrl;
    }

    public String getContractImgUrl() {
        return contractImgUrl;
    }

    public void setContractImgUrl(String contractImgUrl) {
        this.contractImgUrl = contractImgUrl;
    }

    public String getTaxRegistrationImgUrl() {
        return taxRegistrationImgUrl;
    }

    public void setTaxRegistrationImgUrl(String taxRegistrationImgUrl) {
        this.taxRegistrationImgUrl = taxRegistrationImgUrl;
    }

    public String getOrgCodeImgUrl() {
        return orgCodeImgUrl;
    }

    public void setOrgCodeImgUrl(String orgCodeImgUrl) {
        this.orgCodeImgUrl = orgCodeImgUrl;
    }

    public Double getVehicleThreshold() {
        return vehicleThreshold;
    }

    public void setVehicleThreshold(Double vehicleThreshold) {
        this.vehicleThreshold = vehicleThreshold;
    }

    public DiscountRuleDTO getAgencyDiscountRule() {
        return agencyDiscountRule;
    }

    public void setAgencyDiscountRule(DiscountRuleDTO agencyDiscountRule) {
        this.agencyDiscountRule = agencyDiscountRule;
    }

    public DiscountRuleDTO getPersonalDiscoutRule() {
        return personalDiscoutRule;
    }

    public void setPersonalDiscoutRule(DiscountRuleDTO personalDiscoutRule) {
        this.personalDiscoutRule = personalDiscoutRule;
    }

    public Integer getLevel0Count() {
        return level0Count;
    }

    public void setLevel0Count(Integer level0Count) {
        this.level0Count = level0Count;
    }

    public Integer getLevel1Count() {
        return level1Count;
    }

    public void setLevel1Count(Integer level1Count) {
        this.level1Count = level1Count;
    }

    public Integer getLevel2Count() {
        return level2Count;
    }

    public void setLevel2Count(Integer level2Count) {
        this.level2Count = level2Count;
    }

    public Integer getLevel3Count() {
        return level3Count;
    }

    public void setLevel3Count(Integer level3Count) {
        this.level3Count = level3Count;
    }

    public Integer getLevel4Count() {
        return level4Count;
    }

    public void setLevel4Count(Integer level4Count) {
        this.level4Count = level4Count;
    }
}
