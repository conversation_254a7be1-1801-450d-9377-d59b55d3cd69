package com.extracme.evcard.mmp.dto;

import java.math.BigDecimal;
import java.util.Date;

public class CouponBatchImportActivityDetailDTO {
    // 活动名称
    private String activityName;

    private String templateFileUrl;

    private String templateFileName;

    // 活动ID
    private Long id;
    // 活动类型
    private Integer type;
    // 活动类型
    private String activityType;
    // 活动状态
    private String activityStatus;
    // 活动状态名称
    private String activityStatusName;
    // 发放人
    private String issuer;
    // 运营公司
    private String orgName;
    private String orgId;
    // 发券时间
    private String sendTime;
    // 发券总金额
    private String totalAmount;
    // 备注
    private String remark;
    // 活动送券配置表id
    private Long thirdActivityId;
    //折扣券金额
    private String discountTotalAmount;
    //抵用券金额
    private String voucherTotalAmount;

    /** 活动预算总金额 */
    private BigDecimal preTotalAmount ;
    /** 发券失败 1 是 0 否*/
    private Integer voucherFailure = 0;
    private Date createTime;
    /** 扣除额度的运营公司*/
    private String quotaOrgId;

    private String quotaOrgName;

    /**
     * 优惠券交易方式，0赠送(默认) 1购买
     */
    private Integer transType = 0;
    /**
     * 购买方类别，0无  1企业 2个人
     */
    private Integer couponOwner = 0;
    /**
     * 购买方id: 企业id或会员pk_id
     */
    private String ownerId;
    /**
     * 企业购买方信息
     */
    private MmpAgencyInfoDTO mmpAgency;
    /*
     *无门槛优惠券预算金额
     */
    private BigDecimal preCouponTotalAmount = new BigDecimal(0);
    /*
     *非收入无门槛券预算金额
     */
    private BigDecimal preNonRevenueCouponTotalAmount = new BigDecimal(0);

    private String smsContent;


    /**
     * 兑换码二维码图片集合 打成的zip  oss文件ur
     */
    private String qrCodeZipUrl;

    /**
     * 一码多券开关 0开闭 1打开
     */
    private Integer oneCodeDulCouponFlag;

    /**
     * 生成兑换码数量
     */
    private Integer couponCodeNum;

    /**
     * 兑换码有效期结束时间
     * 可以是 无
     */
    private Date cdkExpiresTimeDate;

    /**
     * 兑换码有效期结束时间
     * 可以是 无
     */
    private String cdkExpiresTime;

    /**
     * 模版文件类型 1=手机号 2=会员ID
     */
    private Integer templateType;

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public Integer getOneCodeDulCouponFlag() {
        return oneCodeDulCouponFlag;
    }

    public void setOneCodeDulCouponFlag(Integer oneCodeDulCouponFlag) {
        this.oneCodeDulCouponFlag = oneCodeDulCouponFlag;
    }

    public Integer getCouponCodeNum() {
        return couponCodeNum;
    }

    public void setCouponCodeNum(Integer couponCodeNum) {
        this.couponCodeNum = couponCodeNum;
    }

    public Date getCdkExpiresTimeDate() {
        return cdkExpiresTimeDate;
    }

    public void setCdkExpiresTimeDate(Date cdkExpiresTimeDate) {
        this.cdkExpiresTimeDate = cdkExpiresTimeDate;
    }

    public String getCdkExpiresTime() {
        return cdkExpiresTime;
    }

    public void setCdkExpiresTime(String cdkExpiresTime) {
        this.cdkExpiresTime = cdkExpiresTime;
    }

    public String getQrCodeZipUrl() {
        return qrCodeZipUrl;
    }

    public void setQrCodeZipUrl(String qrCodeZipUrl) {
        this.qrCodeZipUrl = qrCodeZipUrl;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public BigDecimal getPreCouponTotalAmount() {
        return preCouponTotalAmount;
    }

    public void setPreCouponTotalAmount(BigDecimal preCouponTotalAmount) {
        this.preCouponTotalAmount = preCouponTotalAmount;
    }

    public BigDecimal getPreNonRevenueCouponTotalAmount() {
        return preNonRevenueCouponTotalAmount;
    }

    public void setPreNonRevenueCouponTotalAmount(BigDecimal preNonRevenueCouponTotalAmount) {
        this.preNonRevenueCouponTotalAmount = preNonRevenueCouponTotalAmount;
    }

    public Long getThirdActivityId() {
        return thirdActivityId;
    }

    public void setThirdActivityId(Long thirdActivityId) {
        this.thirdActivityId = thirdActivityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityStatusName() {
        return activityStatusName;
    }

    public void setActivityStatusName(String activityStatusName) {
        this.activityStatusName = activityStatusName;
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDiscountTotalAmount() {
        return discountTotalAmount;
    }

    public void setDiscountTotalAmount(String discountTotalAmount) {
        this.discountTotalAmount = discountTotalAmount;
    }

    public String getVoucherTotalAmount() {
        return voucherTotalAmount;
    }

    public void setVoucherTotalAmount(String voucherTotalAmount) {
        this.voucherTotalAmount = voucherTotalAmount;
    }

    public BigDecimal getPreTotalAmount() {
        return preTotalAmount;
    }

    public void setPreTotalAmount(BigDecimal preTotalAmount) {
        this.preTotalAmount = preTotalAmount;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getVoucherFailure() {
        return voucherFailure;
    }

    public void setVoucherFailure(Integer voucherFailure) {
        this.voucherFailure = voucherFailure;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getQuotaOrgId() {
        return quotaOrgId;
    }

    public void setQuotaOrgId(String quotaOrgId) {
        this.quotaOrgId = quotaOrgId;
    }

    public String getQuotaOrgName() {
        return quotaOrgName;
    }

    public void setQuotaOrgName(String quotaOrgName) {
        this.quotaOrgName = quotaOrgName;
    }

    public Integer getTransType() { return transType; }

    public void setTransType(Integer transType) { this.transType = transType; }

    public Integer getCouponOwner() { return couponOwner; }

    public void setCouponOwner(Integer couponOwner) { this.couponOwner = couponOwner; }

    public String getOwnerId() { return ownerId; }

    public void setOwnerId(String ownerId) { this.ownerId = ownerId; }

    public MmpAgencyInfoDTO getMmpAgency() { return mmpAgency; }

    public void setMmpAgency(MmpAgencyInfoDTO mmpAgency) { this.mmpAgency = mmpAgency; }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTemplateFileUrl() {
        return templateFileUrl;
    }

    public void setTemplateFileUrl(String templateFileUrl) {
        this.templateFileUrl = templateFileUrl;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }
}
