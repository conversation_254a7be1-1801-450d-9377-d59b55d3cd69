package com.extracme.evcard.mmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 长租会员同步输入参数
 * 字段命名与 AgencyInfoSyncInput 保持一致
 */
@Data
public class LongRentMemberSyncInput implements Serializable {

    private static final long serialVersionUID = -8412874216743348645L;

    /**
     * 0:正常同步 1:oa 审批
     */
    private int type;
    /**
     * 企业名称（客户名称）- 必填
     */
    private String agencyName;

    /**
     * 企业性质：0（外部）1（内部）
     */
    private String orgProperty;

    /**
     * 联系人姓名（客户主联系人姓名）- 必填
     */
    private String contractsName;

    /**
     * 联系人手机号（客户主联系人手机号）- 必填
     */
    private String contractsMobile;

    /**
     * 联系人邮箱（客户主联系人邮箱)
     */
    private String contractEmail;

    /**
     * 客户营业证件号 - 非必填
     */
    private String licenseNo;

    /**
     * 结算方式 0.0 后付费 1.0 预付费
     */
    private Double payWay;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private Integer orderPayer;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private Integer depositGuarantor;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private Integer defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店
     */
    private Integer businessSource;

    /**
     * 失效时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 车牌限制
     */
    private String vehicleNo;

    /**
     * 每月可透支额度
     */
    private Double lineLimitMonthly;

    /**
     * 用车阈值（最大在租车辆数）
     */
    private Double maxCarNumber;

    /**
     * 免押额度
     */
    private BigDecimal exemptDepositAmount;

    /**
     * 推荐人姓名（客户所属销售姓名）
     */
    private String referrerName;

    /**
     * 推荐人手机号（客户所属销售手机号）
     */
    private String referrerMobile;

    /**
     * 创建方式 1会员系统 2政企框架合同 3长租客户
     */
    private Integer createWay;

    /**
     * 备注（客户id）
     */
    private String remark;

    /**
     * 个人折扣-折扣率
     */
    private Double personDiscountRate;

    /**
     * 个人折扣-旺季折扣率
     */
    private Double personPeakSeasonDiscountRate;
    /**
     * 受益人数
     */
    private Integer beneficiaryNumber;

    /**
     * 有效期开始时间
     */
    private Date validStartTime;

    /**
     * 有效期结束时间
     */
    private Date validEndTime;


    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
