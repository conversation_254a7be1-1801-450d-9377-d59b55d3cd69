package com.extracme.evcard.mmp.service;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * 用车规则服务接口
 */
public interface IVehicleRuleService {

    /**
     * 创建默认用车规则
     * 
     * @param agencyId 企业ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 创建结果，包含用车规则ID
     */
    BaseResponse createDefaultVehicleRule(String agencyId, Long operatorId, String operatorName);

    /**
     * 更新用车规则为默认规则
     * 
     * @param agencyId 企业ID
     * @param agencyRoleId 用车规则ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 更新结果
     */
    BaseResponse updateToDefaultVehicleRule(String agencyId, String agencyRoleId, Long operatorId, String operatorName);

    /**
     * 检查企业是否存在用车规则
     * 
     * @param agencyId 企业ID
     * @return 是否存在用车规则
     */
    boolean hasVehicleRule(String agencyId);

    /**
     * 获取企业的默认用车规则ID
     * 
     * @param agencyId 企业ID
     * @return 用车规则ID，如果不存在返回null
     */
    String getDefaultVehicleRuleId(String agencyId);
}
