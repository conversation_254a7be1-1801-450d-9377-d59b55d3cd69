package com.extracme.evcard.mmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


/**
 *项目名称：evcard-mmp-service-api
 *类名称：CreateRedeemCodeDTO
 *类描述：生成兑换码活动配置
 *创建人：sunb-孙彬
 *创建时间：2018年1月18日下午5:34:41
 *修改备注
 *@version1.0
 *
 */
@ApiModel(value="生成兑换码活动配置对象")
public class CreateRedeemCodeDTO {
    /** 活动名称 */
    @ApiModelProperty(value="活动名称", required = true)
    private String activityName;

    /** 组织机构ID */
    @ApiModelProperty(value="组织机构ID", required = true)
    private String orgId;

    /** 备注*/
    @ApiModelProperty(value="备注", required = true)
    private String remark;

    /**
     * 优惠券交易方式，0赠送(默认) 1购买
     */
    @ApiModelProperty(value="优惠券交易方式，0赠送(默认) 1购买", required = true)
    private Integer transType = 0;

    /**
     * 购买方类别，0无  1企业 2个人
     */
    @ApiModelProperty(value="购买方类别，0无  1企业 2个人", required = true)
    private Integer couponOwner = 0;

    /**
     * 购买方id: 企业id或会员pk_id
     */
    @ApiModelProperty(value="购买方id: 企业id或会员pk_id", required = true)
    private String ownerId;

    /**
     * 企业购买方信息
     */
    @ApiModelProperty(value="企业购买方信息", required = true)
    private MmpAgencyInfoDTO mmpAgency;

    //优惠券模板
    @ApiModelProperty(value="优惠券模板", required = true)
    private List<ThirdCouponModelDTO> couponModels;

    /**
     * 一码多券开关 0开闭 1打开
     */
    private Integer oneCodeDulCouponFlag;

    /**
     * 生成兑换码数量  只有oneCodeDulCouponFlag 为1 这个才有值
     */
    private Integer couponCodeNum;

    /**
     * 兑换码有效期结束时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private String cdkExpiresTime;

    public Integer getOneCodeDulCouponFlag() {
        return oneCodeDulCouponFlag;
    }

    public void setOneCodeDulCouponFlag(Integer oneCodeDulCouponFlag) {
        this.oneCodeDulCouponFlag = oneCodeDulCouponFlag;
    }

    public Integer getCouponCodeNum() {
        return couponCodeNum;
    }

    public void setCouponCodeNum(Integer couponCodeNum) {
        this.couponCodeNum = couponCodeNum;
    }

    public String getCdkExpiresTime() {
        return cdkExpiresTime;
    }

    public void setCdkExpiresTime(String cdkExpiresTime) {
        this.cdkExpiresTime = cdkExpiresTime;
    }

    public String getActivityName() {
        return activityName;
    }
    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
    public String getOrgId() {
        return orgId;
    }
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public List<ThirdCouponModelDTO> getCouponModels() {
        return couponModels;
    }
    public void setCouponModels(List<ThirdCouponModelDTO> couponModels) {
        this.couponModels = couponModels;
    }

    public Integer getTransType() {
        if(transType == null) {
            transType = 0;
        }
        return transType;
    }

    public void setTransType(Integer transType) {
        this.transType = transType;
    }

    public Integer getCouponOwner() {
        if(couponOwner == null) {
            couponOwner = 0;
        }
        return couponOwner;
    }

    public void setCouponOwner(Integer couponOwner) {
        this.couponOwner = couponOwner;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public MmpAgencyInfoDTO getMmpAgency() {
        return mmpAgency;
    }

    public void setMmpAgency(MmpAgencyInfoDTO mmpAgency) {
        this.mmpAgency = mmpAgency;
    }
}
