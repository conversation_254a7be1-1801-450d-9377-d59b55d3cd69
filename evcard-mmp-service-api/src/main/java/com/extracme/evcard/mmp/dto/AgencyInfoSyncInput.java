package com.extracme.evcard.mmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 同步合同实体类
 */
@Data
public class AgencyInfoSyncInput implements Serializable {

    private static final long serialVersionUID = 2122801538104970372L;

    /**
     * 合同状态
     * 1=已创建 2=要素审批中 3=用印待审批 4=用印审批中 5=押金待收取 6=待生效 7=生效中 8=已关闭
     */
    private int contractStatus;

    /**
     * 企业名称
     */
    private String agencyName;

    /**
     * 失效时间
     */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date expireTime;

    /**
     * 创建方式 1会员系统 2政企框架合同 3长租客户
     */
    private Byte createWay;

    /**
     * 长租框架合同编号
     *
     */
    private String longRentContractId;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private Byte depositGuarantor;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private Byte orderPayer;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private Byte defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店
     */
    private Byte businessSource;

    /**
     * 联系人姓名
     */
    private String contractsName;

    /**
     * 联系人手机号
     */
    private String contractsMobile;

    /**
     * 客户营业证件号
     *
     */
    private String licenseNo;

    /**
     * 用车阈值
     * 最大在租车辆数
     *
     */
    private int maxCarNumber;

    /**
     * 合同折扣
     */
    private Double contractDiscount;

    /**
     * 受益人数
     *
     */
    private int beneficiaryNumber;

    /**
     * 订单免押额度
     */
    private BigDecimal exemptDepositAmount;

    /**
     * 推荐人姓名
     */
    private String referrerName;

    /**
     * 推荐人手机号
     */
    private String referrerMobile;
}
