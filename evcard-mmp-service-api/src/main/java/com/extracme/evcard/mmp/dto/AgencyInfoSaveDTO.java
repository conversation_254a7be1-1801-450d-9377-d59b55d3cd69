package com.extracme.evcard.mmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by lx920 on 2018/5/17.
 */
public class AgencyInfoSaveDTO {

    /** 机构ID*/
    private String agencyId;
    /** 机构名称*/
    private String agencyName;
    /** 联系人*/
    private String contact;
    /** 企业执照*/
    private String licenseNo;
    /** 企业座机*/
    private String tel;
    /** 联系人手机号*/
    private String mobilePhone;
    /** 企业邮箱*/
    private String mail;
    /** 企业传真*/
    private String fax;
    /** 备注 */
    private String remark;
    /** 营业执照*/
    private String licenseNoImgUrl;
    /** 合同*/
    private String contractImgUrl;
    /** 税务登记证*/
    private String taxRegistrationImgUrl;
    /** 组织机构代码证*/
    private String orgCodeImgUrl;
    /** 结算方式 */
    private Double payWay;
    /** 企业性质：0（外部）1（内部） */
    private String orgProperty;
    /** 内循环*/
    private Integer insideFlag;
    /** 开始合作时间*/
    private Date cooperateStartTime;
    /** 结束合作时间*/
    private Date cooperateEndTime;
    /** 0-未开始；1-合作中；2-已暂停*/
    private Integer cooperateStatus;
    /** 用车阈值*/
    private Double vehicleThreshold;
    /** 折扣规则企业折扣ID*/
    private Long discountId;
    /** 折扣规则个人折扣ID*/
    private Long discountPersonalId;
    /**  月可透支额度限制*/
    private Double lineLimitMonthly;
    /** 优惠折扣*/
    private Integer discountRule;
    /** 免押等级一级 免押人数 */
    private Integer firstClassNumberLimit;
    /** 免押等级二级 免押人数 */
    private Integer secondClassNumberLimit;
    /** 免押等级三级 免押人数 */
    private Integer thirdClassNumberLimit;
    /** 免押等级四级 免押人数 */
    private Integer fourthClassNumberLimit;
    /** 车牌限制 **/
    private List<String> vehicleNoLimit;

    // 企业免押金额（新）
    private BigDecimal exemptDepositAmount;

    /**
     * 失效时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 创建方式 1会员系统 2政企框架合同 3长租客户
     */
    private Integer createWay;

    /**
     * 长租框架合同编号
     */
    private String longRentContractId;

    /**
     * 长租框架合同名称
     */
    private String longRentContractName;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private Integer depositGuarantor;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private Integer orderPayer;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private Integer defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店
     */
    private Integer businessSource;

    /**
     * 推荐人姓名
     */
    private String referrerName;

    /**
     * 推荐人手机号
     */
    private String referrerMobile;

    public String getReferrerName() {
        return referrerName;
    }

    public void setReferrerName(String referrerName) {
        this.referrerName = referrerName;
    }

    public String getReferrerMobile() {
        return referrerMobile;
    }

    public void setReferrerMobile(String referrerMobile) {
        this.referrerMobile = referrerMobile;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getCreateWay() {
        return createWay;
    }

    public void setCreateWay(Integer createWay) {
        this.createWay = createWay;
    }

    public String getlongRentContractId() {
        return longRentContractId;
    }

    public void setlongRentContractId(String longRentContractId) {
        this.longRentContractId = longRentContractId;
    }

    public String getLongRentContractName() {
        return longRentContractName;
    }

    public void setLongRentContractName(String longRentContractName) {
        this.longRentContractName = longRentContractName;
    }

    public Integer getDepositGuarantor() {
        return depositGuarantor;
    }

    public void setDepositGuarantor(Integer depositGuarantor) {
        this.depositGuarantor = depositGuarantor;
    }

    public Integer getOrderPayer() {
        return orderPayer;
    }

    public void setOrderPayer(Integer orderPayer) {
        this.orderPayer = orderPayer;
    }

    public Integer getDefaultingParty() {
        return defaultingParty;
    }

    public void setDefaultingParty(Integer defaultingParty) {
        this.defaultingParty = defaultingParty;
    }

    public Integer getBusinessSource() {
        return businessSource;
    }

    public void setBusinessSource(Integer businessSource) {
        this.businessSource = businessSource;
    }

    public BigDecimal getExemptDepositAmount() {
        return exemptDepositAmount;
    }

    public void setExemptDepositAmount(BigDecimal exemptDepositAmount) {
        this.exemptDepositAmount = exemptDepositAmount;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLicenseNoImgUrl() {
        return licenseNoImgUrl;
    }

    public void setLicenseNoImgUrl(String licenseNoImgUrl) {
        this.licenseNoImgUrl = licenseNoImgUrl;
    }

    public String getContractImgUrl() {
        return contractImgUrl;
    }

    public void setContractImgUrl(String contractImgUrl) {
        this.contractImgUrl = contractImgUrl;
    }

    public String getTaxRegistrationImgUrl() {
        return taxRegistrationImgUrl;
    }

    public void setTaxRegistrationImgUrl(String taxRegistrationImgUrl) {
        this.taxRegistrationImgUrl = taxRegistrationImgUrl;
    }

    public String getOrgCodeImgUrl() {
        return orgCodeImgUrl;
    }

    public void setOrgCodeImgUrl(String orgCodeImgUrl) {
        this.orgCodeImgUrl = orgCodeImgUrl;
    }

    public Double getPayWay() {
        return payWay;
    }

    public void setPayWay(Double payWay) {
        this.payWay = payWay;
    }

    public String getOrgProperty() {
        return orgProperty;
    }

    public void setOrgProperty(String orgProperty) {
        this.orgProperty = orgProperty;
    }

    public Integer getInsideFlag() {
        return insideFlag;
    }

    public void setInsideFlag(Integer insideFlag) {
        this.insideFlag = insideFlag;
    }

    public Date getCooperateStartTime() {
        return cooperateStartTime;
    }

    public void setCooperateStartTime(Date cooperateStartTime) {
        this.cooperateStartTime = cooperateStartTime;
    }

    public Date getCooperateEndTime() {
        return cooperateEndTime;
    }

    public void setCooperateEndTime(Date cooperateEndTime) {
        this.cooperateEndTime = cooperateEndTime;
    }

    public Integer getCooperateStatus() {
        return cooperateStatus;
    }

    public void setCooperateStatus(Integer cooperateStatus) {
        this.cooperateStatus = cooperateStatus;
    }

    public Double getVehicleThreshold() {
        return vehicleThreshold;
    }

    public void setVehicleThreshold(Double vehicleThreshold) {
        this.vehicleThreshold = vehicleThreshold;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public Long getDiscountPersonalId() {
        return discountPersonalId;
    }

    public void setDiscountPersonalId(Long discountPersonalId) {
        this.discountPersonalId = discountPersonalId;
    }

    public Double getLineLimitMonthly() {
        return lineLimitMonthly;
    }

    public void setLineLimitMonthly(Double lineLimitMonthly) {
        this.lineLimitMonthly = lineLimitMonthly;
    }

    public Integer getDiscountRule() {
        return discountRule;
    }

    public void setDiscountRule(Integer discountRule) {
        this.discountRule = discountRule;
    }

    public Integer getFirstClassNumberLimit() {
        return firstClassNumberLimit;
    }

    public void setFirstClassNumberLimit(Integer firstClassNumberLimit) {
        this.firstClassNumberLimit = firstClassNumberLimit;
    }

    public Integer getSecondClassNumberLimit() {
        return secondClassNumberLimit;
    }

    public void setSecondClassNumberLimit(Integer secondClassNumberLimit) {
        this.secondClassNumberLimit = secondClassNumberLimit;
    }

    public Integer getThirdClassNumberLimit() {
        return thirdClassNumberLimit;
    }

    public void setThirdClassNumberLimit(Integer thirdClassNumberLimit) {
        this.thirdClassNumberLimit = thirdClassNumberLimit;
    }

    public Integer getFourthClassNumberLimit() {
        return fourthClassNumberLimit;
    }

    public void setFourthClassNumberLimit(Integer fourthClassNumberLimit) {
        this.fourthClassNumberLimit = fourthClassNumberLimit;
    }


    public List<String> getVehicleNoLimit() {
        return vehicleNoLimit;
    }

    public void setVehicleNoLimit(List<String> vehicleNoLimit) {
        this.vehicleNoLimit = vehicleNoLimit;
    }
}
